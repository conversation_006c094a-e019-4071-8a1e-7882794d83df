# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
from dash_iconify import DashIconify

from common import id_factory

id = id_factory(__name__)
layout = dmc.Modal(
    title=dmc.Title("请选择料号申请类型", size="h4"),
    id="modal-select",
    zIndex=10000,
    opened=True,
    centered=True,
    withCloseButton=False,
    shadow="xl",
    trapFocus=False,
    children=dmc.List(
        [
            dmc.ListItem(dmc.Anchor("全新料号申请", href="/ce/pn/new/rd")),
            dmc.ListItem(dmc.Anchor("临时料号申请", href="/ce/pn/temp/rd")),
            dmc.ListItem(dmc.Anchor("客户专用料号申请", href="/ce/pn/customer/rd")),
            dmc.ListItem(dmc.Anchor("料号升级申请", href="/ce/pn/update/rd")),
            dmc.ListItem(dmc.Anchor("规格书更新申请", href="/ce/pn/spec/rd")),
            dmc.ListItem(dmc.Anchor("IC+软体组合件申请", href="/ce/pn/sw/rd")),
        ],
        size="lg",
        withPadding=True,
        type="ordered",
        spacing=10,
        icon=dmc.ThemeIcon(
            DashIconify(icon="radix-icons:check-circled", width=16),
            radius="xl",
            color="teal",
            size=24,
        ),
    ),
)
