# -*- coding: utf-8 -*-
import time
from datetime import datetime

import dash_mantine_components as dmc
import feffery_antd_components as fac
import feffery_utils_components as fuc
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, no_update

from common import (
    bom_check,
    df_add_checkcode,
    df_add_mat_category,
    df_add_packaging,
    id_factory,
    parse_search,
    read_sql,
)
from components.notice import notice
from config import BOM_DIR, styles
from utils import db

id = id_factory(__name__)

bom_title = {
    "DeltaPN": "deltapn",
    "DESCRIPTION": "des",
    "VendorName": "mfgname",
    "VendorPart": "mfgpn",
    "Source": "source",
    "DESIGN NO": "designno",
    "SMD/DIP": "packaging",
}

change_title = {
    "DESIGN NO(位置号)": "designno",
    "Change Type(add,del,change)": "change_type",
    "DeltaPN": "deltapn",
    "DESCRIPTION": "des",
    "VendorName": "mfgname",
    "VendorPart": "mfgpn",
    "SMD/DIP": "packaging",
}
bom_type_dict = {"EE": "b_ee", "ME": "b_me", "MAG": "b_mag"}

cols_change = [
    "prt_id",
    "bom_id",
    "change_type",
    "prtno",
    "designno",
    "deltapn",
    "des",
    "mfgname",
    "mfgpn",
    "checkcode",
    "packaging",
    "cat1",
    "cat2",
    "cat3",
    "source",
    "bomtype",
    "pcb_remark",
    "remark",
]

cols_bom = [
    "prt_id",
    "bom_id",
    "item_bom",
    "prtno",
    "deltapn",
    "des",
    "mfgname",
    "mfgpn",
    "designno",
    "source",
    "sourcestatus",
    "bomtype",
    "assembly",
    "remark",
    "designpart",
    "first_date",
    "status",
    "pcb_remark",
    "owner",
    "packaging",
    "cat1",
    "cat2",
    "cat3",
    "checkcode",
]

close_window = """
if (confirm("提交成功，关闭当前窗口?")) {close();}
"""


def layout(**query):
    prtno = query.get("prtno")
    prt_id = query.get("prt_id")
    bom_type = query.get("type")
    ee_disabled = True
    me_disabled = True
    mag_disabled = True
    change_disabled = True
    switch_disabled = False
    submit_disabled = False
    toola_disabled = False

    prt = db.find_one("ssp.prt", {"id": prt_id})
    if bom_type == "change":
        change_disabled = False
        switch_disabled = True
        toola_disabled = True
    else:
        if prt.get("b_ee") == "Y":
            ee_disabled = False
        if prt.get("b_me") == "Y":
            me_disabled = False
        if prt.get("b_mag") == "Y":
            mag_disabled = False
        res = db.find_one(
            "ssp.bom_record",
            {"prt_id": prt_id, "source": "change", "status": "processing"},
        )
        if res:
            return dmc.Alert(
                "当前有ChangeList在途,请处理完再上传",
                color="red",
                title="提醒:",
            )
    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Group(
                    [
                        dmc.CheckboxGroup(
                            [
                                dmc.Checkbox(
                                    label="EE",
                                    value="EE",
                                    disabled=ee_disabled,
                                ),
                                dmc.Checkbox(
                                    label="ME",
                                    value="ME",
                                    disabled=me_disabled,
                                ),
                                dmc.Checkbox(
                                    label="MAG",
                                    value="MAG",
                                    disabled=mag_disabled,
                                ),
                                dmc.Checkbox(
                                    label="CHANGE",
                                    value="change",
                                    disabled=change_disabled,
                                ),
                            ],
                            id=id("bom-type"),
                            orientation="horizontal",
                        ),
                        dmc.Divider(orientation="vertical"),
                        dmc.Switch(
                            id=id("refer"),
                            disabled=switch_disabled,
                            offLabel="不参考其他项目",
                            onLabel="参考其他项目",
                        ),
                        dmc.Divider(orientation="vertical"),
                        dmc.DatePicker(
                            label="邮件收到日期",
                            id=id("receive-date"),
                            styles=styles,
                            maxDate=datetime.now(),
                            inputFormat="YYYY-MM-DD",
                            placeholder="邮件收到日期",
                            clearable=False,
                            # dropdownPosition="top-start",
                            # value=datetime.now(),
                        ),
                        dmc.TimeInput(
                            styles=styles,
                            id=id("receive-time"),
                        ),
                        dmc.Switch(
                            id=id("toola"),
                            disabled=toola_disabled,
                            offLabel="未使用TOOLA",
                            onLabel="使用过TOOLA",
                        ),
                    ],
                    align="center",
                ),
                dmc.Divider(),
                dmc.Center(dmc.Text(prtno, weight=800, underline=True)),
                fac.AntdDraggerUpload(
                    apiUrl="/upload/bom/",
                    text="上传附件",
                    id=id("attachment"),
                    lastUploadTaskRecord={},
                    uploadId=prtno,
                    style={"display": "block"},
                    showSuccessMessage=False,
                ),
                dmc.TextInput(
                    label="请输入参考项目号",
                    id=id("refer-prtno"),
                    style={"display": "none"},
                ),
                dmc.Button("提交", id=id("submit"), disabled=submit_disabled),
                fuc.FefferyExecuteJs(id=id("js")),
            ],
            spacing=5,
        ),
        fluid=True,
    )
    return layout


@callback(
    Output("global-notice", "children"),
    Output(id("js"), "jsString"),
    Input(id("submit"), "n_clicks"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State(id("bom-type"), "value"),
    State("url", "search"),
    State("user", "data"),
    State(id("refer-prtno"), "value"),
    State(id("receive-date"), "value"),
    State(id("receive-time"), "value"),
    State(id("toola"), "checked"),
)
def upload_bom(
    n_clicks,
    attach,
    bom_type,
    search,
    user,
    refer_prtno,
    receive_date,
    receive_time,
    toola,
):
    """上传最初BOM"""
    if not n_clicks:
        raise PreventUpdate
    if not bom_type:
        return notice("请选择BOM类型", "error"), no_update
    if (not receive_date) or (not receive_time):
        return notice("请选择邮件收到日期和时间", "error"), no_update

    bomtype = ",".join(bom_type)
    url = parse_search(search)
    prtno = url.get("prtno")
    prt_id = url.get("prt_id")
    nt_name = user.get("nt_name")
    source = url.get("type")

    r_date = datetime.fromisoformat(receive_date).date()
    r_time = datetime.fromisoformat(receive_time).time()
    receive_date = f"{r_date} {r_time}"

    # ======复制的BOM不需要上传附件========

    if refer_prtno:
        res = db.find_one("ssp.bom_initial", {"prt_id": prt_id})
        if res:
            return notice(f"{prtno} BOM已存在,无法复制", "error"), no_update

        res = db.find_one("ssp.prt", {"prtno": refer_prtno})
        if not res:
            return notice("参考项目不存在", "error"), no_update

        if res.get("b_ee") == "O" or res.get("b_me") == "O" or res.get("b_mag") == "O":
            return notice(f"{refer_prtno} 未结束,无法复制", "error"), no_update

        refer_prt_id = res.get("id")
        sql = "select * from ssp.smbom where prt_id=%s"
        smbom = read_sql(sql, params=[refer_prt_id])
        smbom.columns = smbom.columns.str.lower()
        folder = BOM_DIR / prtno
        if not folder.exists():
            folder.mkdir()
        smbom.to_excel(folder / f"{prtno}.xlsx")
        smbom["prt_id"] = prt_id
        smbom["prtno"] = prtno
        smbom["source"] = "100"

        data = {
            "prtno": prtno,
            "prt_id": prt_id,
            "bomtype": bomtype,
            "status": "processing",
            "source": "bom",
            "owner1": nt_name,
            "processingmode": "1",
            "receive_date": receive_date,
            "update_date": datetime.now(),
            "designno_qty": smbom.shape[0],
            "bom_source": refer_prtno,
        }
        bom_id = db.insert("ssp.bom_record", data)

        smbom["bom_id"] = bom_id
        smbom = df_add_mat_category(smbom)

        smbom = smbom.reindex(columns=cols_bom).replace({np.nan: None, "": None})
        for i in smbom.itertuples(index=False):
            db.insert("ssp.bom_initial", i._asdict())

        data = {bom_type_dict.get(i): "O" for i in bom_type} | {"id": prt_id}
        db.update("ssp.prt", data)
    # ===========上传的BOM需要上传附件================
    else:
        if not attach:
            return notice("附件未上传", "error"), no_update

        file_name = attach.get("fileName")
        if prtno not in file_name:
            return notice("BOM文件命名有误,需带项目名", "error"), no_update

        file = BOM_DIR / attach.get("taskId") / file_name
        file = file.rename(file.with_stem(f"{file.stem}{datetime.now():%y%m%d%H%M%S}"))
        if source == "change":
            df: pd.DataFrame = pd.read_excel(file, keep_default_na=False, dtype=str)
            header = list(change_title.keys())
            header_diff = set(header) - set(df.columns)
            if header_diff:
                return (
                    notice(f"文件表头与要求不一致,正确表头为{header}", "error"),
                    no_update,
                )
            df = df.rename(columns=change_title)
            df = df.reindex(columns=change_title.values())

            df["designno"] = df["designno"].str.strip().str.upper()
            df["change_type"] = df["change_type"].str.strip().str.lower()
            df["packaging"] = df["packaging"].str.strip().str.upper()
            df["original_packaging"] = df["packaging"]

            sql = "select distinct designno,deltapn as deltapn_sm \
                from smbom where prt_id=%s"
            smbom = read_sql(sql, params=[prt_id])

            df = df.merge(smbom, on="designno", how="left")
            if (df["designno"].value_counts() > 1).any():
                return notice("位置号重复,请修改后再上传", "error"), no_update
            c1 = df["change_type"].isin(["del", "change"])
            c2 = df["deltapn_sm"].isna()
            df1 = df.loc[c1 & c2]
            if not df1.empty:
                return notice(f"{df1['designno'].tolist()}不存在", "error"), no_update

            c1 = df["change_type"] == "add"
            c2 = df["deltapn_sm"].notna()
            df1 = df.loc[c1 & c2]
            if not df1.empty:
                return notice(f"{df1['designno'].tolist()}已存在", "error"), no_update

            c1 = df["change_type"] == "change"
            c2 = df["deltapn"] == df["deltapn_sm"]
            df1 = df.loc[c1 & c2]
            if not df1.empty:
                return (
                    notice(f"{df1['designno'].tolist()}料号与原BOM一样", "error"),
                    no_update,
                )

            res = db.find_one(
                "ssp.bom_record",
                {
                    "prt_id": prt_id,
                    "source": "change",
                    "status": "processing",
                },
            )
            if res:
                return notice("当前有ChangeList在途,请处理完再上传", "error"), no_update

            df = df_add_checkcode(df)
            df = df_add_mat_category(df)
            df = df_add_packaging(df)
            c1 = df["packaging"] != df["original_packaging"]
            c2 = df["original_packaging"] != ""
            dfp = df.loc[c1 & c2]

            if not dfp.empty:
                return (
                    notice(
                        f"{dfp['designno'].tolist()}SMD/DIP与系统不一致，请确认",
                        "error",
                    ),
                    no_update,
                )

            data = {
                "prtno": prtno,
                "prt_id": prt_id,
                "bomtype": "change",
                "status": "processing",
                "source": "change",
                "owner1": nt_name,
                "processingmode": "1",
                "receive_date": receive_date,
                "update_date": datetime.now(),
                "designno_qty": df.shape[0],
            }
            bom_id = db.insert("ssp.bom_record", data)

            df["prt_id"] = prt_id
            df["bom_id"] = bom_id
            df["source"] = "100"
            df["prtno"] = prtno
            df["bomtype"] = "change"
            # df = df_add_checkcode(df)
            # df = df_add_mat_category(df)
            # df = df_add_packaging(df)h

            # =======changelist deltaPN不需要checkcode,mat_category,packaging========
            c1 = df["change_type"] == "del"
            df["deltapn"] = np.where(c1, "Delete", df["deltapn"])
            df["packaging"] = np.where(c1, "NA", df["packaging"])
            df["cat1"] = np.where(c1, "NA", df["cat1"])
            df["cat2"] = np.where(c1, "NA", df["cat2"])
            df["cat3"] = np.where(c1, "NA", df["cat3"])

            df = df.reindex(columns=cols_change).replace({"": None, np.nan: None})
            for i in df.itertuples(index=False):
                db.insert("ssp.bom_change", i._asdict())
        else:
            df = pd.read_excel(file, keep_default_na=False, dtype=str)
            header = list(bom_title.keys())
            header_diff = set(header) - set(df.columns)
            if header_diff:
                return notice(
                    f"文件表头与要求不一致,正确表头为{header}", "error"
                ), no_update
            df = df.rename(columns=bom_title)
            df = df.reindex(columns=bom_title.values())
            df["designno"] = df["designno"].str.strip().str.upper()
            df["packaging"] = df["packaging"].str.strip().str.upper()
            df["source"] = df["source"].replace(
                {"": "100", "1": "100", "100%": "100", "0%": "0"}
            )
            df["original_packaging"] = df["packaging"]
            df = df_add_checkcode(df)
            df = df_add_packaging(df)
            df = df_add_mat_category(df)
            c1 = df["packaging"] != df["original_packaging"]
            c2 = df["original_packaging"] != ""
            dfp = df.loc[c1 & c2]
            if not dfp.empty:
                return (
                    notice(
                        f"{dfp['designno'].tolist()}SMD/DIP与系统不一致，请确认",
                        "error",
                    ),
                    no_update,
                )

            data = {
                "prtno": prtno,
                "prt_id": prt_id,
                "bomtype": bomtype,
                "status": "processing",
                "source": "bom",
                "owner1": nt_name,
                "processingmode": "1",
                "receive_date": receive_date,
                "update_date": datetime.now(),
                "designno_qty": df.shape[0],
            }
            if toola:
                data.update({"bom_source": "toola"})
            bom_id = db.insert("ssp.bom_record", data)

            df["bom_id"] = bom_id
            df["prtno"] = prtno
            df["prt_id"] = prt_id
            df["bomtype"] = bomtype
            df = df.fillna("")

            df = df.reindex(columns=cols_bom).replace({"": None, np.nan: None})
            for i in df.itertuples(index=False):
                db.insert("ssp.bom_initial", i._asdict())

            data = {bom_type_dict.get(i): "O" for i in bom_type} | {"id": prt_id}
            db.update("ssp.prt", data)

    dfx = bom_check(prt_id, bom_id, source)
    sql = "update ssp.bom_record set problem_qty=%s where id=%s"
    db.execute(sql, (dfx.shape[0], bom_id))
    return notice("上传成功"), "close()"


@callback(
    Output(id("attachment"), "style"),
    Output(id("refer-prtno"), "style"),
    Input(id("refer"), "checked"),
)
def refer_checked(checked):
    """显示或隐藏参考项目输入框"""
    if checked:
        return {"display": "none"}, {"display": "block"}
    else:
        return {"display": "block"}, {"display": "none"}
