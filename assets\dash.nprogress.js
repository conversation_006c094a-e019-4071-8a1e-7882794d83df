/*! For license information please see dash.nprogress.js.LICENSE.txt */
(()=>{var n={677:(n,e,t)=>{"use strict";t.d(e,{Z:()=>a});var r=t(81),o=t.n(r),s=t(645),i=t.n(s)()(o());i.push([n.id,"/* Make clicks pass-through */\n#nprogress {\n  pointer-events: none;\n}\n\n#nprogress .bar {\n  background: var(--nprogress-color);\n\n  position: fixed;\n  z-index: 1031;\n  top: 0;\n  left: 0;\n\n  width: 100%;\n  height: 2px;\n}\n\n/* Fancy blur effect */\n#nprogress .peg {\n  display: block;\n  position: absolute;\n  right: 0px;\n  width: 100px;\n  height: 100%;\n  box-shadow: 0 0 10px var(--nprogress-color), 0 0 5px var(--nprogress-color);\n  opacity: 1.0;\n\n  -webkit-transform: rotate(3deg) translate(0px, -4px);\n      -ms-transform: rotate(3deg) translate(0px, -4px);\n          transform: rotate(3deg) translate(0px, -4px);\n}\n\n/* Remove these to get rid of the spinner */\n#nprogress .spinner {\n  display: block;\n  position: fixed;\n  z-index: 1031;\n  top: 15px;\n  right: 15px;\n}\n\n#nprogress .spinner-icon {\n  width: 18px;\n  height: 18px;\n  box-sizing: border-box;\n\n  border: solid 2px transparent;\n  border-top-color: var(--nprogress-color);\n  border-left-color: var(--nprogress-color);\n  border-radius: 50%;\n\n  -webkit-animation: nprogress-spinner 400ms linear infinite;\n          animation: nprogress-spinner 400ms linear infinite;\n}\n\n.nprogress-custom-parent {\n  overflow: hidden;\n  position: relative;\n}\n\n.nprogress-custom-parent #nprogress .spinner,\n.nprogress-custom-parent #nprogress .bar {\n  position: absolute;\n}\n\n@-webkit-keyframes nprogress-spinner {\n  0%   { -webkit-transform: rotate(0deg); }\n  100% { -webkit-transform: rotate(360deg); }\n}\n@keyframes nprogress-spinner {\n  0%   { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n",""]);const a=i},645:n=>{"use strict";n.exports=function(n){var e=[];return e.toString=function(){return this.map((function(e){var t="",r=void 0!==e[5];return e[4]&&(t+="@supports (".concat(e[4],") {")),e[2]&&(t+="@media ".concat(e[2]," {")),r&&(t+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),t+=n(e),r&&(t+="}"),e[2]&&(t+="}"),e[4]&&(t+="}"),t})).join("")},e.i=function(n,t,r,o,s){"string"==typeof n&&(n=[[null,n,void 0]]);var i={};if(r)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(i[c]=!0)}for(var u=0;u<n.length;u++){var p=[].concat(n[u]);r&&i[p[0]]||(void 0!==s&&(void 0===p[5]||(p[1]="@layer".concat(p[5].length>0?" ".concat(p[5]):""," {").concat(p[1],"}")),p[5]=s),t&&(p[2]?(p[1]="@media ".concat(p[2]," {").concat(p[1],"}"),p[2]=t):p[2]=t),o&&(p[4]?(p[1]="@supports (".concat(p[4],") {").concat(p[1],"}"),p[4]=o):p[4]="".concat(o)),e.push(p))}},e}},81:n=>{"use strict";n.exports=function(n){return n[1]}},865:function(n,e,t){var r,o;r=function(){var n,e,t={version:"0.2.0"},r=t.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function o(n,e,t){return n<e?e:n>t?t:n}function s(n){return 100*(-1+n)}t.configure=function(n){var e,t;for(e in n)void 0!==(t=n[e])&&n.hasOwnProperty(e)&&(r[e]=t);return this},t.status=null,t.set=function(n){var e=t.isStarted();n=o(n,r.minimum,1),t.status=1===n?null:n;var c=t.render(!e),u=c.querySelector(r.barSelector),p=r.speed,l=r.easing;return c.offsetWidth,i((function(e){""===r.positionUsing&&(r.positionUsing=t.getPositioningCSS()),a(u,function(n,e,t){var o;return(o="translate3d"===r.positionUsing?{transform:"translate3d("+s(n)+"%,0,0)"}:"translate"===r.positionUsing?{transform:"translate("+s(n)+"%,0)"}:{"margin-left":s(n)+"%"}).transition="all "+e+"ms "+t,o}(n,p,l)),1===n?(a(c,{transition:"none",opacity:1}),c.offsetWidth,setTimeout((function(){a(c,{transition:"all "+p+"ms linear",opacity:0}),setTimeout((function(){t.remove(),e()}),p)}),p)):setTimeout(e,p)})),this},t.isStarted=function(){return"number"==typeof t.status},t.start=function(){t.status||t.set(0);var n=function(){setTimeout((function(){t.status&&(t.trickle(),n())}),r.trickleSpeed)};return r.trickle&&n(),this},t.done=function(n){return n||t.status?t.inc(.3+.5*Math.random()).set(1):this},t.inc=function(n){var e=t.status;return e?("number"!=typeof n&&(n=(1-e)*o(Math.random()*e,.1,.95)),e=o(e+n,0,.994),t.set(e)):t.start()},t.trickle=function(){return t.inc(Math.random()*r.trickleRate)},n=0,e=0,t.promise=function(r){return r&&"resolved"!==r.state()?(0===e&&t.start(),n++,e++,r.always((function(){0==--e?(n=0,t.done()):t.set((n-e)/n)})),this):this},t.render=function(n){if(t.isRendered())return document.getElementById("nprogress");u(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=r.template;var o,i=e.querySelector(r.barSelector),c=n?"-100":s(t.status||0),p=document.querySelector(r.parent);return a(i,{transition:"all 0 linear",transform:"translate3d("+c+"%,0,0)"}),r.showSpinner||(o=e.querySelector(r.spinnerSelector))&&d(o),p!=document.body&&u(p,"nprogress-custom-parent"),p.appendChild(e),e},t.remove=function(){p(document.documentElement,"nprogress-busy"),p(document.querySelector(r.parent),"nprogress-custom-parent");var n=document.getElementById("nprogress");n&&d(n)},t.isRendered=function(){return!!document.getElementById("nprogress")},t.getPositioningCSS=function(){var n=document.body.style,e="WebkitTransform"in n?"Webkit":"MozTransform"in n?"Moz":"msTransform"in n?"ms":"OTransform"in n?"O":"";return e+"Perspective"in n?"translate3d":e+"Transform"in n?"translate":"margin"};var i=function(){var n=[];function e(){var t=n.shift();t&&t(e)}return function(t){n.push(t),1==n.length&&e()}}(),a=function(){var n=["Webkit","O","Moz","ms"],e={};function t(t){return t=t.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(n,e){return e.toUpperCase()})),e[t]||(e[t]=function(e){var t=document.body.style;if(e in t)return e;for(var r,o=n.length,s=e.charAt(0).toUpperCase()+e.slice(1);o--;)if((r=n[o]+s)in t)return r;return e}(t))}function r(n,e,r){e=t(e),n.style[e]=r}return function(n,e){var t,o,s=arguments;if(2==s.length)for(t in e)void 0!==(o=e[t])&&e.hasOwnProperty(t)&&r(n,t,o);else r(n,s[1],s[2])}}();function c(n,e){return("string"==typeof n?n:l(n)).indexOf(" "+e+" ")>=0}function u(n,e){var t=l(n),r=t+e;c(t,e)||(n.className=r.substring(1))}function p(n,e){var t,r=l(n);c(n,e)&&(t=r.replace(" "+e+" "," "),n.className=t.substring(1,t.length-1))}function l(n){return(" "+(n.className||"")+" ").replace(/\s+/gi," ")}function d(n){n&&n.parentNode&&n.parentNode.removeChild(n)}return t},void 0===(o=r.call(e,t,e,n))||(n.exports=o)},379:n=>{"use strict";var e=[];function t(n){for(var t=-1,r=0;r<e.length;r++)if(e[r].identifier===n){t=r;break}return t}function r(n,r){for(var s={},i=[],a=0;a<n.length;a++){var c=n[a],u=r.base?c[0]+r.base:c[0],p=s[u]||0,l="".concat(u," ").concat(p);s[u]=p+1;var d=t(l),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==d)e[d].references++,e[d].updater(f);else{var m=o(f,r);r.byIndex=a,e.splice(a,0,{identifier:l,updater:m,references:1})}i.push(l)}return i}function o(n,e){var t=e.domAPI(e);return t.update(n),function(e){if(e){if(e.css===n.css&&e.media===n.media&&e.sourceMap===n.sourceMap&&e.supports===n.supports&&e.layer===n.layer)return;t.update(n=e)}else t.remove()}}n.exports=function(n,o){var s=r(n=n||[],o=o||{});return function(n){n=n||[];for(var i=0;i<s.length;i++){var a=t(s[i]);e[a].references--}for(var c=r(n,o),u=0;u<s.length;u++){var p=t(s[u]);0===e[p].references&&(e[p].updater(),e.splice(p,1))}s=c}}},569:n=>{"use strict";var e={};n.exports=function(n,t){var r=function(n){if(void 0===e[n]){var t=document.querySelector(n);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(n){t=null}e[n]=t}return e[n]}(n);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(t)}},216:n=>{"use strict";n.exports=function(n){var e=document.createElement("style");return n.setAttributes(e,n.attributes),n.insert(e,n.options),e}},565:(n,e,t)=>{"use strict";n.exports=function(n){var e=t.nc;e&&n.setAttribute("nonce",e)}},795:n=>{"use strict";n.exports=function(n){var e=n.insertStyleElement(n);return{update:function(t){!function(n,e,t){var r="";t.supports&&(r+="@supports (".concat(t.supports,") {")),t.media&&(r+="@media ".concat(t.media," {"));var o=void 0!==t.layer;o&&(r+="@layer".concat(t.layer.length>0?" ".concat(t.layer):""," {")),r+=t.css,o&&(r+="}"),t.media&&(r+="}"),t.supports&&(r+="}");var s=t.sourceMap;s&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")),e.styleTagTransform(r,n,e.options)}(e,n,t)},remove:function(){!function(n){if(null===n.parentNode)return!1;n.parentNode.removeChild(n)}(e)}}}},589:n=>{"use strict";n.exports=function(n,e){if(e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}}},e={};function t(r){var o=e[r];if(void 0!==o)return o.exports;var s=e[r]={id:r,exports:{}};return n[r].call(s.exports,s,s.exports,t),s.exports}t.n=n=>{var e=n&&n.__esModule?()=>n.default:()=>n;return t.d(e,{a:e}),e},t.d=(n,e)=>{for(var r in e)t.o(e,r)&&!t.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:e[r]})},t.o=(n,e)=>Object.prototype.hasOwnProperty.call(n,e),t.nc=void 0,(()=>{"use strict";var n=t(379),e=t.n(n),r=t(795),o=t.n(r),s=t(569),i=t.n(s),a=t(565),c=t.n(a),u=t(216),p=t.n(u),l=t(589),d=t.n(l),f=t(677),m={};m.styleTagTransform=d(),m.setAttributes=c(),m.insert=i().bind(null,"head"),m.domAPI=o(),m.insertStyleElement=p(),e()(f.Z,m),f.Z&&f.Z.locals&&f.Z.locals;var v=t(865),g=t.n(v);!function(){const n=n=>window.getComputedStyle(document.body).getPropertyValue(`--nprogress-color-${n}`),e=n=>{document.querySelector(":root").style.setProperty("--nprogress-color",n)},t=n("good")||"#51CF66",r=n("bad")||"#FF6B6B";e(t),g().configure({easing:"ease",speed:500}),window.intercept=0;const{fetch:o}=window;window.fetch=async(...n)=>{let[s,i]=n;0===window.intercept?(e(t),g().start()):g().inc(.05),window.intercept++;const a=await o(s,i);return a.status>=400&&e(r),window.intercept--,0===window.intercept&&g().done(),a}}()})()})();