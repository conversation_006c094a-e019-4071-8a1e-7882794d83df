# -*- coding: utf-8 -*-
import re
from pathlib import Path

import duckdb as dk
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import ALL, Input, Output, State, callback, dash, no_update

from common import read_sql
from components.notice import notice
from config import UPLOAD_FOLDER_ROOT

from . import layout

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/bom-compare", title="BOM比对")

header = [
    "parent_level",
    "deltapn",
    "des",
    "mfgname",
    "mfgpn",
    "alt",
    "grp",
    "qpa",
    "um",
    "design_no",
    "item_text",
]
bom_type_map = {
    ("designator", "footprint", "value", "delta pn"): {
        "type": "pcad",
        "rename": {
            "designator": "design_no",
            "delta pn": "deltapn",
        },
    },
    (
        "item",
        "level",
        "part no",
        "l/f",
        "description",
        "mfg part",
        "mfg code",
        "mfg title",
        "ag",
        "%",
        "qpa",
        "un",
        "design no",
        "long text",
    ): {
        "type": "yp36",
        "rename": {
            "part no": "deltapn",
            "description": "des",
            "mfg part": "mfgpn",
            "mfg title": "mfgname",
            "ag": "alt",
            "%": "grp",
            "un": "um",
            "design no": "design_no",
            "long text": "item_text",
        },
    },
    (
        "item",
        "f",
        "parent level p/n",
        "level",
        "part no",
        "create date",
        "group no",
        "status",
        "vendor name",
        "mfg part",
        "l/f",
        "description",
        "alt",
        "%",
        "qpa",
        "um",
        "design no",
        "item text",
        "lead time",
    ): {
        "type": "ypan",
        "rename": {
            "parent level p/n": "parent_level",
            "part no": "deltapn",
            "vendor name": "mfgname",
            "mfg part": "mfgpn",
            "description": "des",
            "%": "grp",
            "design no": "design_no",
            "item text": "item_text",
        },
    },
    (
        "deltapn\n台达料号",
        "description\n描述",
        "mfgname\n厂商名称",
        "mfgpart\n厂商料号",
        "grp\n主次source区分",
        "desginno \n位置号",
        "pcb remark\n板号",
        "rd remark\nrd备注",
        "alt",
        "备注",
    ): {
        "type": "toolb",
        "rename": {
            "deltapn\n台达料号": "deltapn",
            "description\n描述": "des",
            "mfgname\n厂商名称": "mfgname",
            "mfgpart\n厂商料号": "mfgpn",
            "grp\n主次source区分": "grp",
            "desginno \n位置号": "design_no",
        },
    },
    (
        "deltapn\n台达料号",
        "description\n描述",
        "mfgname\n厂商名称",
        "mfgpart\n厂商料号",
        "grp\n主次source区分",
        "desginno \n位置号",
        "pcb remark\n板号",
        "rd remark\nrd备注",
        "alt",
        "备注",
    ): {
        "type": "toolb",
        "rename": {
            "deltapn\n台达料号": "deltapn",
            "description\n描述": "des",
            "mfgname\n厂商名称": "mfgname",
            "mfgpart\n厂商料号": "mfgpn",
            "grp\n主次source区分": "grp",
            "desginno \n位置号": "design_no",
        },
    },
    (
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "source",
        "designno",
        "designpart",
        "pcb_remark",
        "dip/smd",
    ): {
        "type": "smbom_ce",
        "rename": {"designno": "design_no", "source": "grp"},
    },
    (
        "project",
        "stage",
        "prtno",
        "board",
        "pcbpn",
        "designno",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "packaging",
    ): {
        "type": "smbom_rd",
        "rename": {"designno": "design_no"},
    },
    (
        "deltapn\n台达料号",
        "description\n描述",
        "mfg name\n厂商名称",
        "mfg part\n厂商料号",
        "%\n主次source区分",
        "desgin no \n位置号",
        "remark\nrd备注",
    ): {
        "type": "normal",
        "rename": {
            "deltapn\n台达料号": "deltapn",
            "description\n描述": "des",
            "mfg name\n厂商名称": "mfgname",
            "mfg part\n厂商料号": "mfgpn",
            "%\n主次source区分": "grp",
            "desgin no \n位置号": "design_no",
        },
    },
}


@callback(
    Output(id({"type": "skeleton", "index": ALL}), "visible"),
    Input(id("tab1_select1"), "value"),
)
def tab1_upload_visible(value):
    if value:
        return [False, False, False]
    else:
        return [True, True, True]


@callback(
    Output(id("notice"), "children"),
    Output(id("tab1_table"), "data"),
    Input(id("file1"), "lastUploadTaskRecord"),
    State(id("tab1_select1"), "value"),
    State(id("tab1_select2"), "value"),
)
def file1_upload(record, source, exclusion):
    file = UPLOAD_FOLDER_ROOT / record.get("taskId") / record.get("fileName")

    if not file.exists():
        raise PreventUpdate

    df1 = bom_self_check(file, source, exclusion)

    if df1.empty:
        return notice("手选Source类型与逻辑判断不一致,请修改", "error"), no_update
    else:
        return no_update, df1.to_dict(orient="records")


@callback(
    Output(id("tab2_table"), "data"),
    Input(id("file2"), "lastUploadTaskRecord"),
    Input(id("file3"), "lastUploadTaskRecord"),
    State(id("tab1_select1"), "value"),
)
def file2_file3_upload(record1, record2, source):
    if (record1 is None) or (record2 is None):
        raise PreventUpdate

    file1 = UPLOAD_FOLDER_ROOT / record1.get("taskId") / record1.get("fileName")
    file2 = UPLOAD_FOLDER_ROOT / record2.get("taskId") / record2.get("fileName")

    if not all([file1.exists(), file2.exists()]):
        raise PreventUpdate

    df1 = bom_file_load(file1, source)
    df2 = bom_file_load(file2, source)
    df1["qpa"] = df1["qpa"].replace({np.nan: 1, "": 1})
    df2["qpa"] = df2["qpa"].replace({np.nan: 1, "": 1})

    df1 = df1.fillna("")
    df2 = df2.fillna("")

    grp1 = df1.groupby(["design_no"], as_index=False).agg(
        {
            "index": set,
            "deltapn": set,
            "des": set,
            "mfgname": set,
            "mfgpn": set,
            "grp": set,
            "qpa": set,
            "um": set,
            "item_text": set,
        }
    )

    grp2 = df2.groupby(["design_no"], as_index=False).agg(
        {
            "index": set,
            "deltapn": set,
            "des": set,
            "mfgname": set,
            "mfgpn": set,
            "grp": set,
            "qpa": set,
            "um": set,
            "item_text": set,
        }
    )

    df = grp1.merge(grp2, on="design_no", how="outer")
    df = df.loc[df["design_no"] != ""]

    c1 = df["deltapn_x"] != df["deltapn_y"]
    df["error"] = np.where(c1, "DELTAPN unmatch error", "")

    c1 = df["error"] == ""
    c2 = df["des_x"] != df["des_y"]
    df["error"] = np.where(c1 & c2, "DES unmatch error", df["error"])

    # c1 = df["error"] == ""
    # c2 = df["mfgname_x"] != df["mfgname_y"]
    # df["error"] = np.where(c1 & c2, "MFGNAME unmatch error", df["error"])

    c1 = df["error"] == ""
    c2 = df["mfgpn_x"] != df["mfgpn_y"]
    df["error"] = np.where(c1 & c2, "MFGPN unmatch error", df["error"])

    c1 = df["error"] == ""
    c2 = df["grp_x"] != df["grp_y"]
    df["error"] = np.where(c1 & c2, "GRP unmatch error", df["error"])

    c1 = df["error"] == ""
    c2 = df["qpa_x"] != df["qpa_y"]
    df["error"] = np.where(c1 & c2, "QPA unmatch error", df["error"])

    c1 = df["error"] == ""
    c2 = df["um_x"] != df["um_y"]
    df["error"] = np.where(c1 & c2, "UM unmatch error", df["error"])

    c1 = df["error"] == ""
    c2 = df["item_text_x"] != df["item_text_y"]
    df["error"] = np.where(c1 & c2, "ITEM TEXT unmatch error", df["error"])

    df["index_x"] = df["index_x"].fillna("").apply(set)
    df["index_y"] = df["index_y"].fillna("").apply(set)
    c1 = df["index_x"] == set()
    df["error"] = np.where(c1, f"Only {file2.stem} bom show", df["error"])

    c1 = df["index_y"] == set()
    df["error"] = np.where(c1, f"Only {file1.stem} bom show", df["error"])

    c1 = df["design_no"] == ""
    df["error"] = np.where(c1, f"Only {file1.stem} bom show", df["error"])

    # df11 = df1.loc[
    #     (df1["design_no"] == "") & (df1["deltapn"].str.startswith(("3", "4")))
    # ]
    # df21 = df2.loc[
    #     (df2["design_no"] == "") & (df2["deltapn"].str.startswith(("3", "4")))
    # ]

    # df11["qpa"] = df11["qpa"].apply(lambda x: f"{float(x):g}")
    # df21["qpa"] = df21["qpa"].apply(lambda x: f"{float(x):g}")

    # grp11 = df11.groupby("deltapn", as_index=False).agg(
    #     count=("deltapn", "count"),
    #     index=("index", set),
    #     qpa=("qpa", set),
    #     grp=("grp", set),
    # )

    # grp21 = df21.groupby("deltapn", as_index=False).agg(
    #     count=("deltapn", "count"),
    #     index=("index", set),
    #     qpa=("qpa", set),
    #     grp=("grp", set),
    # )

    # dfx = grp11.merge(grp21, on="deltapn", how="outer")
    # dfx["error"] = ""
    # c1 = dfx["index_x"].notna()
    # c2 = dfx["index_y"].isna()
    # dfx["error"] = np.where(c1 & c2, f"Only {file1.stem} bom show", dfx["error"])

    # c1 = dfx["index_x"].isna()
    # c2 = dfx["index_y"].notna()
    # dfx["error"] = np.where(c1 & c2, f"Only {file2.stem} bom show", dfx["error"])

    df11 = df1.loc[df1["design_no"] == ""]
    df21 = df2.loc[df2["design_no"] == ""]
    # breakpoint()

    df11["qpa"] = df11["qpa"].apply(lambda x: f"{float(x):g}")
    df21["qpa"] = df21["qpa"].apply(lambda x: f"{float(x):g}")

    grp11 = df11.groupby("deltapn", as_index=False).agg(
        count=("deltapn", "count"),
        index=("index", set),
        qpa=("qpa", set),
        grp=("grp", set),
    )

    grp21 = df21.groupby("deltapn", as_index=False).agg(
        count=("deltapn", "count"),
        index=("index", set),
        qpa=("qpa", set),
        grp=("grp", set),
    )

    dfx = grp11.merge(grp21, on="deltapn", how="outer")
    dfx["error"] = [set()] * dfx.shape[0]
    dfx["index_x"] = dfx["index_x"].fillna("").apply(set)
    dfx["index_y"] = dfx["index_y"].fillna("").apply(set)

    c1 = (dfx["index_x"] - dfx["index_y"]) != set()
    # c3=dfx["deltapn"].str.startswith(("3", "4"))
    dfx["error"] = np.where(
        c1,
        dfx["error"].apply(lambda x: x | {f"Only {file1.stem} bom show"}),
        dfx["error"],
    )

    c1 = (dfx["index_y"] - dfx["index_x"]) != set()
    # c3=dfx["deltapn"].str.startswith(("3", "4"))
    dfx["error"] = np.where(
        c1,
        dfx["error"].apply(lambda x: x | {f"Only {file2.stem} bom show"}),
        dfx["error"],
    )

    c1 = dfx["index_x"].notna()
    c2 = dfx["index_y"].notna()
    c3 = dfx["qpa_x"] != dfx["qpa_y"]
    dfx["error"] = np.where(
        c1 & c2 & c3,
        dfx["error"].apply(lambda x: x | {"QPA unmatch error"}),
        dfx["error"],
    )

    c1 = dfx["index_x"].notna()
    c2 = dfx["index_y"].notna()
    c3 = dfx["grp_x"] != dfx["grp_y"]
    dfx["error"] = np.where(
        c1 & c2 & c3,
        dfx["error"].apply(lambda x: x | {"GRP unmatch error"}),
        dfx["error"],
    )

    df = df.loc[df["error"] != ""]
    data = []
    for i in df.itertuples():
        df1i = df1.loc[df1["index"].isin(i.index_x)]
        # df1i["error"] = i.error
        df2i = df2.loc[df2["index"].isin(i.index_y)]
        # df2i["error"] = i.error
        dfc = pd.concat([df1i, df2i])
        dfi = {
            "error": i.error,
            "design_no": i.design_no,
            "_children": dfc.to_dict(orient="records"),
        }
        data.append(dfi)

    dfx = dfx.explode("error")
    dfx = dfx.where(dfx.notna(), {})
    dfx["design_no"] = ""
    dfx = dfx.loc[dfx["error"] != {}]
    for i in dfx.itertuples():
        df1i = df1.loc[df1["index"].isin(i.index_x)]
        df2i = df2.loc[df2["index"].isin(i.index_y)]
        dfc = pd.concat([df1i, df2i])
        dfi = {
            "error": i.error,
            "design_no": i.design_no,
            "_children": dfc.to_dict(orient="records"),
        }
        data.append(dfi)

    return data


def bom_file_load(file: Path, source: str = "") -> pd.DataFrame:
    """
    1.读取文件
    2.重命名表头
    3.判断source是single还是multi
    """
    df = pd.read_excel(file, dtype=str, keep_default_na=False)
    df.columns = df.columns.str.lower()
    head = tuple(df.columns)
    df = df.rename(columns=bom_type_map.get(head, {}).get("rename", {}))
    df = df.reindex(columns=header)
    df = df.reset_index()
    df["bom_name"] = file.name
    df["parent_level"] = df["parent_level"].fillna("")
    bom_type = bom_type_map.get(head, {}).get("type")
    if bom_type == "ypan":
        c1 = df["parent_level"].str.contains("^[A-Za-z]")
        c2 = df["parent_level"].str.startswith(("55", "38"))
        df = df.loc[c1 | c2]

    design_no_max_count = df.groupby("design_no").design_no.count().max()
    if design_no_max_count > 1:
        df["source"] = "multiple"
    else:
        df["source"] = "single"

    return df


def bom_self_check(
    file: Path, source: str = "", exclusion: list = None
) -> pd.DataFrame:
    """
    1.读取文件
    2.重命名表头
    3.判断source是single还是multi
    4.BOM自检逻辑
    """
    exclusion = exclusion or []
    df = pd.read_excel(file, dtype=str, keep_default_na=False)
    df.columns = df.columns.str.lower()
    head = tuple(df.columns)
    df = df.rename(columns=bom_type_map.get(head, {}).get("rename", {}))
    df = df.reindex(columns=header)
    df = df.reset_index()
    df["bom_name"] = file.name
    df["parent_level"] = df["parent_level"].fillna("")

    bom_type = bom_type_map.get(head, {}).get("type")
    if bom_type == "ypan":
        c1 = df["parent_level"].str.contains("^[A-Za-z]")
        c2 = df["parent_level"].str.startswith(("55", "38"))
        df = df.loc[c1 | c2]

    design_no_max_count = (
        df["design_no"].loc[df["design_no"] != ""].value_counts().max()
    )
    if design_no_max_count > 1:
        logic_source = "multiple"
    else:
        logic_source = "single"

    if logic_source != source:
        return pd.DataFrame()
    else:
        df["source"] = source

    params = df["deltapn"].unique().tolist()
    ph = ",".join(["%s"] * len(params))

    sql = f"select distinct deltapn as deltapn_csg,des as des_csg,\
    mfgname as mfgname_csg,mfgpn as mfgpn_csg from ssp_csg.csg where deltapn in ({ph})"
    dfm = read_sql(sql, params=params)
    dfm = dfm.drop_duplicates("deltapn_csg")

    sql = f"select deltapn,original_des from ssp_br.z_basicdata_koagp \
    where deltapn in ({ph})"
    koa = read_sql(sql, params=params)
    koa = koa.drop_duplicates("deltapn")

    sql = "SELECT pn_like,des_like,des_not_like,backup1,category as cat1,r_mgroup,\
    r_packagecheck,r_sequence FROM ssp_br.r_subgrouprule order by R_sequence"
    sr = read_sql(sql)
    sr = sr.loc[sr["r_packagecheck"] == "Y"]

    sr[["pn_like", "des_like", "des_not_like"]] = sr[
        ["pn_like", "des_like", "des_not_like"]
    ].applymap(like_to_regex)
    sr["des_not_like"] = "~" + sr["des_not_like"]
    sr["cat1"] = sr["cat1"].str.replace("*", "", regex=False)

    sql = "SELECT package,group_filter,packagegroup,category as cat2\
        from ssp_br.r_multifilter_cr_package order by PackageGroup,ID"
    pr = read_sql(sql)

    df1 = df.merge(dfm, left_on="deltapn", right_on="deltapn_csg", how="left").merge(
        koa, on="deltapn", how="left"
    )

    df1["error"] = [set()] * df1.shape[0]
    single_source = df1["source"] == "single"
    multiple_source = df1["source"] == "multiple"

    # ====DESIGN NO. Blank
    c1 = df1["deltapn"].str.startswith(("0", "1", "2", "48"))
    c2 = df1["design_no"].isin(["", np.nan])
    c3 = bom_type in ("smbom_ce", "smbom_rd", "normal")
    df1["error"] = np.where(
        c1 & c2, df1["error"].apply(lambda x: x | {"DESIGN NO. Blank"}), df1["error"]
    )

    df1["error"] = np.where(
        c2 & c3, df1["error"].apply(lambda x: x | {"DESIGN NO. Blank"}), df1["error"]
    )

    design_no_not_blank = ~c2

    # ===Package unmatch error

    grp1 = df1.groupby(["parent_level", "design_no"])

    sql = "select * from dfi cross join sr cross join pr \
        where regexp_full_match(deltapn,pn_like) \
            and regexp_full_match(des,des_like) \
            and not regexp_full_match(des,des_not_like) \
            and contains(cat2,cat1) \
            and regexp_matches(des,'\s'||package||'\W')"

    for _, dfi in grp1:
        if dfi["deltapn"].unique().size > 1:
            dfx = dk.execute(sql).df()
            if not dfx.empty:
                if dfx["packagegroup"].unique().size > 1:
                    df1["error"] = np.where(
                        df1["index"].isin(dfx["index"]),
                        df1["error"].apply(lambda x: x | {"Package unmatch error"}),
                        df1["error"],
                    )

    # ======Information Unmatch

    c0 = df1["deltapn_csg"].notna()
    c1 = df1["des"] != df1["des_csg"]
    # c2 = df1["mfgname"] != df1["mfgname_csg"]厂商名称不需要与csg匹配
    c3 = df1["mfgpn"] != df1["mfgpn_csg"]
    # 机构料（3开头或者英文字母开头的临时料号；4开头且非48开头材料）不需要进行信息匹配检查
    c4 = df1["deltapn"].str.startswith(("0", "1", "2", "48"))

    df1["error"] = np.where(
        c0 & (c1 | c3) & c4,
        df1["error"].apply(lambda x: x | {"Information Unmatch"}),
        df1["error"],
    )

    c1 = df1["deltapn"].isin(["", np.nan])
    df1["error"] = np.where(
        c1, df1["error"].apply(lambda x: x | {"PN BLANK"}), df1["error"]
    )

    c1 = df1.groupby(["parent_level", "design_no"])["design_no"].transform("count") > 1
    df1["error"] = np.where(
        design_no_not_blank & c1 & single_source,
        df1["error"].apply(lambda x: x | {"DESIGN NO. REPEAT"}),
        df1["error"],
    )

    c1 = df1.groupby(["design_no"])["parent_level"].transform(lambda x: len(set(x))) > 1
    c2 = ~df1["design_no"].isin(["", np.nan])
    df1["error"] = np.where(
        c1 & c2,
        df1["error"].apply(
            lambda x: x | {"Different parent level design NO.repeat error"}
        ),
        df1["error"],
    )

    c1 = df1.groupby(["design_no", "deltapn"])["deltapn"].transform("count") > 1
    c2 = ~df1["design_no"].isin(["", np.nan])
    df1["error"] = np.where(
        c1 & c2, df1["error"].apply(lambda x: x | {"PN REPEAT"}), df1["error"]
    )

    df1["original_des"] = np.where(
        df1["original_des"].notna(), df1["original_des"], df1["des_csg"]
    )  # KOA的描述转换

    df1["type"] = np.where(df1["original_des"].str.contains("\s?CAP\s"), "CAP", None)
    df1["type"] = np.where(
        df1["original_des"].str.contains("\s?RES\s"), "RES", df1["type"]
    )

    df1["des_res_cap"] = df1["original_des"].str.split(
        "RES|CAP", n=1, expand=True, regex=True
    )[1]  # 删除RES|CAP之前的字符

    df1["des_res_cap"] = df1["des_res_cap"].str.strip().fillna("")

    df1["des_res_cap"] = df1["des_res_cap"].str.replace(
        "\sCS.*?\s", " ", regex=True
    )  # cs电阻不作为差异

    def unit_convert(x: str):
        if not x:
            return x

        if "Kohm" in x:
            x = re.sub("(\S+)Kohm", lambda v: f"{eval(f'{v.group(1)}*1000'):g}ohm", x)

        elif "kohm" in x:
            x = re.sub("(\S+)kohm", lambda v: f"{eval(f'{v.group(1)}*1000'):g}ohm", x)

        elif "mohm" in x:
            x = re.sub("(\S+)mohm", lambda v: f"{eval(f'{v.group(1)}*0.001'):g}ohm", x)

        elif "Mohm" in x:
            x = re.sub(
                "(\S+)mohm", lambda v: f"{eval(f'{v.group(1)}*1000000'):g}ohm", x
            )

        elif "Mohm" in x:
            x = re.sub(
                "(\S+)mohm", lambda v: f"{eval(f'{v.group(1)}*1000000'):g}ohm", x
            )
        elif re.search("(?<=\d)PF(?=\s)", x):
            x = re.sub("(?<=\d)PF(?=\s)", "pF", x)

        elif re.search("(?<=\d)UF(?=\s)", x):
            x = re.sub("(?<=\d)UF(?=\s)", "uF", x)

        return x

    df1["des_res_cap"] = df1["des_res_cap"].apply(unit_convert)
    df1["cap_height"] = df1["des_res_cap"].str.rsplit(" ", n=1, expand=True)[1]
    df1["cap_height"] = np.where(df1["type"] == "CAP", df1["cap_height"], None)
    df1["cap_height"] = pd.to_numeric(df1["cap_height"], errors="coerce")

    if "temperature" in exclusion:
        # 15开头温度特性的差异X7R,X7S不作为差异
        df1["des_res_cap"] = np.where(
            df1["deltapn"].str.startswith("15"),
            df1["des_res_cap"].str.replace("X7R|X7S", "", regex=True),
            df1["des_res_cap"],
        )

    if "height" in exclusion:
        # 15电容封装之后的内容(高度)不作为差异
        df1["des_res_cap"] = np.where(
            df1["deltapn"].str.startswith("15"),
            df1["des_res_cap"].str.replace("(?<=\s)\d+\.?\d*(?=$)", "", regex=True),
            df1["des_res_cap"],
        )

    if "antis" in exclusion:
        # 抗硫不作为差异
        df1["des_res_cap"] = np.where(
            df1["deltapn"].str.startswith("03"),
            df1["des_res_cap"].str.replace("ANTI-S\s", "", regex=True),
            df1["des_res_cap"],
        )

    # 材料的高度,最大值减去最小值小于0.2的时候，都不作为差异
    c1 = df1["deltapn"].str.startswith("15")
    c2 = (
        df1.groupby(["parent_level", "design_no"])["cap_height"].transform(
            lambda x: max(x) - min(x)
        )
        < 0.2
    )

    df1["des_res_cap"] = np.where(
        c1 & c2,
        df1["des_res_cap"].str.replace("(?<=\s)\d+\.?\d*(?=$)", "", regex=True),
        df1["des_res_cap"],
    )
    # breakpoint()

    c1 = (
        df1.groupby(["parent_level", "design_no"])["des_res_cap"].transform(
            lambda x: len(set(x))
        )
        > 1
    )

    df1["error"] = np.where(
        design_no_not_blank & multiple_source & c1,
        df1["error"].apply(lambda x: x | {"Description unmatch ERROR"}),
        df1["error"],
    )

    # grp error
    df1["grp"] = df1["grp"].fillna("").astype(str).str.replace("%", "")
    df1["design_no_count"] = df1.groupby(["parent_level", "design_no"])[
        "design_no"
    ].transform("count")

    df1["design_no_count"] = np.where(df1["design_no"] == "", 0, df1["design_no_count"])

    c2 = df1["grp"] != ""
    df1["error"] = np.where(
        single_source & c2,
        df1["error"].apply(lambda x: x | {"GRP ERROR"}),
        df1["error"],
    )

    c1 = (
        df1.groupby(["parent_level", "design_no"])["grp"].transform(
            lambda x: x.value_counts().to_dict().get("100", 0)
        )
        != 1
    )
    c2 = df1["design_no_count"] > 1
    # breakpoint()

    df1["error"] = np.where(
        c1 & c2 & multiple_source,
        df1["error"].apply(lambda x: x | {"GRP ERROR"}),
        df1["error"],
    )

    c1 = df1["design_no_count"] == 1
    c2 = df1["grp"] != ""
    df1["error"] = np.where(
        design_no_not_blank & c1 & c2 & multiple_source,
        df1["error"].apply(lambda x: x | {"GRP ERROR"}),
        df1["error"],
    )

    # =========ALT ERROR
    df1["alt"] = df1["alt"].fillna("").astype(str)
    c2 = df1["alt"] != ""
    df1["error"] = np.where(
        design_no_not_blank & single_source & c2,
        df1["error"].apply(lambda x: x | {"ALT ERROR"}),
        df1["error"],
    )

    c1 = df1["design_no_count"] == 1
    c2 = df1["alt"] != ""
    df1["error"] = np.where(
        design_no_not_blank & c1 & c2 & multiple_source,
        df1["error"].apply(lambda x: x | {"ALT ERROR"}),
        df1["error"],
    )

    c1 = df1["design_no_count"] > 1
    c2 = df1["alt"] == ""
    df1["error"] = np.where(
        design_no_not_blank & c1 & c2 & multiple_source,
        df1["error"].apply(lambda x: x | {"ALT ERROR"}),
        df1["error"],
    )

    c1 = (
        df1.groupby(["parent_level", "design_no"])["alt"].transform(
            lambda x: x.unique().size
        )
        > 1
    )
    c2 = df1["alt"] != ""
    # breakpoint()
    df1["error"] = np.where(
        design_no_not_blank & c1 & c2 & multiple_source,
        df1["error"].apply(lambda x: x | {"ALT ERROR"}),
        df1["error"],
    )

    df1 = df1.explode("error")
    df1 = df1.dropna(subset=["error"])

    df1 = df1.sort_values(by=["design_no"])

    return df1


def pcad(df):
    df = df.join(df["des"].str.split("/", expand=True))
    df["type"] = "semi"
    df["type"] = np.where(df["design_no"].str.startswith("C"), "cap", df["type"])
    df["type"] = np.where(df["design_no"].str.startswith("R"), "res", df["type"])
    df = df.join(df[1].str.extract(r"(?P<vol>[\d\.]+)(?P<vol_unit>[mK]?)V"))
    df["vol_unit"] = df["vol_unit"].str.replace("K", "1000").str.replace("m", "0.001")
    df["vol_unit"] = np.where(df["vol_unit"].isin(["", np.nan]), "1", df["vol_unit"])
    df["vol"] = pd.to_numeric(df["vol"], errors="coerce") * pd.to_numeric(
        df["vol_unit"], errors="coerce"
    )
    df = df.join(df[0].str.extract(r"(?P<cap>[\d\.]+)(?P<cap_unit>[nNuUpP]?)[Ff]?"))
    df["cap_unit"] = df["cap_unit"].replace(
        {
            "p": "1000000",
            "P": "1000000",
            "n": "1000",
            "N": "1000",
            "u": "1",
            "U": "1",
            "": "1",
            np.nan: "1",
        }
    )
    df["cap"] = pd.to_numeric(df["cap"], errors="coerce") / pd.to_numeric(
        df["cap_unit"], errors="coerce"
    )

    df = df.join(df[0].str.extract(r"(?P<res>[\d\.]+)(?P<res_unit>[kKmM]?)", re.I))
    df["res_unit"] = df["res_unit"].replace(
        {
            "K": "1000",
            "k": "1000",
            "M": "1000000",
            "m": "0.001",
            "": "1",
            np.nan: "1",
        }
    )
    df["res"] = pd.to_numeric(df["res"], errors="coerce") * pd.to_numeric(
        df["res_unit"], errors="coerce"
    )

    df["package"] = df["mfgpn"].str.extract(r"(\d{4})")


def like_to_regex(x: str) -> str:
    """
    Excel like匹配转正则匹配
    """
    if x:
        x = "^" + x.replace("*", ".*").replace("[.*]", r"\*").replace("?", ".") + "$"
    else:
        x = "^.*$"
    return x
