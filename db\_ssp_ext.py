# -*- coding: utf-8 -*-
from sqlalchemy import Column, Date, DateTime, String, text
from sqlalchemy.dialects.mysql import INTEGER
from config import db

Base = db.Model


class Dummyload(Base):
    __tablename__ = "dummyload"
    __table_args__ = {"schema": "ssp_ext"}

    id = Column(INTEGER(11), primary_key=True)
    purpose = Column(String(255))
    equipment_pn = Column(String(255))
    power = Column(String(255))
    voltage = Column(String(255))
    state = Column(String(255))
    owner = Column(String(255))
    storage_floor = Column(String(255))
    equipment_remarks = Column(String(255))
    project = Column(String(255))
    ee = Column(String(255))
    me = Column(String(255))
    pm = Column(String(255))
    application_date = Column(Date)
    use_floor = Column(String(255))
    expected_end_date = Column(Date)
    expected_return_date = Column(Date)
    destination = Column(String(255))
    application_remarks = Column(String(255))
    reason_giving = Column(String(255))
    apply_return_date = Column(Date)
    reason_delay = Column(String(255))
    public = Column(String(255), comment="是否为共用")
    description = Column(String(255), comment="项目描述")
    prtno = Column(String(255), comment="可用于项目号")


class DummyloadHistory(Base):
    __tablename__ = "dummyload_history"
    __table_args__ = {"schema": "ssp_ext"}

    id = Column(INTEGER(11), primary_key=True)
    dummyload_id = Column(INTEGER(11), nullable=False, index=True)
    gmt_create = Column(
        DateTime, nullable=False, server_default=text("CURRENT_TIMESTAMP")
    )
    action = Column(String(255))
    purpose = Column(String(255))
    equipment_pn = Column(String(255))
    power = Column(String(255))
    voltage = Column(String(255))
    state = Column(String(255))
    owner = Column(String(255))
    storage_floor = Column(String(255))
    equipment_remarks = Column(String(255))
    project = Column(String(255))
    ee = Column(String(255))
    me = Column(String(255))
    pm = Column(String(255))
    application_date = Column(Date)
    use_floor = Column(String(255))
    expected_end_date = Column(Date)
    expected_return_date = Column(Date)
    destination = Column(String(255))
    application_remarks = Column(String(255))
    reason_giving = Column(String(255))
    apply_return_date = Column(Date)
    reason_delay = Column(String(255))
    public = Column(String(255), comment="是否为共用")
    description = Column(String(255), comment="项目描述")
    prtno = Column(String(255), comment="可用于项目号")


# SQLModel.metadata = MetaData(schema="ssp_ext")
# db = Alchemical(ALCHEMICAL_DATABASE_URL, model_class=SQLModel)


# class Dummyload(db.Model, table=True):
#     id: int = Field(primary_key=True)
#     purpose: Optional[str]
#     equipment_pn: Optional[str]
#     power: Optional[str]
#     voltage: Optional[str]
#     state: Optional[str]
#     owner: Optional[str]
#     storage_floor: Optional[str]
#     equipment_remarks: Optional[str]
#     project: Optional[str]
#     ee: Optional[str]
#     me: Optional[str]
#     pm: Optional[str]
#     application_date: Optional[date]
#     use_floor: Optional[str]
#     expected_end_date: Optional[date]
#     expected_return_date: Optional[date]
#     destination: Optional[str]
#     application_remarks: Optional[str]
#     reason_giving: Optional[str]
#     apply_return_date: Optional[date]
#     reason_delay: Optional[str]
#     public: Optional[str]
#     description: Optional[str]
#     prtno: Optional[str]


# class Dummyload_history(db.Model, table=True):
#     id: int = Field(primary_key=True)
#     dummyload_id: int = Field(foreign_key="dummyload.id")
#     action: Optional[str]
#     purpose: Optional[str]
#     equipment_pn: Optional[str]
#     power: Optional[str]
#     voltage: Optional[str]
#     state: Optional[str]
#     owner: Optional[str]
#     storage_floor: Optional[str]
#     equipment_remarks: Optional[str]
#     project: Optional[str]
#     ee: Optional[str]
#     me: Optional[str]
#     pm: Optional[str]
#     application_date: Optional[date]
#     use_floor: Optional[str]
#     expected_end_date: Optional[date]
#     expected_return_date: Optional[date]
#     destination: Optional[str]
#     application_remarks: Optional[str]
#     reason_giving: Optional[str]
#     apply_return_date: Optional[date]
#     reason_delay: Optional[str]
#     gmt_create: Optional[datetime] = Field(default_factory=datetime.now)
#     public: Optional[str]
#     description: Optional[str]
#     prtno: Optional[str]


db.create_all()
