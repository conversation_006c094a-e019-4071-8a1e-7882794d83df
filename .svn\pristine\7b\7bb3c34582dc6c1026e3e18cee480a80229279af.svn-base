# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
from dash import dcc, html, dash_table
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator
import dash_mantine_components as dmc
from common import mat_cats, read_db, pur_etd
from components import file_browser, create_sidebar
from config import SSP_DIR, cfg
import feffery_antd_components as fac
from dash_iconify import DashIconify
import dash_ag_grid as dag
import polars as pl
import feffery_antd_charts as fact
import polars.selectors as cs

# from statsmodels.tsa.holtwinters import SimpleExpSmoothing
# from datetime import date
import time
# import plotly.express as px

ns = Namespace("myNamespace", "tabulator")
cat1, cat2 = mat_cats()

# ----------收件箱中的栏位-----------·
project_application = {
    "NBE_HVG": "医疗电源",
    "NBE_IPS": "医疗电源",
    "ADP_ADP": "便携式电源",
    "DCBU_DCBU": "DC-DC",
    "AMP_AMP": "OBCM",
    "ATI_ATI": "OBCM",
    "APE_APE": "OBCM",
    "IDC_IDC": "网络电源",
    "DES_IMBU": "网络电源",
    "DES_CDBU": "网络电源",
    "SPA_PVIBU": "PV inverter",
    "SPA_EVCSBU": "PV inverter",
    "RTP_PCSBD": "储能",
    "LGT_LGT": "照明",
}

product_place = {
    "NBE_HVG": "吴江",
    "NBE_IPS": "吴江",
    "ADP_ADP": "吴江",
    "AMP_AMP": "吴江",
    "ATI_ATI": "吴江",
    "IDC_IDC": "吴江",
    "SPA_PVIBU": "吴江",
    "SPA_EVCSBU": "吴江",
    "APE_APE": "泰国",
    "DCBU_DCBU": "泰国",
    "DES_IMBU": "泰国",
    "DES_CDBU": "泰国",
    "LGT_LGT": "芜湖",
}
# ---------------新增模块-----------------
# tab1_print_btn = dbc.Button("打印", color="primary", size="md", id="tab1_print_btn")
# tab1_upload_btn = dcc.Upload(html.Button("上传"))
tab1_upload_btn = dcc.Upload(
    dbc.Button(html.A("上传"), color="warning", size="md"),
    id="tab1_upload",
    accept=".xlsx",
)
tab1_submit_btn = dbc.Button("提交", color="success", size="md", id="tab1_submit_btn")
tab1_add_row = dbc.Button(
    "新增行",
    # className="fa fa-plus",
    size="md",
    color="info",
    id="tab1_add_row",
)

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
    "font-family": "Helvetica",
    "textAlign": "left",
}

tab1_table = dash_table.DataTable(
    id="tab1_table",
    data=[{}],
    columns=[
        {"name": "id", "id": "id"},
        {"name": "checkcode", "id": "checkcode"},
        {"name": "申请备注", "id": "pur_remark"},
        {"name": "机种名", "id": "proj"},
        {"name": "台达料号", "id": "deltapn"},
        {"name": "描述", "id": "des"},
        {"name": "厂商", "id": "mfgname"},
        {"name": "厂商料号", "id": "mfgpn"},
        {"name": "工厂仓别", "id": "plant"},
        {"name": "需求数量", "id": "qty", "type": "numeric"},
        {
            "name": "需求日",
            "id": "req_date",
            "type": "datetime",
            "validation": {"allow_YY": True},
        },
        {"name": "部门", "id": "dept", "presentation": "dropdown"},
        {"name": "申请人", "id": "rd", "presentation": "dropdown"},
        {"name": "类别1", "id": "mat_catelogue", "presentation": "dropdown"},
        {"name": "类别2", "id": "mat_group", "presentation": "dropdown"},
        {"name": "in_db", "id": "in_db"},  # 辅助列，用于判断料号是否在数据库中
    ],
    tooltip_header={
        "req_date": "参照格式：22-2-12",
    },
    style_cell_conditional=[
        {"if": {"column_id": "dept"}, "width": "150px"},
        {"if": {"column_id": "rd"}, "width": "150px"},
        {"if": {"column_id": "mat_catelogue"}, "width": "80px"},
    ],
    style_header_conditional=[
        {
            "if": {"column_id": col},
            "textDecoration": "underline",
            "textDecorationStyle": "double",
        }
        for col in ["req_date"]
    ],
    tooltip_delay=0,
    tooltip_duration=None,
    editable=True,
    row_deletable=True,
    is_focused=True,
    page_action="native",
    page_current=0,
    page_size=10,
    filter_options={"case": "insensitive"},
    hidden_columns=["checkcode", "id", "in_db"],
    # fixed_rows={"headers": True},
    # style_table={"height": "390px"},
    # export_format="xlsx",  # ! lwm:添加下载功能
    # export_headers="display",  # ! lwm:添加下载功能
    style_header=style_header,
    style_cell={
        "whiteSpace": "normal",
        "height": "auto",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
    },
    style_data_conditional=[
        {
            "if": {"filter_query": "{in_db}  is blank"},
            "backgroundColor": "#FFF8DC",
        },
    ],
    css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    style_table={"height": "400px", "overflowY": "auto"},
)

tab1_notice = dbc.Alert(
    id="tab1_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)


tab1_content = dmc.Stack(
    [
        dbc.Row(
            [
                dbc.Col(tab1_add_row, width=2),
                # dbc.Col(tab1_print_btn, width=2),
                dbc.Col(tab1_upload_btn, width=2),
                dbc.Col(tab1_submit_btn, width=2),
                dbc.Col(tab1_notice),
            ],
            justify="between",
            className="my-2",
        ),
        tab1_table,
    ],
    pr=5,
    spacing=0,
)


# ---------------处理模块-----------------
tab2_table = DashTabulator(
    id="tab2_table",
    theme="tabulator_site",
    clearFilterButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "清除筛选",
    },
    options={
        "layout": "fitDataStretch",
        "height": "80vh",
        "persistence": True,  # 可以保留用户设置的样式，测试的时候需要注释掉
        "selectable": True,
        "clipboard": True,
        "clipboardCopyStyled": True,
        "clipboardCopySelector": "selected",
        "clipboardCopyRowRange": "selected",
        "clipboardPasteAction": "update",
        "rowFormatter": ns("rowFormatter4"),
        "pagination": "local",
        "paginationSize": 10,
        "paginationSizeSelector": True,
        "movableColumns": True,
    },
    columns=[
        # 具有全选的功能，复选框的功能
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
            "clipboard": False,
        },
        {
            "title": "id",
            "field": "id",
            "headerSort": False,
            "headerClick": ns("headerClick"),
        },
        {
            "title": "状态",
            "field": "status",
            "headerFilter": "select",
            "headerFilterParams": {"values": True},
            "clipboard": False,
            "width": 90,
            "headerFilterLiveFilter": False,
        },
        {
            "title": "用途",
            "field": "application",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "归属地",
            "field": "dest_area",
            "headerFilter": "input",
            "editor": "select",
            "editorParams": ["SH", "HZ", "WH"],
        },
        {
            "title": "项目号",
            "field": "prtno",
            "editor": "input",
            "headerFilter": "input",
        },
        {"title": "部门", "field": "dept", "editor": "input", "headerFilter": "input"},
        {"title": "工程师", "field": "rd", "editor": "input", "headerFilter": "input"},
        {
            "title": "机种名",
            "field": "proj",
            "editor": "input",
            "headerFilter": "input",
            "width": 100,
        },
        {
            "title": "台达料号",
            "field": "deltapn",
            "editor": "input",
            "headerFilter": "input",
            "width": 100,
        },
        {
            "title": "描述",
            "field": "des",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "width": 100,
        },
        {
            "title": "封装",
            "field": "smd_dip",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "厂商",
            "field": "mfgname",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "formatter": ns("formatter6"),
        },
        {
            "title": "厂商料号",
            "field": "mfgpn",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "需求数量",
            "field": "qty",
            "editor": "input",
            # "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "width": 100,
        },
        {
            "title": "采购数量",
            "field": "r_qty",
            "editor": "input",
            # "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "validator": "integer",
        },
        {
            "title": "厂区",
            "field": "plant",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "工厂库存",
            "field": "plant_qty",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "仓别",
            "field": "location",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "架位",
            "field": "lot",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "单价",
            "field": "price",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "总价",
            "field": "total_price",
            # "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "validator": "float",
            # "clipboard": False,
        },  # 数据库无
        {
            "title": "到板日期",
            "field": "pcb_date",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            # "clipboard": False,
        },  # 数据库无 pcb到板时间
        {
            "title": "材料发起日",
            "field": "start_date",
            "editor": "input",  # 测试可编辑
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "title": "需求日",
            "field": "req_date",
            # "editor": "input",
            # "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "title": "采购申请日",
            "field": "pur_date",
            # "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "title": "预计到货日",
            "field": "es_date",
            "editor": ns("dateEditor"),
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "title": "采购备注",
            "field": "pur_remark",
            "editor": "input",
            # "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "width": 100,
        },
        {
            "title": "到货数量",
            "field": "received_qty",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "clipboard": False,
        },  # 数据库无
        {
            "title": "PR单号",
            "field": "pr_no",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "PO单号",
            "field": "po_no",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },
        {
            "title": "是否签收",
            "field": "signed",
            "headerFilter": "input",
            "editor": "select",
            "editorParams": ["Y"],
        },  # 数据库无
        {
            "title": "替代料",
            "field": "sub_deltapn",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
        },  # 数据库为“替代料号”
        {
            "title": "物料备注",
            "field": "mat_remark",
            "headerFilter": "input",
            "width": 100,
        },  # 数据库无
        {
            "title": "前次预到日",
            "field": "es_date_lasttime",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
            },
            "clipboard": False,
        },
        {
            "title": "材料类别",
            "field": "mat_catelogue",
            "editor": "select",
            "editorParams": cat1,
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "clipboard": False,
        },  # 数据库为“材料大类”
        {
            "title": "材料类型",
            "field": "mat_group",
            "editor": "select",
            "editorParams": cat2,
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "clipboard": False,
        },  # 数据库为“材料类型”
        {
            "title": "品质问题",
            "field": "qissue",
            "editor": "input",
            "headerMenu": ns("headerMenu3"),
            "headerFilter": "input",
            "clipboard": False,
        },
        {
            "title": "CheckCode",
            "field": "checkcode",
            "editor": "input",
            # "headerMenu": ns("headerMenu4"),
            "headerFilter": "input",
            "clipboard": False,
        },
        {
            "title": "上海库存",
            "field": "sh_stock",
            "editor": "input",
            "headerFilter": "input",
            "clipboard": False,
        },  # 数据库无
        {
            "title": "杭州库存",
            "field": "hz_stock",
            "editor": "input",
            "headerFilter": "input",
            "clipboard": False,
        },  # 数据库无
        {
            "title": "vendor",
            "field": "vendor_exists",
            "visible": False,
            "clipboard": False,
        },
        {
            "title": "原状态",
            "field": "pur_status",
            "visible": False,
            "clipboard": False,
        },
    ],
)


def tab2_content(user: dict):
    return dmc.Stack(
        [
            dbc.Row(
                [
                    dbc.Col(
                        dcc.Dropdown(
                            id="tab2_dro_1",
                            placeholder="人员",
                            options=cfg.pur + ["Bo.Sm.Wang", "ALL"],
                        ),
                        width=2,
                    ),
                    # dbc.Col(
                    #     dcc.Dropdown(
                    #         id="tab2_status",
                    #         placeholder="状态",
                    #         options=[
                    #             {"label": "Pending", "value": "pending"},
                    #             {"label": "ConfirmETD", "value": "confirm_etd"},
                    #             {"label": "Received", "value": "received"},
                    #             {"label": "待PR单", "value": "pr"},
                    #             {"label": "逾期", "value": "overdue"},
                    #         ],
                    #     ),
                    #     width=2,
                    # ),
                    dbc.Col(
                        dcc.Dropdown(
                            id="tab2_dro_2",
                            placeholder="转单至",
                            options=cfg.pur + ["Bo.Sm.Wang"],
                        ),
                        width=2,
                    ),
                    dbc.Col(
                        dbc.Button(
                            "更新", color="warning", size="md", id="tab2_update_btn"
                        ),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Button(
                            "取消", color="danger", size="md", id="tab2_cancel_btn"
                        ),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Button(
                            "下单", color="success", size="md", id="tab2_order_btn"
                        ),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Button("邮件", color="info", size="md", id="tab2_mail_btn"),
                        width=1,
                    ),
                    dbc.Col(
                        dbc.Alert(
                            id="tab2_notice",
                            is_open=False,
                            duration=3000,
                            color="warning",
                            class_name="d-inline-flex p-1",
                        )
                    ),  # --------------------弹窗
                ],
                justify="between",
                className="my-2",
            ),
            dmc.LoadingOverlay(tab2_table),
        ],
        pr=5,
        spacing=0,
    )


# ---------------查询模块-----------------
status_dropdown = dcc.Dropdown(
    id="status_dropdown",
    placeholder="状态",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)
application_dropdown = dcc.Dropdown(
    id="application_dropdown",
    placeholder="用途",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)
department_dropdown = dcc.Dropdown(
    id="department_dropdown",
    placeholder="部门",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)
pur_dropdown = dcc.Dropdown(
    id="pur_dropdown",
    placeholder="采购",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)

mfgname_dropdown = dcc.Dropdown(
    id="mfgname_dropdown",
    placeholder="厂商",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
        # "border-style": "none",
    },
)

rd_dropdown = dcc.Dropdown(
    id="rd_dropdown",
    placeholder="工程师",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)

# 查询数据库下拉菜单
database_dropdown = dcc.Dropdown(
    id="database_dropdown",
    placeholder="数据库",
    style={
        "width": "100%",
        "flex": 1,
    },
    options=[
        {"label": "pur", "value": "pur"},
        {"label": "plant", "value": "pur_plant"},
    ],
    value="pur",
)

cat1_dropdown = dcc.Dropdown(
    id="cat1_dropdown",
    placeholder="类别1",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)

cat2_dropdown = dcc.Dropdown(
    id="cat2_dropdown",
    placeholder="类别2",
    multi=True,
    style={
        "width": "100%",
        "flex": 1,
    },
)


# pur_release_date = dmc.DateRangePicker(
#     id="pur_release_date",
#     placeholder="材料发起日",
#     inputFormat="YYYY-MM-DD",
#     # label="材料发起日",
#     zIndex=1050,
# )


tab3_datepicker1 = dmc.DatePicker(
    id="tab3_datepicker1",
    inputFormat="YYYY-MM-DD",
    style={"width": 150},
    label="材料发起日-开始",
    clearable=False,
    required=True,
    dropdownPosition="top-start",
)

tab3_datepicker2 = dmc.DatePicker(
    id="tab3_datepicker2",
    inputFormat="YYYY-MM-DD",
    style={"width": 150},
    label="材料发起日-结束",
    clearable=False,
    required=True,
    dropdownPosition="top-start",
)

mat_start_date = dmc.Group(
    spacing="xl",
    children=[
        tab3_datepicker1,
        tab3_datepicker2,
    ],
    id="mat_start_date",
)

tab3_select_btn = dbc.Button("查询", color="primary", size="md", id="tab3_select_btn")
# ---------------------侧边栏---------------------------------------------
nav = html.Div(
    [
        database_dropdown,
        status_dropdown,
        application_dropdown,
        cat1_dropdown,
        cat2_dropdown,
        department_dropdown,
        pur_dropdown,
        mfgname_dropdown,
        rd_dropdown,
        mat_start_date,
        tab3_select_btn,
    ],
    className="d-grid gap-2",
)

offcanvas = dbc.Offcanvas(
    children=nav,
    id="offcanvas-placement",
    title="请输入筛选条件",
    is_open=False,
    # placement="top",
)

# -----------------------------------------------------------------------------------------------------
tab3_dowload_btn = dbc.Button("下载", color="danger", size="md", id="tab3_dowload_btn")
tab3_allcheck_btn = dbc.Button(
    "全选", color="warning", size="md", id="tab3_allcheck_btn"
)
# tab3_allcheck_btn = dmc.Checkbox(size="md", label="全选", id="tab3_allcheck_btn")
# downloadButtonType1 = {
#     #  btn-outline-primary position-absolute bottom-0 start-5 my-3
#     "css": "btn btn-sm  pur-tab3-right1",
#     "text": "下载",
#     "type": "xlsx",
# }

# clearFilterButtonType1 = {
#     # position-absolute  bottom-0  my-3 right2
#     "css": "btn btn-sm  pur-tab3-right2",
#     "text": "清除过滤",
# }
# tab3_select_download_btn = dbc.Button("下载", color="danger", size="md")
tab3_notice = dbc.Alert(
    id="tab3_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)


tab3_table = dash_table.DataTable(
    id="tab3_table",
    data=[],
    filter_options={"case": "insensitive"},
    columns=[
        {"name": "id", "id": "id"},
        {"name": "状态", "id": "pur_status", "hideable": True},
        {"name": "用途", "id": "application", "hideable": True},
        {"name": "项目号", "id": "prtno", "hideable": True},
        {"name": "部门", "id": "dept", "hideable": True},
        {"name": "工程师", "id": "rd", "hideable": True},
        {"name": "机种名", "id": "proj", "hideable": True},
        {"name": "台达料号", "id": "deltapn", "hideable": True},
        {"name": "描述", "id": "des", "hideable": True},
        {"name": "厂商", "id": "mfgname", "hideable": True},
        {"name": "厂商料号", "id": "mfgpn", "hideable": True},
        {"name": "厂区", "id": "plant", "hideable": True},
        {"name": "工厂库存", "id": "plant_qty", "hideable": True},
        {"name": "仓别", "id": "location", "hideable": True},
        {"name": "架位", "id": "lot", "hideable": True},
        {
            "name": "需求数量",
            "id": "qty",
            "hideable": True,
            # "editable": "input",
        },
        {
            "name": "采购数量",
            "id": "r_qty",
            "hideable": True,
            # "editable": "input",
        },
        {
            "name": "单价",
            "id": "price",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },
        {
            "name": "总价",
            "id": "total_price",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },  # 数据库无
        {
            "name": "到板日期",
            "id": "pcb_date",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
            "type": "datetime",
            # "formate": {"YY-MM-DD": True},
        },  # 数据库无"
        {
            "name": "材料发起日",
            "id": "start_date",
            "hideable": True,
            # "editable": "input",
            "type": "datetime",
            # 'format':'YYYY-MM-DD',
            # "validation": {"allow_YY-MM-DD": True},
        },
        {
            "name": "需求日",
            "id": "req_date",
            "hideable": True,
            # "editable": "input",
            "type": "datetime",
            # "validation": {"allow_YY-MM-DD": True},
        },
        {
            "name": "采购申请日",
            "id": "pur_date",
            "hideable": True,
            # "editable": "input",
            "type": "datetime",
            # "validation": {"allow_YY-MM-DD": True},
        },
        {
            "name": "预计到货日",
            "id": "es_date",
            "hideable": True,
        },  # 数据库为“上一次预计到货日”
        {
            "name": "采购备注",
            "id": "pur_remark",
            "hideable": True,
            # "editable": "input",
            # "width": 120,
        },
        {
            "name": "PR单号",
            "id": "pr_no",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },
        {
            "name": "PO单号",
            "id": "po_no",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },
        {
            "name": "是否签收",
            "id": "signed",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },  # 数据库无
        {"name": "替代料", "id": "sub_deltapn", "hideable": True},
        {"name": "替代料库位", "id": "sub_stockno", "hideable": True},
        {"name": "物料备注", "id": "mat_remark", "hideable": True},  # 数据库无
        {"name": "采购", "id": "pur", "hideable": True},
        {"name": "采购交期", "id": "pur_lead_time", "hideable": True},  #!lwm:修改id名称
        {"name": "到货日", "id": "mat_receiveddate", "hideable": True},
        {
            "name": "到货数量",
            "id": "received_qty",
            "hideable": True,
        },  #!lwm:添加到货数量
        {
            "name": "材料类别",
            "id": "mat_catelogue",
            "hideable": True,
        },  # 数据库为“材料大类”
        {"name": "材料类型", "id": "mat_group", "hideable": True},
        {
            "name": "CE备注",
            "id": "ce_remark",
            "hideable": True,
            # "editable": "input",
            # "headerMenu": ns("headerMenu4"),
        },
        {
            "name": "品质问题",
            "id": "qissue",
            "hideable": True,
            # "editor": "input",
            # "headerMenu": ns("headerMenu4"),
        },  # 数据库无
        {
            "name": "CheckCode",
            "id": "checkcode",
            "hideable": True,
            # "editor": "input",
            # "headerMenu": ns("headerMenu4"),
        },
        {
            "name": "上海库存",
            "id": "sh_stock",
            "hideable": True,
            # "editor": "input",
        },  # 数据库无
        {
            "name": "杭州库存",
            "id": "hz_stock",
            "hideable": True,
            # "editor": "input",
        },  # 数据库无
        {
            "name": "常用料",
            "id": "com_mat",
            "hideable": True,
            # "editor": "input",
        },  # 数据库无
    ],
    row_selectable="multi",
    is_focused=True,
    hidden_columns=["id"],
    # sort_action="custom",
    # sort_mode="multi",
    filter_action="custom",
    filter_query="",
    # fixed_rows={"headers": True},
    style_table={"height": "75vh", "overflowX": "auto"},
    style_header=style_header,
    page_action="custom",
    page_current=0,
    page_size=15,
    style_cell={
        # "whiteSpace": "normal",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
        "maxWidth": 98,
        "minWidth": 98,
        "width": 98,
        "overflow": "hidden",
        "textOverflow": "ellipsis",
    },
    # style_data_conditional=[
    #     # {"if": {"column_type": "datetime"},format=Format()},
    #     {
    #         "if": {"column_editable": False},  # True | False
    #         "backgroundColor": "rgb(240, 240, 240)",
    #         "cursor": "not-allowed",
    #     },
    # ],
    # export_format="xlsx",
    # export_headers="display",
    # css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
)
tab3_table_store = dcc.Store(id="tab3_table_store")
tab3_table_row_ids_store = dcc.Store(id="tab3_table_row_ids_store", data={})
tab3_pur_reopen = dmc.Modal(
    [
        dmc.Text("重新打开该笔记录？"),
        dmc.Space(h=20),
        dmc.Group(
            [
                dmc.Button("确定", id="reopen-submit"),
                dmc.Button(
                    "取消",
                    color="red",
                    variant="outline",
                    id="reopen-cancel",
                ),
            ],
            position="right",
        ),
    ],
    id="pur-reopen",
    centered=True,
)
tab3_content = dmc.Stack(
    [
        dbc.Row(
            [
                dbc.Col(tab3_allcheck_btn, width=1),
                dbc.Col(tab3_dowload_btn, width=1),
                dbc.Col(dbc.Button("一级筛选", id="open-offcanvas-btn", n_clicks=0)),
            ],
            justify="between",
            className="my-2",
        ),
        offcanvas,
        dmc.LoadingOverlay(tab3_table),
        tab3_pur_reopen,
    ],
    pr=5,
    spacing=0,
)
# ---------------调料模块-----------------
tab4_upload = dcc.Upload(
    dbc.Button(html.A("上传"), color="warning", size="md"), id="tab4_upload"
)
tab4_refresh_btn = dbc.Button(
    "刷新", color="secondary", size="md", id="tab4_refresh_btn"
)
tab4_allcheck_btn = dbc.Button(
    "全选", color="warning", size="md", id="tab4_allcheck_btn"
)
tab4_dowload_btn = dbc.Button("下载", color="danger", size="md", id="tab4_dowload_btn")
# tab4_download = dcc.Download(id="tab4_download")#!lwm:下载组件放在全局使用
tab4_order_btn = dbc.Button("下单", color="success", size="md", id="tab4_order_btn")
tab4_update_btn = dbc.Button("更新", color="info", size="md", id="tab4_update_btn")
tab4_transfer_btn = dbc.Button(
    "转单", color="primary", size="md", id="tab4_transfer_btn"
)
tab4_notice = dbc.Alert(
    id="tab4_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)


tab4_table = dash_table.DataTable(
    id="tab4_table",
    data=[],
    # editable=True,
    filter_options={"case": "insensitive"},
    columns=[
        {"name": "id", "id": "id"},
        {"name": "状态", "id": "pur_status", "hideable": True},
        {"name": "用途", "id": "application", "hideable": True},
        {"name": "项目号", "id": "prtno", "hideable": True},
        {"name": "部门", "id": "dept", "hideable": True},
        {"name": "工程师", "id": "rd", "hideable": True},
        {"name": "机种名", "id": "proj", "hideable": True},
        {"name": "台达料号", "id": "deltapn", "hideable": True},
        {"name": "描述", "id": "des", "hideable": True},
        {"name": "厂商", "id": "mfgname", "hideable": True},
        {"name": "厂商料号", "id": "mfgpn", "hideable": True},
        {"name": "厂区", "id": "plant", "hideable": True, "editable": True},
        {
            "name": "工厂库存",
            "id": "plant_qty",
            "type": "numeric",
            "hideable": True,
            "editable": True,
        },
        {"name": "仓别", "id": "location", "hideable": True, "editable": True},
        {"name": "架位", "id": "lot", "hideable": True, "editable": True},
        {"name": "需求数量", "id": "qty", "type": "numeric", "hideable": True},
        {"name": "采购数量", "id": "r_qty", "hideable": True, "editable": True},
        {
            "name": "材料发起日",
            "id": "start_date",
            "hideable": True,
            "type": "datetime",
            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "name": "需求日",
            "id": "req_date",
            "hideable": True,
            "type": "datetime",
            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "name": "采购申请日",
            "id": "pur_date",
            "hideable": True,
            "type": "datetime",
            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {
            "name": "预计到货日",
            "id": "es_date",
            "hideable": True,
            "editable": True,
            "type": "datetime",
            "validation": {"allow_YY": True},
            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        {"name": "采购备注", "id": "pur_remark", "hideable": True, "editable": True},
        {"name": "部门计费代码", "id": "charge_code", "hideable": True},
        {"name": "上海库存", "id": "sh_stock", "hideable": True},
        {"name": "杭州库存", "id": "hz_stock", "hideable": True},
        {"name": "CheckCode", "id": "checkcode", "hideable": True},
    ],
    row_selectable="multi",
    is_focused=True,
    # hidden_columns=["id"],
    sort_action="native",
    sort_mode="multi",
    filter_action="native",
    # page_action="native",
    # page_current=0,
    # page_size=10,
    # selected_rows=[],
    fixed_rows={"headers": True},
    style_table={"height": "80vh", "overflowX": "auto"},
    style_header=style_header,
    style_cell={
        # "whiteSpace": "normal",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
        "maxWidth": 90,
        "minWidth": 90,
        "width": 90,
        "overflow": "hidden",
        "textOverflow": "ellipsis",
    },
    # css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    style_data_conditional=[
        {
            "if": {"filter_query": "{plant_qty} < {qty}"},
            "backgroundColor": "#FFD700",
        },
        {
            "if": {"filter_query": '{plant_qty} = "0"'},
            "backgroundColor": "#FA8072",
        },
        {
            "if": {"filter_query": "{plant_qty} is blank"},
            "backgroundColor": "#FA8072",
            # "color": "white",
        },
        {
            "if": {"filter_query": "{id} is blank"},
            "backgroundColor": "#FF8C00",
            # "color": "white",
        },
        {"if": {"column_id": "pur_status"}, "width": "50px"},  #!没用
    ],
)

tab4_content = dmc.Stack(
    [
        dbc.Row(
            [
                dbc.Col(tab4_refresh_btn, width=1),
                dbc.Col(tab4_allcheck_btn, width=1),
                dbc.Col(tab4_order_btn, width=1),
                dbc.Col(tab4_update_btn, width=1),
                dbc.Col(tab4_transfer_btn, width=1),
                dbc.Col(tab4_upload, width=1),
                dbc.Col(tab4_dowload_btn, width=1),
                dbc.Col(tab4_notice),  # --------------------弹窗
            ],
            justify="between",
            className="my-2",
        ),
        dmc.LoadingOverlay(tab4_table),
        # tab4_download,
    ],
    pr=5,
    spacing=0,
)

# --------------------发件箱-----------------------
# tab5_refresh_btn = dbc.Button("刷新", color="success", size="md", id="tab5_refresh_btn")
tab5_update_btn = dbc.Button("更新", color="info", size="md", id="tab5_update_btn")
tab5_send_btn = dbc.Button("发送", color="danger", size="md", id="tab5_send_btn")
tab5_notice = dbc.Alert(
    id="tab5_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)
tab5_table = DashTabulator(
    id="tab5_table",
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "80vh",
        "rowContextMenu": ns("RowMenu1"),  # 右击实现删除行
        "rowFormatter": ns("rowFormatter3"),
        "clipboard": True,
    },
    columns=[
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
        },
        {"title": "id", "field": "id"},
        # {"title": "action", "field": "action"},
        {"title": "邮箱", "field": "mail", "editor": "input", "width": 120},
        {"title": "收件地", "field": "area", "editor": "input"},
        {"title": "台达料号", "field": "deltapn", "editor": "input"},
        {"title": "描述", "field": "des", "editor": "input", "width": 120},
        {"title": "厂商", "field": "mfgname", "editor": "input", "width": 110},
        # ----------------自己取的field---------
        {"title": "型号", "field": "mfgpn", "editor": "input"},
        {"title": "申请数量", "field": "r_qty", "editor": "input"},
        {
            "title": "需求日期",
            "field": "req_date",
            "type": "datetime",
            "editor": "input",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
        },
        # ----------------------------------
        {"title": "机种名", "field": "proj", "editor": "input", "width": 110},
        {"title": "产品应用", "field": "project_application"},
        {"title": "量产时间", "field": "product_time", "editor": "input"},
        {"title": "量产地", "field": "product_place"},
        {"title": "量产用量", "field": "product_consumption", "editor": "input"},
        {"title": "RD", "field": "rd", "editor": "input"},
    ],
    data=[],
)
tab5_mail_store = dcc.Store(id="tab5_mail_store")


tab5_content = dmc.Stack(
    [
        dbc.Row(
            [
                # dbc.Col(tab5_refresh_btn, width=1),
                dbc.Col(tab5_update_btn, width=1),
                dbc.Col(tab5_send_btn, width=1),
                dbc.Col(tab5_notice),  # --------------------弹窗
            ],
            justify="between",
            className="my-2",
        ),
        tab5_table,
    ],
    pr=5,
    spacing=0,
)

# -------------------供应商----------------------------------------
tab6_refresh_btn = dbc.Button("刷新", color="success", size="md", id="tab6_refresh_btn")
tab6_submit_btn = dbc.Button("提交", color="warning", size="md", id="tab6_update_btn")
tab6_notice = dbc.Alert(
    id="tab6_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)
tab6_add_row = dbc.Button(
    "新增行",
    size="md",
    id="tab6_add_row",
)
tab6_table = DashTabulator(
    id="tab6_table",
    theme="tabulator_site",
    options={"layout": "fitDataStretch", "height": "80vh", "selectable": True},
    columns=[
        {"title": "id", "field": "id"},
        {
            "title": "action",
            "field": "action",
            "editor": "select",
            "editorParams": ["update", "delete"],
        },
        {
            "title": "Commodity",
            "field": "commodity",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "Vendor_Name",
            "field": "vendor_name",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "Agent_Name",
            "field": "agent_name",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "OA_Code",
            "field": "oa_code",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "Window",
            "field": "window",
            "editor": "select",
            "editorParams": ["TO", "CC", "NA"],
            "headerFilter": "input",
        },  # to，cc，na
        {
            "title": "Chinese_Name",
            "field": "chinese_name",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "English_Name",
            "field": "english_name",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "title",
            "field": "title",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "tel",
            "field": "tel",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "mobile",
            "field": "mobile",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "Mail",
            "field": "mail",
            "editor": "input",
            "headerFilter": "input",
        },
    ],
    data=[],
)
tab6_content = dmc.Stack(
    [
        dbc.Row(
            [
                dbc.Col(tab6_add_row, width=1),
                dbc.Col(tab6_refresh_btn, width=1),
                dbc.Col(tab6_submit_btn, width=1),
                dbc.Col(tab6_notice),  # --------------------弹窗
            ],
            justify="between",
            className="my-2",
        ),
        dmc.LoadingOverlay(tab6_table),
    ],
    pr=5,
    spacing=0,
)


# -------------------工程物料分工职责----------------------------------------
tab7_refresh_btn = dbc.Button("刷新", color="success", size="md", id="tab7_refresh_btn")
tab7_submit_btn = dbc.Button("提交", color="warning", size="md", id="tab7_update_btn")

pur_tab7_table = dag.AgGrid(
    id="pur_assign_table",
    className="ag-theme-quartz",
    columnDefs=[
        {"field": "id", "headerName": "id", "hide": True},
        # {"field": "cat_id", "headerName": "cat_id"},
        {
            "field": "action",
            "headerName": "Action",
            "cellEditor": {"function": "DMC_Select"},
            "cellEditorParams": {
                "options": ["update"],
                "clearable": True,
                "initiallyOpened": True,
            },
            "cellEditorPopup": True,
            "editable": True,
        },
        {"field": "cat1", "headerName": "类别1"},
        {"field": "cat2", "headerName": "类别2"},
        {"field": "area", "headerName": "区域"},
        {"field": "dept", "headerName": "部门"},
        {
            "field": "pur",
            "headerName": "职责人",
            "cellEditor": {"function": "DMC_Select"},
            "cellEditorParams": {
                "options": cfg.pur,
                "clearable": True,
                "shadow": "xl",
            },
            "cellEditorPopup": True,
            "editable": True,
        },
    ],
    rowData=[],
    columnSize="sizeToFit",
    defaultColDef={
        "resizable": True,
        "sortable": True,
        "filter": True,
        "wrapHeaderText": True,
        "autoHeaderHeight": True,
        # "editable": True,
    },
    dashGridOptions={
        "rowSelection": "single",
        "stopEditingWhenCellsLoseFocus": True,
        "singleClickEdit": True,
        "rowHeight": 35,
        "enableCellTextSelection": True,
        "ensureDomOrder": True,
    },
    style={"height": "80vh"},
)
tab7_notice = dbc.Alert(
    id="tab7_notice",
    is_open=False,
    duration=3000,
    color="warning",
    class_name="d-inline-flex p-1",
)
tab7_content = dmc.Stack(
    [
        dbc.Row(
            [
                dbc.Col(tab7_refresh_btn, width=1),
                dbc.Col(tab7_submit_btn, width=1),
                dbc.Col(tab7_notice),  # --------------------弹窗
            ],
            justify="between",
            className="my-2",
        ),
        dmc.LoadingOverlay(pur_tab7_table),
    ],
    pr=5,
    spacing=0,
)
pur_download = dcc.Download(id="pur_download")

fb = file_browser(SSP_DIR / "program" / "DOC" / "pur", __name__)
markdown = dcc.Markdown(
    """
## 工程物料上下端流程图  

![流程图](/assets/img/pur01.jpg "流程图")

**流程图解释:**    

    1. CE双向：编料号的需求，Project （new part）从CE到工程物料，Debug（addce）工程物料到CE.  
    2. BOM组单向：从BOM程式到工程物料网页板.  
    3. 工程师单向：SSP网页板需求到工程物料网页板.  
    4. 仓库：输出收料，输入：库存不足，盘点修正.  
    5. 样制单向：Project材料采购备注，缺料替代料信息单向传入到样制.  

**采购状态:**   

![采购状态](/assets/img/pur02.jpg "采购状态")

"""
)
tab8_content = dmc.Stack([fb, dmc.Divider(color="orange"), markdown], pr=5, spacing=0)

# ---------------安全库存模块-----------------
tab9_add_row = dmc.ActionIcon(
    DashIconify(icon="material-symbols:add-box-outline"),
    size=25,
    color="red",
    variant="light",
    id="tab9_add_row",
)

tab9_update_btn = dbc.Button("更新", color="warning", size="md", id="tab9_update_btn")
tab9_cancel_btn = dbc.Button("取消", color="danger", size="md", id="tab9_cancel_btn")
tab9_calculate_btn = dbc.Button(
    "计算", color="success", size="md", id="tab9_calculate_btn"
)
tab9_download_btn = dbc.Button("下载", color="info", size="md", id="tab9_download_btn")
tab9_add_btn = dbc.Button("新增", color="primary", size="md", id="tab9_add_btn")
tab9_dept_select = fac.AntdTreeSelect(
    treeData=[],
    treeCheckable=True,
    id="tab9_dept_select",
    placeholder="专用部门",
    # style={"width": "250px"},
)

tab9_table = DashTabulator(
    id="tab9_table",
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "75vh",
        # "persistence": True,  # 可以保留用户设置的样式，测试的时候需要注释掉
        "selectable": True,
        "clipboard": True,
        "clipboardCopyStyled": True,
        "clipboardCopySelector": "selected",
        "clipboardCopyRowRange": "selected",
        "clipboardPasteAction": "update",
        "rowFormatter": ns("rowFormatter4"),
        "pagination": "local",
        "paginationSize": 10,
        "paginationSizeSelector": True,
        "movableColumns": True,
    },
    columns=[
        # 具有全选的功能，复选框的功能
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
            "clipboard": False,
        },
        {
            "title": "地区",
            "field": "area",
            "editor": "select",
            "editorParams": ["SH", "HZ", "WH"],
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "部门限用",
            "field": "limituse",
            "headerFilter": "input",
            "width": 100,
            # "clipboard": False,
        },
        {
            "title": "状态",
            "field": "status",
            "editor": "select",
            "editorParams": ["TBD", "Processing"],
            "headerFilter": "input",
            # "clipboard": False,
        },
        # {
        #     "title": "Checkcode",
        #     "field": "checkcode",
        #     # "editor": "input",
        #     "headerFilter": "input",
        #     # "clipboard": False,
        # },
        {
            "title": "台达料号",
            "field": "deltapn",
            "editor": "input",
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "描述",
            "field": "des",
            "editor": "input",
            "headerFilter": "input",
            "width": 150,
            # "clipboard": False,
        },
        {
            "title": "厂商",
            "field": "mfgname",
            "editor": "input",
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "厂商料号",
            "field": "mfgpn",
            "editor": "input",
            "headerFilter": "input",
            "width": 100,
            # "clipboard": False,
        },
        {
            "title": "使用日期",
            "field": "latestdate",
            # "editor": "input",
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
            # "clipboard": False,
        },
        {
            "title": "加入日期",
            "field": "adddate",
            # "editor": "input",
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
            # "clipboard": False,
        },
        {
            "title": "取消日期",
            "field": "canceldate",
            # "editor": "input",
            "headerFilter": "input",
            "formatter": "datetime",
            "formatterParams": {"outputFormat": "YYYY-MM-DD"},
            # "clipboard": False,
        },
        {
            "title": "职责人",
            "field": "latestowner",
            # "editor": "input",
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "加入方式",
            "field": "addtype",
            # "editor": "input",
            "headerFilter": "input",
            # "width": 100
            # "clipboard": False,
        },
        {
            "title": "定制数量",
            "field": "custom_qty",
            "editor": "input",
            "validator": "integer",
            "headerFilter": "number",
            "headerFilterPlaceholder": "大于",
            "headerFilterFunc": ">",
        },
        {
            "title": "定制采购量",
            "field": "pur_qty",
            "editor": "input",
            "validator": "integer",
            "headerFilter": "number",
            "headerFilterPlaceholder": "大于",
            "headerFilterFunc": ">",
        },
        {
            "title": "特殊说明",
            "field": "memo",
            "editor": "input",
            "headerFilter": "input",
            # "clipboard": False,
        },
        # {
        #     "title": "公式结果",
        #     "field": "ss",
        #     "headerFilter": "number",
        #     "headerFilterPlaceholder": "大于",
        #     "headerFilterFunc": ">",
        #     "headerMenu": ns("headerMenu5"),
        # },
        {
            "title": "安全库存",
            "field": "safetystock",
            "headerFilter": "number",
            "headerFilterPlaceholder": "大于",
            "headerFilterFunc": ">",
            "headerMenu": ns("headerMenu5"),
        },
        {
            "title": "当前库存",
            "field": "stock_qty",
            "headerFilter": "number",
            "headerFilterPlaceholder": "大于",
            "headerFilterFunc": ">",
            "headerMenu": ns("headerMenu5"),
        },
        {
            "title": "在途数量",
            "field": "transit_qty",
            "headerFilter": "number",
            "headerFilterPlaceholder": "大于",
            "headerFilterFunc": ">",
            "headerMenu": ns("headerMenu5"),
        },
        {
            "title": "下单条件",
            "field": "order_condition",
            # "editor": "input",
            "headerFilter": "number",
            "headerFilterPlaceholder": "小于",
            "headerFilterFunc": "<",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "下单数量",
            "field": "order_qty",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "部门使用量",
            "field": "dept_qty",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "季度使用次数",
            "field": "quarter_times",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "季度使用量",
            "field": "quarter_qty",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "半年使用次数",
            "field": "half_times",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "半年使用量",
            "field": "half_qty",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "年使用次数",
            "field": "year_times",
            "headerMenu": ns("headerMenu5"),
            # "editor": "input",
            "headerFilter": "input",
            # "clipboard": False,
        },
        {
            "title": "年使用量",
            "field": "year_qty",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
        {
            "title": "年最大量",
            "field": "year_max",
            # "editor": "input",
            "headerFilter": "input",
            "headerMenu": ns("headerMenu5"),
            # "clipboard": False,
        },
    ],
    data=[],
)
tab9_dro_2 = dcc.Dropdown(id="tab9_dro_2", placeholder="转单至", options=cfg.pur)

tab9_dro_1 = dcc.Dropdown(
    id="tab9_dro_1", placeholder="查询", options=cfg.pur + ["All", "Cancel", "Add"]
)
tab9_content = dmc.Stack(
    [
        dmc.Space(),
        dmc.Grid(
            [
                dmc.Col(tab9_dro_1, span=2),
                dmc.Col(tab9_dro_2, span=2),
                dmc.Col(tab9_update_btn, span=1),
                dmc.Col(tab9_cancel_btn, span=1),
                dmc.Col(tab9_download_btn, span=1),
                dmc.Col(tab9_add_btn, span=1),
                dmc.Col(tab9_dept_select, span=4),
            ]
        ),
        html.Div(
            [
                dmc.LoadingOverlay(tab9_table),
                tab9_add_row,
            ]
        ),
        dbc.Modal(id="tab9_modal", centered=True, size="xl"),
    ],
    pt=10,
    pr=5,
    spacing=5,
)

sidebar_items = [
    {
        "key": "0",
        "title": "物料",
        "label": "物料",
        "icon": "material-symbols:home",
        # "href": "/pur",
        "page": "home",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
    },
    {
        "key": "1",
        "title": "新增",
        "label": "新增",
        "icon": "material-symbols:action-key",
        # "href": "/pur#1",
        "page": "#1",
        "status": "1",
        "count": 0,
    },
    {
        "key": "2",
        "title": "处理",
        "label": "处理",
        "icon": "material-symbols:action-key",
        # "href": "/pur#2",
        "page": "#2",
        "status": "2",
        "count": 0,
    },
    {
        "key": "3",
        "title": "查询",
        "label": "查询",
        "icon": "material-symbols:action-key",
        # "href": "/pur#3",
        "page": "#3",
        "status": "3",
        "count": 0,
    },
    {
        "key": "4",
        "title": "调料",
        "label": "调料",
        "icon": "material-symbols:action-key",
        # "href": "/pur#4",
        "page": "#4",
        "status": "4",
        "count": 0,
    },
    {
        "key": "5",
        "title": "安全库存",
        "label": "安全库存",
        "icon": "material-symbols:action-key",
        # "href": "/pur#5",
        "page": "#5",
        "status": "5",
        "count": 0,
    },
    {
        "key": "6",
        "title": "发件箱",
        "label": "发件箱",
        "icon": "material-symbols:action-key",
        # "href": "/pur#6",
        "page": "#6",
        "status": "6",
        "count": 0,
    },
    {
        "key": "7",
        "title": "供应商",
        "label": "供应商",
        "icon": "material-symbols:action-key",
        # "href": "/pur#7",
        "page": "#7",
        "status": "7",
        "count": 0,
    },
    {
        "key": "8",
        "title": "分工职责",
        "label": "分工职责",
        "icon": "material-symbols:action-key",
        # "href": "/pur#8",
        "page": "#8",
        "status": "8",
        "count": 0,
    },
    {
        "key": "9",
        "title": "文档",
        "label": "文档",
        "icon": "material-symbols:action-key",
        # "href": "/pur#9",
        "page": "#9",
        "status": "9",
        "count": 0,
    },
]


def test():
    sql = "select id,start_date,prt_id,checkcode from pur \
        where start_date>=%s and application=%s and pur!=%s \
        and dept_id=%s and prt_id is not null"
    df1 = read_db(sql, params=["2024-01-01", "project", "bo.sm.wang", 21]).with_columns(
        start_date=pl.col("start_date").dt.date(),
        week=pl.col("start_date").dt.week(),
        month=pl.col("start_date").dt.month(),
    )
    sql = "select id as prt_id,smd_count+dip_count as total from prt where id in %s"
    params = [df1["prt_id"].unique().to_list()]
    df2 = read_db(sql, params=params)

    # df2=df2.with_columns(pl.col("count").cast(pl.Int64))
    df1 = (
        df1.group_by(["week", "prt_id"])
        .agg(short=pl.len())
        .join(df2, on="prt_id", how="left")
    )
    df1 = df1.group_by(["week"]).agg(
        short=pl.col("short").sum(),
        total=pl.col("total").sum(),
    )
    df1 = df1.with_columns(
        short_cum=pl.col("short").cum_sum(), total_cum=pl.col("total").cum_sum()
    )
    # dr = pl.date_range(date(2024, 1, 1), date(2024, 12, 31), "1d", eager=True)

    df1 = (
        df1.sort("week")
        .with_columns(
            rate=pl.col("short") / pl.col("total"),
            rate1=pl.col("short_cum") / pl.col("total_cum"),
        )
        .with_columns(ewm_mean=pl.col("rate").ewm_mean(alpha=0.3, ignore_nulls=False))
        .with_columns(roll_mean=pl.col("rate").rolling_mean(4))
        .with_columns(mean=pl.col("rate").mean())
        .with_columns(week=pl.col("week").cast(pl.String))
    )
    df1 = df1.unpivot(["rate", "mean"], index=["week"])
    # print(df1.filter(pl.col("start_date").dt.to_string() == "2025-01-22"))
    # df1 = (
    #     df1.group_by(["start_date"])
    #     .agg(short=pl.col("short").sum(), total=pl.col("total").sum())
    #     .sort("start_date")
    #     .with_columns(
    #         pl.col("short")
    #         .rolling_sum_by("start_date", window_size="1mo")
    #         .alias("rolling_sum_short"),
    #         pl.col("total")
    #         .rolling_sum_by("start_date", window_size="1mo")
    #         .alias("rolling_sum_total"),
    #     )
    #     .with_columns(mean=pl.col("rolling_sum_short") / pl.col("rolling_sum_total"))
    # )
    # print(df1)
    # print(df1.filter(pl.col("start_date").dt.to_string() == "2025-01-22"))

    graph4 = fact.AntdLine(
        # id="pur_home_line",
        data=df1.to_dicts(),
        xField="week",
        yField="value",
        seriesField="variable",
        # isStack=True,
        # smooth=True,
        color=["#f6c022", "#61d9ab"],
        # label={
        #     "position": "left",
        #     "formatter": {"func": "({ value }) => `${(value * 100).toFixed(1)}%`"},
        # },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )


def test1():
    sql = "select id as bom_id,prt_id,date(finish_date) as finish_date \
        from bom_record where finish_date between %s and %s"
    df1 = read_db(sql, params=["2024-01-01", "2024-12-31"])
    dfx = df1.group_by(["prt_id"]).max()
    bom_id = dfx["bom_id"].to_list()
    df1 = df1.filter(pl.col("bom_id").is_in(bom_id))
    sql = "select bom_id,area,checkcode,demand_qty,stock_qty \
        from bom_shortage where bom_id in %s"
    params = [bom_id]
    df2 = read_db(sql, params=params).join(df1, on="bom_id", how="left")
    df2 = df2.group_by(["finish_date", "area", "checkcode"]).agg(
        pl.col("demand_qty").sum(), pl.col("stock_qty").max()
    )
    sql = "select  dest_area as area,checkcode,\
        received_qty as ss_qty,date(mat_receiveddate) as ss_date \
        from pur where application=%s and mat_receiveddate between %s and %s"
    params = ["stock", "2024-01-01", "2024-12-31"]
    pur = read_db(sql, params=params).group_by(["ss_date", "area", "checkcode"]).sum()
    df2 = (
        df2.with_columns(
            ss_qty=pl.struct(["area", "checkcode", "finish_date"]).map_elements(
                lambda x: pur.filter(
                    (pl.col("ss_date") <= x["finish_date"])
                    & (pl.col("area") == x["area"])
                    & (pl.col("checkcode") == x["checkcode"])
                )["ss_qty"].sum(),
                return_dtype=pl.Int64,
            )
        )
        .with_columns(
            rate1=pl.col("stock_qty") > pl.col("demand_qty"),
            rate2=(pl.col("stock_qty") - pl.col("ss_qty")) > pl.col("demand_qty"),
        )
        .with_columns(
            month=pl.col("finish_date").dt.month(),
            week=pl.col("finish_date").dt.week(),
        )
    )
    df2 = (
        df2.group_by(["month"])
        .agg(
            rate1=pl.col("rate1").sum() / pl.len(),
            rate2=pl.col("rate2").sum() / pl.len(),
        )
        .sort("month")
        .with_columns(week=pl.col("month").cast(pl.String))
    ).unpivot(["rate1", "rate2"], index=["month"])
    graph = fact.AntdLine(
        # id="pur_home_line",
        data=df2.to_dicts(),
        xField="month",
        yField="value",
        seriesField="variable",
        # isStack=True,
        # smooth=True,
        color=["#f6c022", "#61d9ab"],
        label={
            "position": "left",
            "formatter": {"func": "({ value }) => `${(value * 100).toFixed(1)}%`"},
        },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )
    return graph


def home_content():
    pur = pur_etd()
    pur1 = pur.filter(pl.col("es_gt_pcb")).with_columns(pl.col("es_date").dt.date())
    table = dag.AgGrid(
        id="pur_home_table",
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "pur", "headerName": "采购"},
            {"field": "prtno", "headerName": "项目号"},
            {"field": "deltapn", "headerName": "料号"},
            {"field": "pcb_date_3d", "headerName": "到板日3天"},
            {"field": "es_date", "headerName": "预计到货日"},
        ],
        rowData=[],
        columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
    )

    sql = "select gmt_create,rate from pur_etd_rate"
    etd = read_db(sql).with_columns(
        day=pl.col("gmt_create").dt.date(),
        week=pl.col("gmt_create").dt.week(),
        rate=pl.col("rate").cast(pl.Float64),
    )
    etd = (
        etd.group_by(["week"])
        .agg(pl.col("rate").mean())
        .with_columns(pl.col("week").cast(pl.String))
        .sort(by="week")
    )

    graph1 = fact.AntdLine(
        id="pur_home_line",
        data=etd.to_dicts(),
        xField="week",
        yField="rate",
        # seriesField="year",
        # isStack=True,
        # smooth=True,
        color="#F58E8E",
        label={
            "position": "left",
            "formatter": {"func": "({ rate }) => `${(rate * 100).toFixed(0)}%`"},
        },
        height=200,
        animation=True,
        point={"shape": "circle"},
    )
    graph2 = fact.AntdBar(
        id="pur_home_bar",
        data=pur1.group_by("pur").count().sort("count", descending=True).to_dicts(),
        xField="count",
        yField="pur",
        # color="#f0884d",
        # yAxis={"grid": None},
        # xAxis={"grid": None},
        # style=style(backgroundColor="white", height="100%", width="100%", padding=10),
        label={"position": "left"},
        height=250,
        # groupField="spec",
        # isPercent=True,
        # padding=10,
        # meta={"日期": {"type": "cat"}},
        # label={"position": "top"},
        # yAxis={"max": 100000},
    )
    graph3 = fact.AntdPie(
        data=pur1.group_by("dept").count().sort("count", descending=True).to_dicts(),
        colorField="dept",
        angleField="count",
        height=250,
        label={"type": "inner", "content": "{percentage}"},
        innerRadius=0.5,
    )
    card = fac.AntdPopupCard(
        table,
        id="pur_home_card",
        title="详细数据",
        visible=False,
        width=700,
        # draggable=True,
        style={
            "bottom": 25,
            "right": 25,
            "position": "fixed",
            # "top": "auto",  # 用于覆盖默认的top: 100px设定
        },
    )

    return fac.AntdFlex(
        [
            fac.AntdTitle("交期达成率走势(到板日3天)", level=5),
            dmc.SegmentedControl(
                id="pur_home_segment",
                color="#15aabf",
                data=[
                    {"value": "day", "label": "Daily"},
                    {"value": "week", "label": "Weekly"},
                    {"value": "month", "label": "Monthly"},
                    {"value": "quarter", "label": "Quarterly"},
                    {"value": "year", "label": "Yearly"},
                ],
                value="week",
                size="xs",
            ),
            graph1,
            fac.AntdFlex(
                [
                    fac.AntdFlex(
                        [
                            fac.AntdTitle("人员分布(交期大于到板日3天)", level=5),
                            fac.AntdTooltip(graph2),
                        ],
                        vertical=True,
                        align="center",
                    ),
                    fac.AntdFlex(
                        [
                            fac.AntdTitle("部门分布(交期大于到板日3天)", level=5),
                            graph3,
                        ],
                        vertical=True,
                        align="center",
                    ),
                ],
                justify="space-between",
                # align="flex-end",
            ),
            # dmc.Group([], grow=True),
            card,
            dcc.Store(id="pur_home_store", data=pur1.to_dicts()),
            # graph4,
        ],
        vertical=True,
    )
    # begin = f"{datetime.now():%Y-01-01}"

    # sql = "select create_date,type,id as pur_id,prt_id,es_date,pur_status\
    #         from ssp.pur_modify where create_date>=%s and type=%s and prt_id is not null"
    # params = [begin, "after"]
    # pur = read_db(sql, params=params)
    # print(pur.unique(subset=["pur_id", "pur_status", "es_date"]).sort("create_date"))
    # sql = "select prt_id,prtno,pur,pur_status as status,checkcode,es_date from pur \
    #     where pur_status not in (%s,%s,%s) \
    #     and application=%s \
    #     and pur!=%s"
    # df = read_db(
    #     sql, params=["cancel", "closed", "received", "project", "bo.sm.wang"]
    # ).with_columns(pl.col("es_date").dt.date())
    # prt_id = df["prt_id"].unique().to_list()

    # print(pur.unique(subset=["pur_id", "es_date"]))
    # pur = pur.join(prt, on="prt_id", how="inner")
    # # print(pur.filter(pl.col("es_date").is_not_null()))

    # # pur1 = pur.filter(pl.col("es_date").is_not_null())
    # pur1 = (
    #     pur.group_by(["pur_id", "create_date", "type"])
    #     .agg(pl.col("es_date").first())
    #     .pivot(
    #         "type",
    #         index=["create_date", "pur_id"],
    #         values="es_date",
    #         aggregate_function="first",
    #     )
    #     .filter(pl.col("before").fill_null("") != pl.col("after").fill_null(""))
    #     .select(["pur_id", "create_date", "after"])
    #     .rename({"create_date": "update_date"})
    # )

    # pur2 = (
    #     pur.unique(subset=["prt_id", "pur_id"])
    #     .select(["prt_id", "pur_id", "pcb_date_3d", "es_date"])
    #     .join(pur1, on="pur_id", how="left")
    # )
    # print(pur2.filter(pl.col("pur_id") == 320669))
    # df = (
    #     df.join(prt, on="prt_id", how="inner").filter(
    #         pl.col("pcb_date_3d").is_not_null()
    #     )
    # ).with_columns((pl.col("es_date") > pl.col("pcb_date_3d")).alias("es_gt_pcb"))
    # print(df)
    # df1 = df.filter(pl.col("es_gt_pcb"))
    # print(df1)
    # df.write_parquet(f"~/pur/{datetime.now().strftime('%Y%m%d%H%M%S')}.parquet")
    # import time

    # t1 = time.perf_counter()
    # dd = pl.scan_parquet("~/pur", include_file_paths="path").collect()
    # print(time.perf_counter() - t1)
    # print(dd)

    # df = (
    #     df.with_columns(
    #         [pl.col("status").str.to_titlecase(), pl.col("pur").str.to_titlecase()]
    #     )
    #     .group_by(["pur", "status"])
    #     .agg(pl.col("checkcode").unique().len())
    #     .with_columns(pl.col("checkcode").sum().over(["pur"]).alias("count"))
    #     .sort(["count", "status"])
    # )
    # div = fact.AntdColumn(
    #     data=df.to_dicts(),
    #     xField="pur",
    #     yField="checkcode",
    #     seriesField="status",
    #     isStack=True,
    #     label={"position": "middle"},
    #     style={"padding": "10px 0px 0px 0px"},
    # )
    table = dag.AgGrid(
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "pur", "headerName": "采购"},
            {"field": "prtno", "headerName": "项目号"},
            {"field": "checkcode", "headerName": "料号"},
            {"field": "pcb_date_3d", "headerName": "到板日3天"},
            {"field": "es_date", "headerName": "预计到货日"},
        ],
        rowData=df1.to_dicts(),
        columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
    )
    return table


def layout(user, **kwargs):
    layout = html.Div(
        [
            fac.AntdTabs(
                items=[
                    {
                        "key": "0",
                        "label": fac.AntdSpace(
                            [fac.AntdIcon(icon="antd-home"), "物料"],
                            style={
                                "font-weight": "bolder",
                                "color": "rgb(0, 159, 232)",
                            },
                        ),
                        "children": home_content(),
                    },
                    {
                        "key": "1",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "新增"],
                        "children": tab1_content,
                    },
                    {
                        "key": "2",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "处理"],
                        "children": tab2_content(user),
                    },
                    {
                        "key": "3",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "查询"],
                        "children": tab3_content,
                    },
                    {
                        "key": "4",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "调料"],
                        "children": tab4_content,
                    },
                    {
                        "key": "5",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "安全库存"],
                        "children": tab9_content,
                    },
                    {
                        "key": "6",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "发件箱"],
                        "children": tab5_content,
                    },
                    {
                        "key": "7",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "供应商"],
                        "children": tab6_content,
                    },
                    {
                        "key": "8",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "分工职责"],
                        "children": tab7_content,
                    },
                    {
                        "key": "9",
                        "label": [fac.AntdIcon(icon="antd-caret-right"), "文档"],
                        "children": tab8_content,
                    },
                ],
                id="pur_tabs",
                tabPosition="left",
                tabBarGutter=0,
                activeKey="0",
                # centered=True,
            ),
            pur_download,
            tab5_mail_store,
            tab3_table_row_ids_store,
            tab3_table_store,
        ],
    )
    return layout
