from datetime import datetime
import dash_bootstrap_components as dbc
import numpy as np
import plotly.graph_objects as go
from config import pool
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dash,
    dcc,
    html,
    no_update,
)
from dash_iconify import DashIconify
from common import read_sql

dash.register_page(__name__, title="满意度")
item1 = dbc.ListGroupItem(
    [
        html.Embed(
            src="/assets/img/pur_icon.svg",
            height="32px",
            width="32px",
            style={"align-self": "center"},
            className="ml-3",
        ),
        html.Span(
            "采购",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-1"),
                html.Li("\U0000e650", className="rate-star", id="star-2-1"),
                html.Li("\U0000e650", className="rate-star", id="star-3-1"),
                html.Li("\U0000e650", className="rate-star", id="star-4-1"),
                html.Li("\U0000e650", className="rate-star", id="star-5-1"),
            ],
            className="cleanfloat rate-star",
            id="star-all-1",
        ),
        html.Span(
            "",
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-1",
        ),
        html.Span(
            "",
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-1",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-1",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse1 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-a ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-1",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-1",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-a ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-2",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-2",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-a ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-3",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-3",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-a ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-4",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-4",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-1"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-1",
)
item2 = dbc.ListGroupItem(
    [
        html.Embed(
            src="/assets/img/ce_icon.svg",
            height="32px",
            width="32px",
            style={"align-self": "center"},
            className="ml-3",
        ),
        html.Span(
            "材料工程",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-2"),
                html.Li("\U0000e650", className="rate-star", id="star-2-2"),
                html.Li("\U0000e650", className="rate-star", id="star-3-2"),
                html.Li("\U0000e650", className="rate-star", id="star-4-2"),
                html.Li("\U0000e650", className="rate-star", id="star-5-2"),
            ],
            className="cleanfloat rate-star",
            id="star-all-2",
        ),
        html.Span(
            "",
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-2",
        ),
        html.Span(
            "",
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-2",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-2",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse2 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-b ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-6",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-6",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-b ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-7",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-7",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-b ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-8",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-8",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-b ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-9",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-9",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-2"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-2",
)
item3 = dbc.ListGroupItem(
    [
        html.Embed(
            src="/assets/img/spec_icon.svg",
            height="32px",
            width="32px",
            style={"align-self": "center"},
            className="ml-3",
        ),
        html.Span(
            "规格",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-3"),
                html.Li("\U0000e650", className="rate-star", id="star-2-3"),
                html.Li("\U0000e650", className="rate-star", id="star-3-3"),
                html.Li("\U0000e650", className="rate-star", id="star-4-3"),
                html.Li("\U0000e650", className="rate-star", id="star-5-3"),
            ],
            className="cleanfloat rate-star",
            id="star-all-3",
        ),
        html.Span(
            "",
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-3",
        ),
        html.Span(
            "",
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-3",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-3",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse3 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-c ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-11",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-11",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-c ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-12",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-12",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-c ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-13",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-13",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-c ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-14",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-14",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-3"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-3",
)
item4 = dbc.ListGroupItem(
    [
        html.Embed(
            src="/assets/img/sm_icon.svg",
            height="32px",
            width="32px",
            style={"align-self": "center"},
            className="ml-3",
        ),
        html.Span(
            "样制",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-4"),
                html.Li("\U0000e650", className="rate-star", id="star-2-4"),
                html.Li("\U0000e650", className="rate-star", id="star-3-4"),
                html.Li("\U0000e650", className="rate-star", id="star-4-4"),
                html.Li("\U0000e650", className="rate-star", id="star-5-4"),
            ],
            className="cleanfloat rate-star",
            id="star-all-4",
        ),
        html.Span(
            "",
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-4",
        ),
        html.Span(
            "",
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-4",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-4",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse4 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-d ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-16",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-16",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-d ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-17",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-17",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-d ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-18",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-18",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-d ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-19",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-19",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-4"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-4",
)
item5 = dbc.ListGroupItem(
    [
        html.Embed(
            src="/assets/img/cross_icon.svg",
            height="32px",
            width="32px",
            style={"align-self": "center"},
            className="ml-3",
        ),
        html.Span(
            "设备仪校与维修",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-5"),
                html.Li("\U0000e650", className="rate-star", id="star-2-5"),
                html.Li("\U0000e650", className="rate-star", id="star-3-5"),
                html.Li("\U0000e650", className="rate-star", id="star-4-5"),
                html.Li("\U0000e650", className="rate-star", id="star-5-5"),
            ],
            className="cleanfloat rate-star",
            id="star-all-5",
        ),
        html.Span(
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-5",
        ),
        html.Span(
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-5",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-5",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse5 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-e ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-21",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-21",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-e ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-22",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-22",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-e ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-23",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-23",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-e ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-24",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-24",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-5"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-5",
)

item8 = dbc.ListGroupItem(
    [
        DashIconify(
            icon="carbon:tool-kit",
            width=30,
            color="orange",
            style={"align-self": "center"},
        ),
        html.Span(
            "材料与工具领用",
            style={
                "font-size": "20px",
                "align-self": "center",
                "color": "#2c3e50",
                "font-weight": "bold",
                "width": "140px",
            },
            className="ml-2",
        ),
        html.Ul(
            [
                html.Li("\U0000e650", className="rate-star", id="star-1-6"),
                html.Li("\U0000e650", className="rate-star", id="star-2-6"),
                html.Li("\U0000e650", className="rate-star", id="star-3-6"),
                html.Li("\U0000e650", className="rate-star", id="star-4-6"),
                html.Li("\U0000e650", className="rate-star", id="star-5-6"),
            ],
            className="cleanfloat rate-star",
            id="star-all-6",
        ),
        html.Span(
            style={"font-size": "25px", "align-self": "center"},
            className="ml-5",
            id="emoji-result-6",
        ),
        html.Span(
            style={"font-size": "20px", "align-self": "center"},
            className="ml-2",
            id="word-result-6",
        ),
        dbc.Button(
            "更多建议",
            style={"align-self": "center", "margin-left": "auto"},
            color="primary",
            className="mr-3",
            id="star-btn-6",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
collapse8 = dbc.Collapse(
    [
        dbc.ListGroupItem(
            [
                html.Span("\U0000e628", className="satisfy-sub-icon-e ml-3"),
                html.Span("交期", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("交期严重延期", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-31",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("交期及时", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-31",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62a", className="satisfy-sub-icon-e ml-3"),
                html.Span("响应速度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("没有任何回馈", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-32",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("响应速度快", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-32",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62c", className="satisfy-sub-icon-e ml-3"),
                html.Span("服务态度", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li("配合意识淡泊", className="dislike-font"),
                    ],
                    className="dislike-all",
                    id="dislike-click-33",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li("服务态度特别好", className="like-font"),
                    ],
                    className="like-all",
                    id="like-click-33",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                html.Span("\U0000e62f", className="satisfy-sub-icon-e ml-3"),
                html.Span("专业能力", className="satisfy-sub-font ml-2"),
                html.Ul(
                    [
                        html.Li("\U0000e607", className="dislike-style"),
                        html.Li(
                            "专业能力欠缺，工作信息不清楚", className="dislike-font"
                        ),
                    ],
                    className="dislike-all",
                    id="dislike-click-34",
                ),
                html.Ul(
                    [
                        html.Li("\U0000e62e", className="like-style"),
                        html.Li(
                            "专业能力强，信息全面，能给出合理建议",
                            className="like-font",
                        ),
                    ],
                    className="like-all",
                    id="like-click-34",
                ),
            ],
            style={"display": "flex"},
            className="py-2",
        ),
        dbc.ListGroupItem(
            [
                dbc.Textarea(
                    value="", placeholder="更多建议请在这里输入", id="advice-6"
                ),
            ],
            className="px-4",
        ),
    ],
    id="star-collapse-6",
)

item6 = dbc.ListGroupItem(
    [
        dbc.Button(
            "提交评价",
            style={"align-self": "center", "width": "100%"},
            color="primary",
            className="m-3",
            id="submit-rate",
        ),
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
item7 = dbc.ListGroupItem(
    [
        dbc.Alert(
            id="submit-alert",
            duration=3000,
            is_open=False,
            style={"width": "100%"},
            className="m-3",
        )
    ],
    style={"display": "flex"},
    action=False,
    className="p-0",
)
card = dbc.Card(
    [
        dbc.CardHeader(
            "SUP部门满意度调查",
            style={"font-size": "22px", "color": "white"},
            id="header-record",
        ),
        dbc.ListGroup(
            [
                item1,
                collapse1,
                item2,
                collapse2,
                item3,
                collapse3,
                item4,
                collapse4,
                item5,
                collapse5,
                item8,
                collapse8,
                item6,
                item7,
            ],
            flush=True,
        ),
    ],
    color="primary",
    className="mb-2",
)
# format_card1 = dbc.CardColumns(
#     [card],className='card-columns px-3'
# )
animals = ["PUR", "CE", "SPEC", "SM", "CROSS"]

fig = go.Figure(
    data=[
        go.Bar(
            name="2分",
            x=animals,
            y=np.log([1000, 30, 10, 10, 2]),
            marker_color="#264653",
        ),
        go.Bar(
            name="4分",
            x=animals,
            y=np.log([30, 20, 20, 40, 20]),
            marker_color="#2a9d8f",
        ),
        go.Bar(
            name="6分",
            x=animals,
            y=np.log([10, 10, 10, 20, 40]),
            marker_color="#e9c46a",
        ),
        go.Bar(
            name="8分",
            x=animals,
            y=np.log([10, 30, 10, 10, 10]),
            marker_color="#f4a261",
        ),
        go.Bar(
            name="10分",
            x=animals,
            y=np.log([100, 9.56, 49.78, 19.69, 9.77]),
            marker_color="#e76f51",
        ),
    ]
)

# Change the bar mode
fig.update_layout(title_text="满意度调查结果", barmode="stack")

fig1 = go.Figure(
    data=[go.Scatter(y=[1, 3, 2], line=dict(color="crimson"))],
    layout=dict(
        title=dict(text="A Graph Object Figure With Magic Underscore Notation")
    ),
)

# Change the bar mode
fig1.update_layout(title_text="满意度调查结果", barmode="stack")


result_vis = html.Div(
    [
        html.Div(
            [
                dbc.Row(
                    [
                        dbc.Col(
                            [
                                html.Div(
                                    [
                                        html.P(
                                            "满意度评分",
                                            style={
                                                "font-size": "15px",
                                                "vertical-align": "middle",
                                            },
                                            className="mb-0 mt-4",
                                        ),
                                        html.P(
                                            "",
                                            style={
                                                "font-size": "50px",
                                                "vertical-align": "middle",
                                            },
                                            className="mt-0",
                                            id="sa-final-score",
                                        ),
                                    ],
                                    className="sa-circle",
                                ),
                            ],
                            width=2,
                        ),
                        dbc.Col(
                            [
                                html.Div(
                                    [
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Div(
                                            style={
                                                "width": "0%",
                                                "float": "left",
                                                "vertical-align": "middle",
                                            },
                                            className="progress-all ml-3",
                                            id="sa-result-1",
                                        ),
                                        html.Span(
                                            ["100%"],
                                            style={"float": "left"},
                                            className="ml-2",
                                            id="sa-percent-1",
                                        ),
                                    ],
                                    className="progress-con",
                                    style={"clear": "both"},
                                ),
                                # 100 is 80%,original rate*0.80
                                html.Div(
                                    [
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Div(
                                            style={
                                                "width": "0%",
                                                "float": "left",
                                                "vertical-align": "middle",
                                            },
                                            className="progress-all ml-3",
                                            id="sa-result-2",
                                        ),
                                        html.Span(
                                            ["90%"],
                                            style={"float": "left"},
                                            className="ml-2",
                                            id="sa-percent-2",
                                        ),
                                    ],
                                    className="progress-con mb-0",
                                    style={"clear": "both"},
                                ),
                                html.Div(
                                    [
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Div(
                                            style={
                                                "width": "0%",
                                                "float": "left",
                                                "vertical-align": "middle",
                                            },
                                            className="progress-all ml-3",
                                            id="sa-result-3",
                                        ),
                                        html.Span(
                                            ["90%"],
                                            style={"float": "left"},
                                            className="ml-2",
                                            id="sa-percent-3",
                                        ),
                                    ],
                                    className="progress-con",
                                    style={"clear": "both"},
                                ),
                                html.Div(
                                    [
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Div(
                                            style={
                                                "width": "0%",
                                                "float": "left",
                                                "vertical-align": "middle",
                                            },
                                            className="progress-all ml-3",
                                            id="sa-result-4",
                                        ),
                                        html.Span(
                                            ["60%"],
                                            style={"float": "left"},
                                            className="ml-2",
                                            id="sa-percent-4",
                                        ),
                                    ],
                                    className="progress-con",
                                    style={"clear": "both"},
                                ),
                                html.Div(
                                    [
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="wh-star",
                                        ),
                                        html.Span(
                                            ["\U0000e650"],
                                            style={"float": "left"},
                                            className="sa-star",
                                        ),
                                        html.Div(
                                            style={
                                                "width": "0%",
                                                "float": "left",
                                                "vertical-align": "middle",
                                            },
                                            className="progress-all ml-3",
                                            id="sa-result-5",
                                        ),
                                        html.Span(
                                            ["1%"],
                                            style={"float": "left"},
                                            className="ml-2",
                                            id="sa-percent-5",
                                        ),
                                    ],
                                    className="progress-con",
                                    style={"clear": "both"},
                                ),
                            ],
                            width=8,
                        ),
                        dbc.Col(
                            [
                                dcc.Dropdown(
                                    placeholder="select year",
                                    id="sa-select-1",
                                    value=2020,
                                ),
                                dcc.Dropdown(
                                    placeholder="select month",
                                    id="sa-select-2",
                                    className="mt-2",
                                    value=11,
                                ),
                                dcc.Dropdown(
                                    placeholder="select group",
                                    id="sa-select-3",
                                    options=[
                                        {"label": "ALL", "value": "ALL"},
                                        {"label": "PUR", "value": "PUR"},
                                        {"label": "CE", "value": "CE"},
                                        {"label": "SPEC", "value": "SPEC"},
                                        {"label": "SM", "value": "SM"},
                                        {"label": "CROSS", "value": "CROSS"},
                                    ],
                                    className="mt-2",
                                    value="ALL",
                                ),
                            ],
                            width=2,
                        ),
                    ]
                ),
            ]
        ),
        dcc.Graph(
            id="example-graph-1",
        ),
        # dcc.Graph(
        #     id='example-graph-2',
        #     figure=fig1,
        # )
    ]
)
data_init = {
    "click-1": None,
    "click-2": None,
    "click-3": None,
    "click-4": None,
    "click-5": None,
    "click-6": None,
    "click-7": None,
    "click-8": None,
    "click-9": None,
    "click-10": None,
    "click-11": None,
    "click-12": None,
    "click-13": None,
    "click-14": None,
    "click-15": None,
    "click-16": None,
    "click-17": None,
    "click-18": None,
    "click-19": None,
    "click-20": None,
    "click-21": None,
    "click-22": None,
    "click-23": None,
    "click-24": None,
    "click-25": None,
}


def layout(**kwargs):
    return dbc.Container([dcc.Store(data=data_init, id="record-return")], id="s-layout")


@callback(
    Output("s-layout", "children"),
    [Input("user", "data")],
    [State("s-layout", "children")],
    prevent_initial_call=False,
)
def often_layout(user, ch):
    dept = user.get("dept")
    if dept == "SUP_SUP":
        ch.append(result_vis)
        return ch
    else:
        ch.append(card)
        return ch


for i in range(1, 7):
    clientside_callback(
        ClientsideFunction(namespace="clientside", function_name="star_hover"),
        Output(f"star-all-{i}", "style"),
        [
            Input(f"star-all-{i}", "id"),
            Input(f"emoji-result-{i}", "id"),
            Input(f"word-result-{i}", "id"),
        ],
        prevent_initial_call=False,
    )

for i in range(1, 7):

    @callback(
        Output(f"word-result-{i}", "children"),
        [
            Input(f"star-1-{i}", "n_clicks"),
            Input(f"star-2-{i}", "n_clicks"),
            Input(f"star-3-{i}", "n_clicks"),
            Input(f"star-4-{i}", "n_clicks"),
            Input(f"star-5-{i}", "n_clicks"),
        ],
        prevent_initial_call=False,
    )
    def final_record(n1, n2, n3, n4, n5):
        ctx = dash.callback_context
        id = ctx.triggered[0]["prop_id"].split(".")[0]
        if id in [
            "star-1-1",
            "star-1-2",
            "star-1-3",
            "star-1-4",
            "star-1-5",
            "star-1-6",
        ]:
            return "非常不满意！！"
        elif id in [
            "star-2-1",
            "star-2-2",
            "star-2-3",
            "star-2-4",
            "star-2-5",
            "star-2-6",
        ]:
            return "不满意！"
        elif id in [
            "star-3-1",
            "star-3-2",
            "star-3-3",
            "star-3-4",
            "star-3-5",
            "star-3-6",
        ]:
            return "一般。"
        elif id in [
            "star-4-1",
            "star-4-2",
            "star-4-3",
            "star-4-4",
            "star-4-5",
            "star-4-6",
        ]:
            return "满意！"
        elif id in [
            "star-5-1",
            "star-5-2",
            "star-5-3",
            "star-5-4",
            "star-5-5",
            "star-5-6",
        ]:
            return "非常满意！！"
        else:
            raise PreventUpdate


for i in range(1, 7):

    @callback(
        Output(f"star-collapse-{i}", "is_open"),
        [Input(f"star-btn-{i}", "n_clicks")],
        [State(f"star-collapse-{i}", "is_open")],
    )
    def toggle_star_collapse(n, is_open):
        if n:
            return not is_open
        return is_open


for i in range(1, 35):
    clientside_callback(
        ClientsideFunction(namespace="clientside", function_name="like_click"),
        Output(f"like-click-{i}", "style"),
        [Input(f"dislike-click-{i}", "id"), Input(f"like-click-{i}", "id")],
        prevent_initial_call=False,
    )


def like_dislike(id, data):
    l_id = id.split("-", 1)
    s1 = l_id[0]
    s2 = l_id[1]
    if s1 == "dislike":
        data[s2] = 0
    elif s1 == "like":
        data[s2] = 1
    else:
        data[s2] = 2
    return data


@callback(
    Output("record-return", "data"),
    [
        Input("dislike-click-1", "n_clicks"),
        Input("dislike-click-2", "n_clicks"),
        Input("dislike-click-3", "n_clicks"),
        Input("dislike-click-4", "n_clicks"),
        Input("dislike-click-6", "n_clicks"),
        Input("dislike-click-7", "n_clicks"),
        Input("dislike-click-8", "n_clicks"),
        Input("dislike-click-9", "n_clicks"),
        Input("dislike-click-11", "n_clicks"),
        Input("dislike-click-12", "n_clicks"),
        Input("dislike-click-13", "n_clicks"),
        Input("dislike-click-14", "n_clicks"),
        Input("dislike-click-16", "n_clicks"),
        Input("dislike-click-17", "n_clicks"),
        Input("dislike-click-18", "n_clicks"),
        Input("dislike-click-19", "n_clicks"),
        Input("dislike-click-21", "n_clicks"),
        Input("dislike-click-22", "n_clicks"),
        Input("dislike-click-23", "n_clicks"),
        Input("dislike-click-24", "n_clicks"),
        Input("dislike-click-31", "n_clicks"),
        Input("dislike-click-32", "n_clicks"),
        Input("dislike-click-33", "n_clicks"),
        Input("dislike-click-34", "n_clicks"),
        Input("like-click-1", "n_clicks"),
        Input("like-click-2", "n_clicks"),
        Input("like-click-3", "n_clicks"),
        Input("like-click-4", "n_clicks"),
        Input("like-click-6", "n_clicks"),
        Input("like-click-7", "n_clicks"),
        Input("like-click-8", "n_clicks"),
        Input("like-click-9", "n_clicks"),
        Input("like-click-11", "n_clicks"),
        Input("like-click-12", "n_clicks"),
        Input("like-click-13", "n_clicks"),
        Input("like-click-14", "n_clicks"),
        Input("like-click-16", "n_clicks"),
        Input("like-click-17", "n_clicks"),
        Input("like-click-18", "n_clicks"),
        Input("like-click-19", "n_clicks"),
        Input("like-click-21", "n_clicks"),
        Input("like-click-22", "n_clicks"),
        Input("like-click-23", "n_clicks"),
        Input("like-click-24", "n_clicks"),
        Input("like-click-31", "n_clicks"),
        Input("like-click-32", "n_clicks"),
        Input("like-click-33", "n_clicks"),
        Input("like-click-34", "n_clicks"),
    ],
    [State("record-return", "data")],
    prevent_initial_call=False,
)
def click_resulat(
    d1,
    d2,
    d3,
    d4,
    d6,
    d7,
    d8,
    d9,
    d11,
    d12,
    d13,
    d14,
    d16,
    d17,
    d18,
    d19,
    d21,
    d22,
    d23,
    d24,
    d31,
    d32,
    d33,
    d34,
    l1,
    l2,
    l3,
    l4,
    l6,
    l7,
    l8,
    l9,
    l11,
    l12,
    l13,
    l14,
    l16,
    l17,
    l18,
    l19,
    l21,
    l22,
    l23,
    l24,
    l31,
    l32,
    l33,
    l34,
    data,
):
    ctx = dash.callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if id.split("-", 2):
        l_id = id.split("-", 2)
        s1 = l_id[0]
        if s1 == "like" or s1 == "dislike":
            # print(like_dislike(id,data))
            return like_dislike(id, data)
        else:
            raise PreventUpdate
    else:
        raise PreventUpdate


def divert_record(w):
    if w == "非常不满意！！":
        return 2
    elif w == "不满意！":
        return 4
    elif w == "一般。":
        return 6
    elif w == "满意！":
        return 8
    elif w == "非常满意！！":
        return 10
    else:
        return 0


@callback(
    Output("submit-rate", "color"),
    Output("submit-alert", "children"),
    Output("submit-alert", "color"),
    Output("submit-alert", "is_open"),
    Output("submit-rate", "disabled"),
    Output("submit-rate", "children"),
    Input("submit-rate", "n_clicks"),
    State("word-result-1", "children"),
    State("word-result-2", "children"),
    State("word-result-3", "children"),
    State("word-result-4", "children"),
    State("word-result-5", "children"),
    State("word-result-6", "children"),
    State("user", "data"),
    State("record-return", "data"),
    State("advice-1", "value"),
    State("advice-2", "value"),
    State("advice-3", "value"),
    State("advice-4", "value"),
    State("advice-5", "value"),
    State("advice-6", "value"),
    prevent_initial_call=False,
)
def submit_rate(n, w1, w2, w3, w4, w5, w6, data, data1, ad1, ad2, ad3, ad4, ad5, ad6):
    if not n:
        raise PreventUpdate
    if not (w1 and w2 and w3 and w4 and w5 and w6):
        return "danger", "评分不能为空", "danger", True, False, no_update

    owner = data.get("nt_name")
    dept = data.get("dept")
    record1 = divert_record(w1)
    record2 = divert_record(w2)
    record3 = divert_record(w3)
    record4 = divert_record(w4)
    record5 = divert_record(w5)
    record6 = divert_record(w6)
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    a1 = data1.get("click-1")
    a2 = data1.get("click-2")
    a3 = data1.get("click-3")
    a4 = data1.get("click-4")
    a5 = data1.get("click-5")
    a6 = data1.get("click-6")
    a7 = data1.get("click-7")
    a8 = data1.get("click-8")
    a9 = data1.get("click-9")
    a10 = data1.get("click-10")
    a11 = data1.get("click-11")
    a12 = data1.get("click-12")
    a13 = data1.get("click-13")
    a14 = data1.get("click-14")
    a15 = data1.get("click-15")
    a16 = data1.get("click-16")
    a17 = data1.get("click-17")
    a18 = data1.get("click-18")
    a19 = data1.get("click-19")
    a20 = data1.get("click-20")
    a21 = data1.get("click-21")
    a22 = data1.get("click-22")
    a23 = data1.get("click-23")
    a24 = data1.get("click-24")
    a25 = data1.get("click-25")
    a31 = data1.get("click-31")
    a32 = data1.get("click-32")
    a33 = data1.get("click-33")
    a34 = data1.get("click-34")
    a35 = data1.get("click-35")

    year = datetime.now().year
    month = datetime.now().month

    params1 = [owner, year, month]
    sql1 = "select id from ssp.satisfaction \
        where 投诉人=%s and year(投诉日期)=%s and month(投诉日期)=%s"
    df = read_sql(sql1, params=params1)

    if df.empty:
        sql = "insert into ssp.satisfaction\
            (投诉人部门,得分ID,评价类型,投诉日期,交期,响应速度,服务态度,\
                专业能力,配合资源,更多建议,投诉人,被投诉部门)\
            values(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
        params = [
            [dept, record1, "满意度", now, a1, a2, a3, a4, a5, ad1, owner, "PUR"],
            [dept, record2, "满意度", now, a6, a7, a8, a9, a10, ad2, owner, "CE"],
            [dept, record3, "满意度", now, a11, a12, a13, a14, a15, ad3, owner, "SPEC"],
            [dept, record4, "满意度", now, a16, a17, a18, a19, a20, ad4, owner, "SM"],
            [
                dept,
                record5,
                "满意度",
                now,
                a21,
                a22,
                a23,
                a24,
                a25,
                ad5,
                owner,
                "DEVICE",
            ],
            [
                dept,
                record6,
                "满意度",
                now,
                a31,
                a32,
                a33,
                a34,
                a35,
                ad6,
                owner,
                "MATERIAL",
            ],
        ]
    else:
        sql = "update ssp.satisfaction set 投诉人部门=%s,得分ID=%s,交期=%s,\
            响应速度=%s,服务态度=%s,专业能力=%s,配合资源=%s,更多建议=%s \
                where 投诉人=%s and 被投诉部门=%s and year(投诉日期)=%s and month(投诉日期)=%s"
        params = [
            [dept, record1, a1, a2, a3, a4, a5, ad1, owner, "PUR", year, month],
            [dept, record2, a6, a7, a8, a9, a10, ad2, owner, "CE", year, month],
            [dept, record3, a11, a12, a13, a14, a15, ad3, owner, "SPEC", year, month],
            [dept, record4, a16, a17, a18, a19, a20, ad4, owner, "SM", year, month],
            [dept, record5, a21, a22, a23, a24, a25, ad5, owner, "DEVICE", year, month],
            [
                dept,
                record6,
                a31,
                a32,
                a33,
                a34,
                a35,
                ad6,
                owner,
                "MATERIAL",
                year,
                month,
            ],
        ]

    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.executemany(sql, params)
            conn.commit()

    return "success", "评分成功！", "success", True, True, "评分成功"


@callback(
    Output("sa-select-1", "options"),
    [Input("user", "data")],
    prevent_initial_call=False,
)
def generate_date(user):
    params = []
    sql = "select distinct year(投诉日期) from ssp.satisfaction"
    df = read_sql(sql, params=params)
    sa_year = df["year(投诉日期)"]
    options = [{"label": i, "value": i} for i in sa_year]
    return options


@callback(
    Output("sa-select-2", "options"),
    [Input("sa-select-1", "value")],
    prevent_initial_call=False,
)
def generate_date(v):
    params = [v]
    sql = (
        "select distinct month(投诉日期) from ssp.satisfaction where year(投诉日期)=%s"
    )
    df = read_sql(sql, params=params)

    sa_mon = df["month(投诉日期)"]
    options = [{"label": i, "value": i} for i in sa_mon]
    return options


@callback(
    [
        Output("sa-result-1", "style"),
        Output("sa-result-2", "style"),
        Output("sa-result-3", "style"),
        Output("sa-result-4", "style"),
        Output("sa-result-5", "style"),
        Output("sa-percent-1", "children"),
        Output("sa-percent-2", "children"),
        Output("sa-percent-3", "children"),
        Output("sa-percent-4", "children"),
        Output("sa-percent-5", "children"),
        Output("sa-final-score", "children"),
    ],
    [
        Input("sa-select-1", "value"),
        Input("sa-select-2", "value"),
        Input("sa-select-3", "value"),
    ],
    [
        State("sa-result-1", "style"),
        State("sa-result-2", "style"),
        State("sa-result-3", "style"),
        State("sa-result-4", "style"),
        State("sa-result-5", "style"),
    ],
    prevent_initial_call=False,
)
def often_layout(v1, v2, v3, s10, s8, s6, s4, s2):
    if v1 and v2 and v3:
        if v3 == "ALL":
            params = [v1, v2, "SUP"]
            sql = "select 得分ID from ssp.satisfaction where year(投诉日期)=%s and month(投诉日期)=%s and 投诉人部门!=%s"
            df = read_sql(sql, params=params)
        else:
            params = [v1, v2, "SUP", v3]
            sql = "select 得分ID from ssp.satisfaction where year(投诉日期)=%s and month(投诉日期)=%s and 投诉人部门!=%s and 被投诉部门=%s"
            df = read_sql(sql, params=params)
        df = df[df.isin(["2", "4", "6", "8", "10"])]
        df = df.dropna(subset=["得分ID"])
        # df = df.drop(df[aaa].index)
        # print(df[df['得分ID'].isnull()])
        # print(df.loc[:,'得分ID'])
        score = df.loc[:, "得分ID"].value_counts()
        df["得分ID"] = df["得分ID"].astype(int)
        total = df["得分ID"].sum()
        coun = df["得分ID"].count()
        final_score = round(total / coun, 2)
        if "2" in score.index:
            s2["width"] = format(0.7 * score["2"] * 2 / total, ".2%")
            p2 = format(score["2"] * 2 / total, ".2%")
        else:
            s2["width"] = 0
            p2 = 0
        if "4" in score.index:
            s4["width"] = format(0.7 * score["4"] * 4 / total, ".2%")
            p4 = format(score["4"] * 4 / total, ".2%")
        else:
            s4["width"] = 0
            p4 = 0
        if "6" in score.index:
            s6["width"] = format(0.7 * score["6"] * 6 / total, ".2%")
            p6 = format(score["6"] * 6 / total, ".2%")
        else:
            s6["width"] = 0
            p6 = 0
        if "8" in score.index:
            s8["width"] = format(0.7 * score["8"] * 8 / total, ".2%")
            p8 = format(score["8"] * 8 / total, ".2%")
        else:
            s8["width"] = 0
            p8 = 0
        if "10" in score.index:
            s10["width"] = format(0.7 * score["10"] * 10 / total, ".2%")
            p10 = format(score["10"] * 10 / total, ".2%")
        else:
            s10["width"] = 0
            p10 = 0
        return s10, s8, s6, s4, s2, p10, p8, p6, p4, p2, final_score
    else:
        raise PreventUpdate


@callback(
    Output("example-graph-1", "figure"),
    [
        Input("sa-select-1", "value"),
        Input("sa-select-2", "value"),
        Input("sa-select-3", "value"),
    ],
    prevent_initial_call=False,
)
def generate_date(v1, v2, v3):
    if v1 and v2 and v3:
        if v3 == "ALL":
            params = [v1, v2, "SUP"]
            sql = "select 投诉人部门,得分ID from ssp.satisfaction where year(投诉日期)=%s and month(投诉日期)=%s and 投诉人部门!=%s"
        else:
            params = [v1, v2, "SUP", v3]
            sql = "select 投诉人部门,得分ID from ssp.satisfaction where year(投诉日期)=%s and month(投诉日期)=%s and 投诉人部门!=%s and 被投诉部门=%s"
        df = read_sql(sql, params=params)
        df = df[df.得分ID.isin(["2", "4", "6", "8", "10"])]
        df["得分ID"] = df["得分ID"].astype(int)
        ave = df.groupby("投诉人部门").mean()["得分ID"].round(decimals=2)
        ave = ave.sort_values()
        dept = ave.index.tolist()
        ave = ave.tolist()
        data = [go.Bar(name="平均分", x=dept, y=ave, marker_color="#1abc9c")]
        fig = go.Figure(data=data)
        fig.update_layout(title_text="满意度调查结果", barmode="stack")
        return fig
    else:
        raise PreventUpdate
