from datetime import datetime

import dash
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash_extensions.enrich import Input, Output, State, callback, html

from config import pool
from tasks import bg_mail

dash.register_page(__name__, title="建议")

content = dmc.Stack(
    [
        dmc.Text("感谢您的反馈与建议,请在下方留言", weight="bold", size="xl"),
        dmc.Textarea(id="message"),
        dmc.Group(dmc.Button("提交", id="message-submit"), position="right"),
        html.Div(id="message-show"),
    ]
)

layout = dmc.Container(content, fluid=True)


# ---------回调函数----------------
@callback(
    Output("message-show", "children"),
    Output("message", "value"),
    Input("message-submit", "n_clicks"),
    State("message", "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def submit_message(n, message, user):
    with pool.connection() as conn:
        with conn.cursor() as cu:
            if message:
                sql = "insert into ssp.notice(gmt_create,type,notice,sender)\
                    value(%s,%s,%s,%s)"
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                nt_name = user.get("nt_name").title()
                sql = "insert into ssp.notice(gmt_create,type,notice,sender)\
                    value(%s,%s,%s,%s)"
                params = [now, "message", message, nt_name]
                cu.execute(sql, params)
                conn.commit()
                to = f"{nt_name}@deltaww.com;<EMAIL>;<EMAIL>"
                bg_mail(to, "SSP问题反馈", message)

            sql = "select gmt_create,notice,sender from ssp.notice order by gmt_create desc"
            cu.execute(sql)
            res = cu.fetchall()

    msg = [
        fac.AntdComment(
            authorName="Weiming.Li",
            publishTime={"value": ""},
            commentContent=dmc.Stack(
                [
                    fac.AntdText("无法安全下载解决办法:"),
                    fac.AntdImage(
                        src="/assets/img/无法安全下载.gif",
                        height="231px",
                        width="376.5px",
                    ),
                ]
            ),
        )
    ]
    for i in res:
        div = fac.AntdComment(
            authorName=i["sender"],
            publishTime={"value": i["gmt_create"]},
            commentContent=i["notice"],
        )
        msg.append(div)
    return msg, ""
