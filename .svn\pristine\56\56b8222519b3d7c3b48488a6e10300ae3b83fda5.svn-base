from pocketflow import Flow, Node

from ..utils import call_llm, call_embedding
import lancedb
from pathlib import Path
import pandas as pd

db = lancedb.connect(Path.home() / "lancedb")


class QueryNode(Node):
    def prep(self, shared):
        return shared["prompt"]

    def exec(self, prompt):
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res


class EmbedNode(Node):
    def prep(self, shared):
        return shared["result"], shared["df"]

    def exec(self, prep_res):
        result, df = prep_res
        df1 = pd.DataFrame(result)
        df1 = df1.drop_duplicates("deltapn")
        df = df.merge(df1, on="deltapn", how="left")
        summary = [
            f"料号:{i.deltapn},厂商:{i.mfgname},厂商料号:{i.mfgpn},概述：{i.summary}，解释：{i.interpretation}"
            for i in df.itertuples()
        ]
        response = call_embedding(summary)
        df["vector"] = response
        df["summary"] = summary
        # tbl = db.create_table("part_query", data=df)
        tb = db.open_table("part_query")
        tb.merge_insert("deltapn").when_not_matched_insert_all().execute(df)
        return response

    def post(self, shared, prep_res, exec_res):
        print(shared["df"])
        shared["result"] = exec_res


def part_query(shared: dict):
    plan_node = QueryNode()
    embed_node = EmbedNode()
    plan_node >> embed_node
    flow = Flow(start=plan_node)
    flow.run(shared)
    return shared
