# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac

from common import get_nt_name, get_ssp_user, id_factory
from components.file_browser import file_browser
from config import SSP_DIR

id = id_factory(__name__)


form_part_2 = dmc.Group(
    [
        fac.AntdDraggerUpload(
            apiUrl="/upload/",
            text="上传申请表附件",
            id=id("application_attachment"),
            lastUploadTaskRecord={},
            style={"width": "455px"},
        ),
        fac.AntdDraggerUpload(
            apiUrl="/upload/",
            text="上传台达软体管控表单附件",
            id=id("controlling_attachment"),
            lastUploadTaskRecord={},
            style={"width": "455px"},
        ),
    ]
)
doc = file_browser(SSP_DIR / "program" / "doc" / "pn", __name__)

submit = dmc.Button("提交", id=id("rd-submit"))


def layout(**kwargs):
    nt_name = get_nt_name()
    users = get_ssp_user()
    user = users.loc[users["nt_name"] == nt_name]
    role_group = user["role_group"].iloc[0]
    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True
    applicant = dmc.Select(
        label="申请人(Applicant)",
        withAsterisk=True,
        size="xs",
        id=id("applicant"),
        placeholder="申请人(Applicant)",
        value=nt_name,
        data=users["nt_name"].tolist(),
        disabled=applicant_disabled,
        searchable=True,
        style={"width": 150},
    )
    form_part_1 = dmc.Stack(
        [
            dmc.Center(dmc.Text("IC+软体组合件申请单", weight=700, id=id("title"))),
            dmc.Divider(),
            applicant,
            dmc.Group(
                [
                    dmc.TextInput(
                        label="台达料号",
                        size="xs",
                        withAsterisk=True,
                        id=id("deltapn"),
                        debounce=1000,
                    ),
                    dmc.TextInput(
                        label="描述",
                        size="xs",
                        id=id("des"),
                    ),
                    dmc.TextInput(
                        label="厂商",
                        size="xs",
                        id=id("mfgname"),
                    ),
                    dmc.TextInput(
                        label="厂商料号",
                        size="xs",
                        id=id("mfgpn"),
                        debounce=1000,
                    ),
                    dmc.Select(
                        label="材料类别1",
                        placeholder="Select one",
                        size="xs",
                        withAsterisk=True,
                        id=id("cat1"),
                        data=[
                            "Active",
                            "Passive",
                            "Magnetic",
                            "Electro-Mechanical",
                            "Mechanical",
                            "Other",
                        ],
                    ),
                    dmc.Select(
                        label="材料类别2",
                        placeholder="Select one",
                        size="xs",
                        withAsterisk=True,
                        id=id("cat2"),
                        data=[],
                    ),
                    dmc.Select(
                        label="材料类别3",
                        placeholder="Select one",
                        size="xs",
                        # withAsterisk=True,
                        id=id("cat3"),
                        data=[],
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ]
    )
    return dmc.Container(
        dmc.Stack(
            [
                form_part_1,
                form_part_2,
                submit,
                dmc.Divider(label="模板文件", labelPosition="center"),
                doc,
            ]
        )
    )
