import json
from datetime import datetime

import dash_bootstrap_components as dbc
import dash_tabulator as dt
import dash_uploader as du
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ALL,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    callback_context,
    clientside_callback,
    dash,
    dcc,
    html,
    no_update,
)
from dash_extensions.javascript import Namespace
from pony.orm import db_session, select

from common import df_to_html, read_sql
from config import UPLOAD_FOLDER_ROOT, pool
from db.ssp import Dept
from db.ssp_spec import Doctype, Due_day, Duty, Task, db
from tasks import bg_mail

dash.register_page(__name__, title="规格")
ns = Namespace("myNamespace", "tabulator")
col_dict = {
    "doc_type": "文件类型",
    "model": "机种名称",
    "status": "状态",
    "input_date": "创建日期",
    "request_date": "需求日期",
}

status_dict = {
    "accepted": "规格已接收",
    "submitted_ee": "电子审核",
    "submitted_me": "机构审核",
    "modify_ee": "文件更新",
    "modify_me": "文件更新",
    "open": "尚未受理",
    "close": "流程结束",
    "cancel": "任务取消",
}

spe_nav = dbc.Nav(
    [
        dbc.NavItem([dbc.NavLink("申请", active=True, href="?page=apply")]),
        dbc.NavItem([dbc.NavLink("处理", href="?page=solve")]),
        dbc.NavItem([dbc.NavLink("查询", href="?page=query")]),
    ],
    pills=True,
    id="spe-nav",
    className="mx-3",
)

spe_submit = dbc.Col(
    [
        dbc.Button(["提交"], color="primary", id="spe-submit"),
    ],
    width={"size": 1, "offset": 11},
)
spe_modal = dbc.Modal(
    [
        dbc.ModalHeader(id="spe-apply-header"),
        dbc.ModalBody(id="spe-apply-body"),
        dbc.ModalFooter(dbc.Button("确定", id="spe-apply-close", className="ml-auto")),
    ],
    id="spe-apply-window",
    size="sm",
    centered=True,
)
page_apply = html.Div(id="spe-apply-display", className="mx-3")
# ----------------申请表单组件------------------------
page_apply_doc = html.Div(
    [
        html.Span(style={"font-size": "20px"}, className="pl-3", id="spe-apply-span"),
        html.Div(
            [
                dbc.Row(id="spe-apply-form"),
                dbc.Row(id="spe-apply-address"),
                dbc.Row([spe_submit, spe_modal], className="mt-3"),
            ],
            style={"border-radius": "4px", "background-color": "white"},
            className="p-3 mt-1",
        ),
    ],
    id="spe-apply-doc",
    className="mx-3 mt-3",
)

# ------------------------------ee edit---------------
spe_ee_remark = dbc.Col(
    [
        dbc.Label(
            "修改意见", html_for="spe-form-remark", width=1, className="required-fields"
        ),
        dbc.Col(
            dbc.Textarea(id="spe-form-remark", value="", placeholder="input remark"),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "none"},
    id="spe-ee-remark-form",
)

spe_ee_upload = dbc.Col(
    [
        dbc.Label("上传附件", html_for="spe-form-upload", width=1),
        dbc.Col(
            du.Upload(
                id="spe-form-upload",
                text="请上传附件",
                filetypes=["xls", "xlsx", "doc", "docx", "xlsm", "txt", "html"],
                default_style={"border-color": "#ccc"},
            ),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "none"},
    id="spe-ee-upload-form",
)
spe_ee_modal = dbc.Modal(
    [
        dbc.ModalHeader(id="spe-ee-modal-header"),
        dbc.ModalBody(id="spe-ee-modal-body"),
        dbc.ModalFooter(
            dbc.Button("确定", id="spe-ee-modal-close", className="ml-auto", href="#")
        ),
    ],
    id="spe-ee-modal-window",
    size="sm",
    centered=True,
)

spe_ee_action = dbc.Col(
    [
        dbc.Label("Action", html_for="spe-ee-action", width=1),
        dbc.Col(
            dbc.RadioItems(
                id="spe-ee-action",
                options=[
                    {"label": "核准", "value": 1},
                    {"label": "退回修改", "value": 2},
                ],
                value=1,
            ),
            width=10,
        ),
    ],
)

page_2_ee_edit = html.Div(
    [
        # html.Hr(className='spe-hr mt-4'),
        # html.Span('文件详情',style={'font-size':'20px'},className='pl-3'),
        html.Div(
            [
                html.Div(
                    [
                        html.Table(
                            html.Tbody(
                                [
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("机种名称："),
                                                            html.Span(
                                                                id="spe-ee-model"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=2,
                                                style={"width": "40%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("Status："),
                                                            html.Span(
                                                                id="spe-ee-status"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("文件类别："),
                                                            html.Span(id="spe-ee-doc"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("客户："),
                                                            html.Span(
                                                                id="spe-ee-custom"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("部门："),
                                                            html.Span(id="spe-ee-dept"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("创建人："),
                                                            html.Span(
                                                                id="spe-ee-owner"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("EE："),
                                                            html.Span(id="spe-ee-ee"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("ME："),
                                                            html.Span(id="spe-ee-me"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("SPEC："),
                                                            html.Span(id="spe-ee-spec"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("需求日期："),
                                                            html.Span(id="spe-ee-date"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span(
                                                                "是否需要替代料："
                                                            ),
                                                            html.Span(
                                                                id="spe-ee-second"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("Remark："),
                                                            html.Span(
                                                                id="spe-ee-remark"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=5,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("附件下载："),
                                                            html.A(
                                                                id="spe-ee-attach",
                                                                className="a-style",
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=5,
                                            )
                                        ]
                                    ),
                                ]
                            ),
                            className="spe-table",
                        )
                    ]
                )
            ],
            style={"background-color": "white", "border-radius": "4px"},
            className="py-2",
        ),
        dcc.Download(id="spe-ee-download"),
        # html.Hr(className="spe-hr-b"),
        html.Div(
            [
                dbc.Form(
                    [spe_ee_action, spe_ee_remark, spe_ee_upload, spe_ee_modal],
                    className="ml-4",
                    id="spe-form",
                ),
                dbc.Row(
                    dbc.Col(
                        dbc.Button(
                            "提交",
                            color="primary",
                            id="spe-ee-submit",
                            className="ml-4",
                            disabled=False,
                        ),
                        width={"size": 1, "offset": 11},
                    ),
                ),
            ],
            style={"background-color": "white", "border-radius": "4px"},
            className="mt-3 py-3",
        ),
    ],
    className="mx-3 mt-3",
)
page_2_ee = html.Div(
    [
        dbc.Spinner(
            dt.DashTabulator(
                id="spe-task-solve",
                theme="tabulator_site",
                options={"maxHeight": "500px", "layout": "fitData"},
                columns=[
                    {"title": "状态", "field": "status"},
                    {"title": "文件类别", "field": "doc_type"},
                    {
                        "title": "机种名称",
                        "field": "model_url",
                        "formatter": "link",
                        "formatterParams": {"labelField": "model"},
                    },
                    {"title": "规格", "field": "spec"},
                    {"title": "创建人", "field": "owner"},
                    {"title": "客户", "field": "customer"},
                    {"title": "是否需要替代料", "field": "second_source"},
                    {"title": "其他要求", "field": "remark"},
                    {
                        "title": "创建日期",
                        "field": "input_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {
                        "title": "需求日期",
                        "field": "request_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {"title": "id", "field": "id", "visible": False},
                ],
                data=[{}],
                cellEditing=True,
            ),
            color="primary",
        ),
    ],
    className="mt-3 mx-3",
)
dropdown_menu_items = [
    dbc.DropdownMenuItem("Deep thought", id="dropdown-menu-item-1"),
    dbc.DropdownMenuItem("Hal", id="dropdown-menu-item-2"),
    dbc.DropdownMenuItem(divider=True),
    dbc.DropdownMenuItem("Clear", id="dropdown-menu-item-clear"),
]

page_query_ee = html.Div(
    [
        dbc.Row(
            [
                dbc.Col(
                    [
                        dbc.Row(
                            [
                                dbc.Col(
                                    dcc.Dropdown(
                                        id="spe-query-status",
                                        placeholder="Select Status",
                                        style={
                                            "width": "100%",
                                            "flex": 1,
                                            "border-style": "none",
                                        },
                                    ),
                                    width=2,
                                ),
                                dbc.Col(
                                    dcc.Dropdown(
                                        id="spe-query-model",
                                        placeholder="Select Model",
                                        style={
                                            "width": "100%",
                                            "flex": 1,
                                            "border-style": "none",
                                        },
                                    )
                                ),
                                dbc.Col(
                                    dcc.DatePickerRange(
                                        start_date_placeholder_text="InputDate Start",
                                        end_date_placeholder_text="InputDate End",
                                        calendar_orientation="vertical",
                                        clearable=True,
                                        className="spe-query-date-picker-multi",
                                        id="spe-query-date-multi",
                                        display_format="YYYY-M-D",
                                    )
                                ),
                                dbc.Col(
                                    dcc.Dropdown(
                                        id="spe-query-doctype",
                                        placeholder="Select DocType",
                                        style={
                                            "width": "100%",
                                            "flex": 1,
                                            "border-style": "none",
                                        },
                                    )
                                ),
                            ]
                        ),
                        dbc.Row(
                            [
                                dbc.Col(
                                    dcc.DatePickerSingle(
                                        display_format="YYYY-M-D",
                                        clearable=True,
                                        className="spe-query-date-picker-single",
                                        id="spe-query-date-single",
                                        placeholder="ReleaseDate",
                                    ),
                                    width=2,
                                ),
                                dbc.Col(
                                    dcc.Dropdown(
                                        id="spe-query-spec",
                                        placeholder="SPEC",
                                        style={
                                            "width": "100%",
                                            "flex": 1,
                                            "border-style": "none",
                                        },
                                    ),
                                    width=3,
                                ),
                            ],
                            className="my-3",
                        ),
                    ]
                ),
                dbc.Col(
                    dbc.Button(
                        "查询",
                        color="primary",
                        id="spe-query-btn",
                        style={"height": "83%", "width": "100%"},
                    ),
                    width=1,
                ),
            ]
        ),
        # html.Hr(className='spe-hr-d mt-4'),
        dcc.Store(id="spe-query-state"),
        dcc.Download(id="spe-download"),
        dbc.Spinner(
            dt.DashTabulator(
                id="spe-query-result",
                theme="tabulator_site",
                options={"maxHeight": "420px", "layout": "fitData"},
                # columns=[{}],
                columns=[
                    {
                        "title": "状态",
                        "field": "status",
                        "headerMenu": ns("headerMenu"),
                    },
                    {
                        "title": "当前处理人",
                        "field": "current",
                        "headerMenu": ns("headerMenu"),
                        "visible": False,
                    },
                    {
                        "title": "创建人",
                        "field": "owner",
                        "headerMenu": ns("headerMenu"),
                    },
                    {
                        "title": "规格",
                        "field": "spec",
                        "headerMenu": ns("headerMenu"),
                    },
                    {
                        "title": "文件类型",
                        "field": "doc_type",
                        "headerMenu": ns("headerMenu"),
                    },
                    {
                        "title": "机种名称",
                        "field": "model",
                        "headerMenu": ns("headerMenu"),
                    },
                    {
                        "title": "电子",
                        "field": "ee",
                        "headerMenu": ns("headerMenu"),
                        "visible": False,
                    },
                    {
                        "title": "机构",
                        "field": "me",
                        "headerMenu": ns("headerMenu"),
                        "visible": False,
                    },
                    {
                        "title": "创建日期",
                        "field": "input_date",
                        "headerMenu": ns("headerMenu"),
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {
                        "title": "生效日期",
                        "field": "release_date",
                        "headerMenu": ns("headerMenu"),
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {"title": "id", "field": "id", "visible": False},
                    # {"title": "附件地址", "field": "attachment", "visible": False},
                    # {
                    #     "title": "附件下载",
                    #     "field": "filename",
                    #     "headerMenu": ns("headerMenu"),
                    #     "formatter": ns("getFormatter"),
                    # },
                ],
                data=[{}],
                cellEditing=True,
            ),
            color="primary",
        ),
    ],
    className="mt-3 mx-3",
)

page_dict = {
    "apply": page_apply,
    "apply2": page_apply_doc,
    "solve": page_2_ee,
    "solve2": page_2_ee_edit,
    "query": page_query_ee,
}


def layout(page="apply", **kwargs):
    content = html.Div(page_dict.get(page), id="spe-content")
    # if page == "solve2":
    #     layout = dbc.Container([content], fluid=True)
    # else:
    #     layout = dbc.Container([spe_nav, content], fluid=True)
    layout = dbc.Container([content], fluid=True)
    # layout = dbc.Container([spe_nav, content], fluid=True)
    return layout


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="nav_click"),
    Output("spe-nav", "style"),
    Input("spe-nav", "id"),
    prevent_initial_call=False,
)


@callback(
    Output("spe-apply-display", "children"),
    Input("user", "data"),
    prevent_initial_call=False,
)
@db_session
def spe_apply_display(user):
    dept_id = user.get("dept_id")
    doc_type = Doctype.select(lambda d: d.dept_id == dept_id)[:]
    doc_type = [i.doc_type for i in doc_type]
    ch = []
    for value in doc_type:
        card = new_cards(value, "?page=apply2&type={}".format(value))
        ch.append(card)
    return [dbc.Row(ch)]


@callback(
    Output("spe-apply-span", "children"),
    Output("spe-apply-form", "children"),
    Input("user", "data"),
    State("url", "search"),
    prevent_initial_call=False,
)
@db_session
def create_apply_form(user, search):
    dept_id = user.get("dept_id")
    doc = search.split("=")[2].replace("%20", " ")
    form_id = Doctype.select(lambda d: d.dept_id == dept_id and d.doc_type == doc)[:]
    form_id = [i.form_id for i in form_id]
    form_id = list(form_id[0])

    params = form_id
    ph = ",".join(["%s"] * len(form_id))
    sql = f"select * from ssp_spec.apply_form where id in ({ph})"
    df1 = read_sql(sql, params=params)

    options = {
        "电子姓名": dropdown_options(dept_id, "ee"),
        "机构姓名": dropdown_options(dept_id, "me"),
        "是否需要多报备替代料": ["是", "否"],
        "上传地址": ["PLM", "其他", "不需要上传"],
    }
    del df1["id"]
    df_input = df1[df1["type"] == "input"]
    del df_input["type"]
    df_select = df1[df1["type"] == "select"]
    del df_select["type"]
    df_select["options"] = df_select["label"].map(options)
    df_date = df1[df1["type"] == "date"]
    del df_date["type"]
    df_upload = df1[df1["type"] == "upload"]
    del df_upload["type"]
    new_input = [
        apply_input(label, i, size, order, required)
        for (i, label, size, order, required) in [tuple(x) for x in df_input.values]
    ]

    new_select = [
        apply_select(label, i, size, order, required, options)
        for (i, label, size, order, required, options) in [
            tuple(x) for x in df_select.values
        ]
    ]

    due_day = (
        select(i for i in Due_day if i.dept_id == dept_id and i.doc_type == doc)
        .first()
        .due_day
    )
    min_date_allowed = datetime.today().date() + pd.offsets.BDay(due_day)
    new_date = [
        apply_date(label, i, size, order, required, min_date_allowed)
        for (i, label, size, order, required) in [tuple(x) for x in df_date.values]
    ]

    new_upload = [
        apply_uploader(label, i, size, order, required)
        for (i, label, size, order, required) in [tuple(x) for x in df_upload.values]
    ]

    form = dbc.Form(
        [
            dbc.Row(
                new_input + new_select + new_date,
                class_name="g-3",
            ),
            dbc.Row(new_upload),
        ],
        style={"display": "-webkit-inline-box"},
    )
    form = new_input + new_select + new_date + new_upload
    return doc + "申请表", form


@callback(
    Output("spe-apply-address", "children"),
    Input({"type": "select", "index": ALL}, "value"),
)
def spe_dropdown_option(value):
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    dic = json.loads(id)
    if dic.get("index") == "upload_address" and "其他" in value:
        return [
            apply_input("其他上传地址", "other_address", "6", "10", "required-fields")
        ]
    elif dic.get("index") == "upload_address" and "其他" not in value:
        return []
    else:
        raise PreventUpdate


@callback(
    Output({"type": "input", "index": ALL}, "value"),
    Output({"type": "select", "index": ALL}, "value"),
    Output({"type": "date", "index": ALL}, "date"),
    Output({"type": "upload", "index": ALL}, "fileNames"),
    Output("spe-apply-window", "is_open"),
    Output("spe-apply-header", "children"),
    Output("spe-apply-body", "children"),
    Output("spe-apply-close", "color"),
    Input("spe-submit", "n_clicks"),
    Input("spe-apply-close", "n_clicks"),
    State({"type": "input", "index": ALL}, "value"),
    State({"type": "select", "index": ALL}, "value"),
    State({"type": "date", "index": ALL}, "date"),
    State({"type": "upload", "index": ALL}, "fileNames"),
    State({"type": "upload", "index": ALL}, "upload_id"),
    State({"type": "input", "index": ALL}, "id"),
    State({"type": "select", "index": ALL}, "id"),
    State({"type": "date", "index": ALL}, "id"),
    State({"type": "upload", "index": ALL}, "id"),
    State("user", "data"),
    State("url", "search"),
    State("spe-apply-window", "is_open"),
)
@db_session
def new_spec_ee_apply_submit(
    n,
    close,
    inpu,
    select,
    date,
    filename,
    upload_id,
    id_i,
    id_s,
    id_d,
    id_u,
    user,
    search,
    is_open,
):
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if n or close:
        if id == "spe-submit":
            name = user.get("nt_name").lower()
            today = datetime.today().date()
            dept_id = user.get("dept_id")
            dept = user.get("dept")
            doc = search.split("=")[2].replace("%20", " ")
            required = id_i + id_s + id_d + id_u
            requiredx = [i.get("index") for i in required]

            ph = ",".join(["%s"] * len(requiredx))
            params = requiredx
            sql = f"select col_name,required from ssp_spec.apply_form where col_name in ({ph})"
            df = read_sql(sql, params=params)
            paramsa = [dept_id, doc]
            sqla = "select action_process from ssp_spec.doctype where dept_id=%s and doc_type=%s"
            dfa = read_sql(sqla, params=paramsa)
            sql = "select * from ssp_spec.due_day where dept_id=%s and doc_type=%s limit 1"
            dfd = read_sql(sql, params=paramsa)

            dfx = df[df["required"] == "required-fields"]
            list_required = dfx["col_name"].tolist()
            due_day = Due_day.get(dept_id=dept_id, doc_type=doc).due_day
            dic_ta = {}
            dic_mo = {}
            if len(filename) and filename[0]:
                attachment = str(
                    UPLOAD_FOLDER_ROOT / upload_id[0] / filename[0][0]
                ).replace("\\", "/")
                dic_mo["attachment"] = attachment

            if date[0]:
                request_date = datetime.strptime(date[0], "%Y-%m-%d").date()
            else:
                request_date = (today + pd.offsets.BDay(due_day)).date()
            dic_mo["request_date"] = request_date
            dic_ta["request_date"] = request_date
            dic_ta["std_submit_lt"] = pd.bdate_range(today, request_date).size - 1
            dic_ta["psl_qty"] = dfd["psl_qty"].values[0]
            dic_ta["work_time_minute"] = dfd["work_time_minute"].values[0]

            for i, col in enumerate(id_i):
                dic_ta[col.get("index")] = inpu[i]
            for i, col in enumerate(id_s):
                dic_ta[col.get("index")] = select[i]
            for key, val in dic_ta.items():
                if key in ["model", "ee", "me", "remark"]:
                    dic_mo[key] = val
                    dic_ta[key] = val

            a = [
                "owner",
                "input_date",
                "doc_type",
                "dept",
                "dept_id",
                "action_process",
                "std_release_lt",
            ]
            b = [name, today, doc, dept, dept_id, dfa.loc[0, "action_process"], due_day]
            dic_ta.update(zip(a, b))
            c = ["user", "modified_date", "status"]
            d = [name, today, "open"]
            dic_mo.update(zip(c, d))

            clear_input = ["" for i in range(len(inpu))]
            clear_select = ["" for i in range(len(select))]
            clear_date = [None] if len(id_d) else []
            clear_upload = [None] if len(id_u) else []
            df1 = pd.DataFrame(dic_ta, index=[0])
            dfn = df1[list_required]

            if all(sum(dfn.values.tolist(), [])):
                new_id = db.insert("Task", **dic_ta, returning="id")
                db.insert("Modification", task_id=new_id, **dic_mo)
                # =========发通知邮件给规格组start==============
                model = dic_mo.get("model")
                duty = Duty.select(lambda x: x.dept_id == dept_id)[:]
                to = ";".join(f"{i.owner}@deltaww.com" for i in duty)
                subject = f"SSP-新任务({doc}-{model})"
                title = "Dears,<br>SSP作业平台，收到新任务,请确认并签核<br>"
                href = "http://sup.deltaww.com/spec?page=task"
                df_mail = pd.DataFrame([dic_ta | dic_mo])
                df_mail = df_mail.reindex(
                    columns=[
                        "doc_type",
                        "model",
                        "status",
                        "input_date",
                        "request_date",
                    ]
                )
                df_mail["status"] = np.where(
                    df_mail["status"] == "open", "尚未接收", df_mail["status"]
                )
                df_mail = df_mail.rename(columns=col_dict)
                body = df_to_html(df_mail, title=title, href=href)
                bg_mail(to, subject, body)

                # =========发通知邮件给规格组end==============
                return (
                    clear_input,
                    clear_select,
                    clear_date,
                    clear_upload,
                    not is_open,
                    "Success!",
                    "提交成功！",
                    "success",
                )
            else:
                return (
                    inpu,
                    select,
                    date,
                    filename,
                    not is_open,
                    "Error!",
                    "提交失败，请将必填项补充完整！",
                    "danger",
                )
        else:
            return (
                inpu,
                select,
                date,
                filename,
                not is_open,
                no_update,
                no_update,
                no_update,
            )
        # raise PreventUpdate
    else:
        raise PreventUpdate


# --------------处理---------------------------
def solve_task(df):
    # df = df.sort_values(by=["modified_date"], ascending=False)
    # df = df.drop_duplicates(["id"])
    columns = (
        "id",
        "status",
        "owner",
        "model",
        "input_date",
        "remark",
        "customer",
        "doc_type",
        "request_date",
        "second_source",
        "spec",
        "dept",
    )
    df = df.reindex(columns=columns)
    if df.empty:
        return df
    # df = df.loc[:,('id','status','owner','model','ee','me','customer','doc_type','request_date','second_source','remark','attachment','dept')]
    for i in ["input_date", "request_date"]:
        df[i] = pd.to_datetime(df[i], errors="coerce").dt.strftime("%Y-%m-%d")
    # f = lambda x:'[{a}](/apps/spec_ee?type=page-2#{b})'.format(a=x['model'],b=x['id'])
    # df['model'] = df.apply(f,axis=1)
    f = lambda x: "/spec-ee?page=solve2#{a}".format(a=x["id"])
    df["model_url"] = df.apply(f, axis=1)
    df = df.reindex(
        columns=[
            "status",
            "doc_type",
            "model",
            "customer",
            "owner",
            "spec",
            "request_date",
            "second_source",
            "remark",
            "input_date",
            "id",
            "model_url",
        ]
    )
    df = repalce(df)
    return df


@callback(
    Output("spe-task-solve", "data"),
    [Input("user", "data")],
    prevent_initial_call=False,
)
def new_task(user):
    nt_name = user.get("nt_name").lower()
    role = user.get("role_group").lower()
    if role in ["ee", "me"]:
        sql = "select m.spec,m.modified_date,m.status,t.remark,m.attachment,t.id,t.input_date,\
        t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source\
        from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id where m.ee=%s \
            and m.status=%s and m.id in (select max(id) from ssp_spec.modification group by task_id)"
        params = [nt_name, "submitted_ee"]
        sql1 = "select m.spec,m.modified_date,m.status,t.remark,m.attachment,t.id,t.input_date,\
        t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source\
        from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id where m.me=%s \
            and m.status=%s and m.id in (select max(id) from ssp_spec.modification group by task_id)"
        params1 = [nt_name, "submitted_me"]
        dfee = read_sql(sql, params=params)
        dfme = read_sql(sql1, params=params1)

        if role == "ee":
            df = solve_task(dfee)
        else:
            df = solve_task(dfme)
    else:
        df = pd.DataFrame(
            columns=[
                "status",
                "doc_type",
                "model",
                "customer",
                "owner",
                "spec",
                "request_date",
                "second_source",
                "remark",
                "input_date",
                "id",
            ]
        )
    df = df.to_dict("records")
    return df


@callback(
    [
        Output("spe-ee-model", "children"),
        Output("spe-ee-status", "children"),
        Output("spe-ee-doc", "children"),
        Output("spe-ee-custom", "children"),
        Output("spe-ee-dept", "children"),
        Output("spe-ee-owner", "children"),
        Output("spe-ee-ee", "children"),
        Output("spe-ee-me", "children"),
        Output("spe-ee-spec", "children"),
        Output("spe-ee-date", "children"),
        Output("spe-ee-second", "children"),
        Output("spe-ee-remark", "children"),
        Output("spe-ee-attach", "children"),
    ],
    [Input("url", "hash")],
    [State("user", "data")],
    prevent_initial_call=False,
)
def spe_ee_edit(urlhash, user):
    role = user.get("role_group").lower()
    if role == "ee":
        condition = ["submitted_ee"]
    else:
        condition = ["submitted_me"]
    if urlhash:
        id = urlhash.split("#")[1]
        sql = "select m.user,m.status,m.remark,m.modified_date,m.attachment,t.id,\
        t.dept,m.spec,t.owner,m.model,t.customer,m.ee,m.me,t.doc_type,m.request_date,\
        t.second_source,t.upload_address,t.other_address \
        from ssp_spec.modification m \
        left join ssp_spec.task t \
            on m.task_id=t.id where t.id=%s \
                order by m.id desc limit 1"
        params = [id]
        df = read_sql(sql, params=params)

        if df.empty:
            raise PreventUpdate

        df["request_date"] = pd.to_datetime(
            df["request_date"], errors="coerce"
        ).dt.strftime("%Y-%m-%d")
        df["attachment"] = df["attachment"].apply(
            lambda x: x.rsplit("/", 1)[1] if x else ""
        )
        df = df.reindex(
            columns=[
                "model",
                "status",
                "doc_type",
                "customer",
                "dept",
                "owner",
                "ee",
                "me",
                "spec",
                "request_date",
                "second_source",
                "remark",
                "attachment",
            ]
        )
        df = repalce(df)
        tuples = tuple(sum(df.loc[0:].values.tolist(), []))
        return tuples
    else:
        raise PreventUpdate


# ---------------下载附件--------------------------
@callback(
    Output("spe-ee-download", "data"),
    [Input("spe-ee-attach", "n_clicks")],
    [State("url", "hash"), State("user", "data")],
    prevent_initial_call=False,
)
def sp_download(n, hash: str, user):
    if not n:
        raise PreventUpdate

    role = user.get("role_group").lower()
    if role == "ee":
        condition = ["submitted_ee"]
    else:
        condition = ["submitted_me"]

    if "#" in hash:
        id = hash.split("#")[1]
        params = [id]
        sql = "select modified_date,status,attachment,id from ssp_spec.modification where task_id=%s"
        df = read_sql(sql, params=params)
        df = df.sort_values(by=["modified_date"], ascending=False)
        df = df.drop_duplicates(["id"])
        df = df.loc[df.status.isin(condition), ("status", "attachment")]
        df.index = range(len(df))
        url = df.loc[0, "attachment"]
        return dcc.send_file(url)


@callback(
    [Output("spe-ee-remark-form", "style"), Output("spe-ee-upload-form", "style")],
    [Input("spe-ee-action", "value")],
)
def ee_action(value):
    if value == 2:
        style = {"display": "flex"}
        return style, style
    elif value == 1:
        style = {"display": "none"}
        return style, style
    else:
        PreventUpdate


# ------action操作---------------------------
@callback(
    Output("spe-ee-modal-window", "is_open"),
    Output("spe-ee-modal-header", "children"),
    Output("spe-ee-modal-body", "children"),
    Output("spe-ee-modal-close", "color"),
    Output("spe-ee-modal-close", "href"),
    Input("spe-ee-submit", "n_clicks"),
    Input("spe-ee-modal-close", "n_clicks"),
    State("spe-ee-modal-window", "is_open"),
    State("spe-form-remark", "value"),
    State("spe-form-upload", "fileNames"),
    State("spe-form-upload", "upload_id"),
    State("spe-ee-action", "value"),
    State("url", "hash"),
    State("user", "data"),
    State("spe-ee-ee", "children"),
    State("spe-ee-me", "children"),
    State("spe-ee-owner", "children"),
    State("spe-ee-spec", "children"),
    State("spe-ee-doc", "children"),
    State("spe-ee-model", "children"),
    prevent_initial_call=False,
)
@db_session
def submit_ee_edit(
    n,
    close,
    is_open,
    remark,
    files,
    uid,
    value,
    hash,
    user,
    ee,
    me,
    pm,
    spec,
    doc_type,
    model,
):
    nt_name = user.get("nt_name").lower()
    role = user.get("role_group").lower()
    if role == "ee":
        condition = "submitted_me"
        condition1 = ["submitted_ee"]
        condition2 = "modify_ee"
    else:
        condition = "close"
        condition1 = ["submitted_me"]
        condition2 = "modify_me"
    today = datetime.now()
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if n or close:
        if id == "spe-ee-submit":
            task_id = hash.split("#")[1]
            sql = (
                "select m.user,m.status,m.remark,m.modified_date,m.attachment,t.id,t.dept,t.action_process,\
            t.owner,date(t.input_date) as input_date,m.model,t.customer,m.ee,m.me,t.doc_type,m.request_date,\
            t.second_source,m.spec from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id \
            where t.id=%s order by m.id desc limit 1"
            )
            params = [task_id]
            df = read_sql(sql, params=params)

            action_process = df["action_process"].tolist()[0]
            # df = df.sort_values(by=["modified_date"], ascending=False)
            # df = df.drop_duplicates(["id"])
            df["request_date"] = pd.to_datetime(
                df["request_date"], errors="coerce"
            ).dt.strftime("%Y-%m-%d")
            # df['request_date'] = df['request_date'].where(df['request_date'].notnull(), None)
            df["request_date"] = np.where(
                df.request_date.notnull(), df.request_date, None
            )
            # df1 = df.loc[
            #     df.status.isin(condition1),
            #     ("model", "ee", "me", "request_date", "spec"),
            # ]

            # df = df.loc[
            #     df.status.isin(condition1),
            #     ("model", "ee", "me", "request_date", "attachment", "spec"),
            # ]
            df1 = df[["model", "ee", "me", "request_date", "spec"]]
            df = df[["model", "ee", "me", "request_date", "attachment", "spec"]]

            task = Task.get(id=task_id)
            input_date = task.input_date

            # 核准
            if value == 1:
                if action_process == "C":
                    condition = "close"
                dic = df.to_dict("records")[0]
                keys = ["task_id", "status", "user", "current", "modified_date"]
                if condition == "submitted_me":
                    current = dic["me"]
                else:
                    current = dic["spec"]
                values = [task_id, condition, nt_name, current, today]
                dic.update(list(zip(keys, values)))
                db.insert("Modification", **dic)
                # add release_date

                dic_update = {}
                if condition == "close":
                    dic_update["release_date"] = today
                    dic_update["act_release_lt"] = (today - input_date).days
                    # request_date 要抓两张表
                    # 查Modification表中最新非空request_date
                    due_day = Due_day.get(
                        dept_id=task.dept_id, doc_type=task.doc_type
                    ).due_day

                    sql = "select id, input_date, request_date from ssp_spec.task \
                        where id=%s and request_date is not null union all\
                        select task_id, modified_date, request_date \
                        from ssp_spec.modification\
                        where task_id=%s  and request_date is not null"
                    params = [task_id, task_id]
                    df = read_sql(sql, params=params)

                    df["request_date"] = pd.to_datetime(df["request_date"]).dt.date
                    if len(df) > 0:
                        request_date = df.values[-1][2]
                        if today.date() <= request_date:
                            dic_update["release_ontime"] = "Y"
                        else:
                            dic_update["release_ontime"] = "N"
                    else:
                        if dic_update.get("act_release_lt") <= due_day:
                            dic_update["release_ontime"] = "Y"
                        else:
                            dic_update["release_ontime"] = "N"
                Task[task_id].set(**dic_update)

                df_mail = pd.DataFrame([dic])
                df_mail["input_date"] = input_date
                df_mail["doc_type"] = task.doc_type
                df_mail = df_mail.reindex(
                    columns=[
                        "doc_type",
                        "model",
                        "status",
                        "input_date",
                        "request_date",
                    ]
                )
                df_mail["status"] = df_mail["status"].apply(
                    lambda x: status_dict.get(x)
                )
                df_mail = df_mail.rename(columns=col_dict)
                # ===========邮件start===========
                status = df_mail["状态"].iloc[0]

                subject = f"SSP-{status}({doc_type}-{model})"
                if status == "流程结束":
                    title = f"Dears,<br>SSP作业平台，{doc_type}-{model}{status},请至SSP下载文件<br>"
                    href = "http://sup.deltaww.com"
                else:
                    title = f"Dears,<br>SSP作业平台，{doc_type}-{model}{status},请至SSP处理<br>"
                    href = f"http://sup.deltaww.com/spec-ee?page=solve2#{task_id}"

                if status == "电子审核":
                    to = {ee, spec}
                elif status == "机构审核":
                    to = {me, spec}
                else:
                    to = {ee, me, pm, spec}

                to = ";".join(f"{i}@deltaww.com" for i in to)
                body = df_to_html(df_mail, title=title, href=href)
                bg_mail(to, subject, body)
                # ===========邮件end=============
                return not is_open, "成功", "成功", "success", no_update
            # 退回修改
            elif value == 2:
                if files:
                    filename = UPLOAD_FOLDER_ROOT / uid / files[0]
                    filename = str(filename)
                    filename = filename.replace("\\", "/")
                else:
                    filename = ""
                if remark or filename:
                    dic1 = df1.to_dict("records")[0]
                    keys = [
                        "task_id",
                        "status",
                        "user",
                        "current",
                        "modified_date",
                        "remark",
                        "attachment",
                    ]
                    values = [
                        task_id,
                        condition2,
                        nt_name,
                        dic1["spec"],
                        today,
                        remark,
                        filename,
                    ]
                    dic1.update(list(zip(keys, values)))
                    db.insert("Modification", **dic1)
                    # ==========退回修改邮件==========

                    df_mail = pd.DataFrame([dic1])
                    df_mail["input_date"] = input_date
                    df_mail["doc_type"] = task.doc_type
                    df_mail = df_mail.reindex(
                        columns=[
                            "doc_type",
                            "model",
                            "status",
                            "input_date",
                            "request_date",
                        ]
                    )
                    df_mail["status"] = df_mail["status"].apply(
                        lambda x: status_dict.get(x)
                    )
                    df_mail = df_mail.rename(columns=col_dict)

                    df_mail["状态"] = "文件更新"
                    to = f"{spec}@deltaww.com"
                    subject = f"SSP-退回修改({doc_type}-{model})"
                    title = f"Dears,<br>SSP作业平台，{doc_type}-{model}退回修改,请至SSP处理<br>"
                    href = f"http://sup.deltaww.com/spec?page=processing2#{task_id}"
                    body = df_to_html(df_mail, title=title, href=href)
                    bg_mail(to, subject, body)
                    # ==========退回修改邮件==========
                    return (not is_open, "success", "退回成功！", "success", no_update)
                else:
                    return not is_open, "error", "退回失败！", "danger", ""
            else:
                raise PreventUpdate
        else:
            return not is_open, no_update, no_update, no_update, no_update
    else:
        raise PreventUpdate


# **关闭窗口**
clientside_callback(
    """
    function closewindow(n_clicks,color) {
        if (color == "success") {
            close()
        }
    }
    """,
    Output("spe-ee-modal-close", "key"),
    Input("spe-ee-modal-close", "n_clicks"),
    State("spe-ee-modal-close", "color"),
)


# --------------查询--------------------------查询---------------------------查询------------------------------------
# @callback(
#     Output("spe-query-result", "columns"),
#     [Input("user", "data")],
#     prevent_initial_call=False,
# )
# def query_table_columns(user):
#     role = user.get("role_group").lower()
#     title = [
#         "Status",
#         "创建人",
#         "当前处理人",
#         "SPEC",
#         "DocType",
#         "Model",
#         "InputDate",
#         "ReleaseDate",
#         "EE",
#         "ME",
#     ]
#     field = [
#         "status",
#         "owner",
#         "current",
#         "spec",
#         "doc_type",
#         "model",
#         "input_date",
#         "modified_date",
#         "ee",
#         "me",
#     ]
#     # if role in ['ee','me']:
#     #     visible = [True,False] + [True] * (len(title)-2)
#     # else:
#     #     visible = [True] * len(title)
#     visible = [True] * len(title)
#     columns = [
#         {"title": i, "field": j, "visible": k, "headerMenu": ns("headerMenu")}
#         for i, j, k in zip(title, field, visible)
#     ]
#     col_id = [
#         {"title": "id", "field": "id", "visible": False},
#         {"title": "附件地址", "field": "attachment", "visible": False},
#         {
#             "title": "附件下载",
#             "field": "filename",
#             "headerMenu": ns("headerMenu"),
#             "formatter": ns("getFormatter"),
#         },
#     ]
#     columns.extend(col_id)
#     return columns


# 合并重叠数据
def merge_rows(df):
    num = df["revise_date"].unique()
    first = num[0]
    dfx = df[df["revise_date"].isin([first])]
    dfx.index = range(len(dfx))
    for i in num:
        dfi = df[df["revise_date"].isin([i])]
        dfi.index = range(len(dfi))
        dfx = dfx.combine_first(dfi)
    return dfx


# 数据预处理
def data_pre_processing(nt_name, role):
    # sqlx = {
    #     'pm':f'SELECT id,status,null user,doc_type,model,input_date,null modified_date,spec,owner,attachment\
    #         FROM ssp_spec.task where owner=%s UNION ALL \
    #         SELECT task_id,status,user,null,model,null,modified_date,null,null,attachment \
    #         FROM ssp_spec.modification\
    #         WHERE task_id IN (SELECT id FROM ssp_spec.task WHERE owner=%s)',
    #     'ee':f'SELECT id,status,null user,doc_type,model,input_date,null modified_date,spec,owner,ee,attachment\
    #         FROM ssp_spec.task \
    #         UNION ALL\
    #         SELECT task_id,status,user,null,model,null,modified_date,null,null,ee,attachment\
    #         FROM ssp_spec.modification\
    #         WHERE task_id IN (SELECT DISTINCT task_id FROM ssp_spec.modification WHERE ee=%s)',
    #     'me':f'SELECT id,status,null user,doc_type,model,input_date,null modified_date,spec,owner,me,attachment\
    #         FROM ssp_spec.task \
    #         UNION ALL\
    #         SELECT task_id,status,user,null,model,null,modified_date,null,null,me,attachment\
    #         FROM ssp_spec.modification\
    #         WHERE task_id IN (SELECT DISTINCT task_id FROM ssp_spec.modification WHERE me=%s)',
    # }
    sql = {
        "pm": "SELECT b.* FROM (\
            SELECT DISTINCT(m.id)tt,m.*,t.owner,t.input_date,t.doc_type,t.release_date\
            FROM ssp_spec.modification m\
            LEFT JOIN ssp_spec.task t\
            ON m.task_id=t.id\
            WHERE t.owner=%s\
            ORDER BY m.id DESC\
        ) b GROUP BY b.task_id",
        "ee": "SELECT c.* FROM (SELECT b.* FROM (\
            SELECT DISTINCT(m.id)tt,m.*,t.owner,t.input_date,t.doc_type,t.release_date\
            FROM ssp_spec.modification m\
            LEFT JOIN ssp_spec.task t\
            ON m.task_id=t.id\
            ORDER BY m.id DESC\
            ) b GROUP BY b.task_id) c WHERE c.ee=%s ORDER BY c.id DESC",
        "me": "SELECT c.* FROM (SELECT b.* FROM (\
            SELECT DISTINCT(m.id)tt,m.*,t.owner,t.input_date,t.doc_type,t.release_date\
            FROM ssp_spec.modification m\
            LEFT JOIN ssp_spec.task t\
            ON m.task_id=t.id\
            ORDER BY m.id DESC\
            ) b GROUP BY b.task_id) c WHERE c.me=%s ORDER BY c.id DESC",
    }
    params = {"pm": [nt_name], "ee": [nt_name], "me": [nt_name]}

    df = read_sql(sql.get(role), params=params.get(role))

    return df


@callback(
    Output("spe-query-state", "data"), Input("user", "data"), prevent_initial_call=False
)
def query_tabel_data(user):
    nt_name = user.get("nt_name").lower()
    # dept = user.get('dept').lower()
    role = user.get("role_group").lower()
    dfx = data_pre_processing(nt_name, role)
    # dfx['revise_date'] = np.where(dfx.input_date.notnull(),dfx.input_date,dfx.modified_date)
    # dfx = dfx.sort_values(by=['id','revise_date'],ascending=False)
    # dfx = dfx.groupby('id').apply(merge_rows)
    # if role in ['ee', 'me']:
    #     dfx = dfx[dfx[role].isin([nt_name])]
    for i in ["input_date", "modified_date"]:
        dfx[i] = pd.to_datetime(dfx[i], errors="coerce").dt.strftime("%Y-%m-%d")
    repalce(dfx)
    dfx = dfx.to_dict("records")
    return dfx


@callback(
    Output("spe-query-result", "data"),
    Input("spe-query-btn", "n_clicks"),
    State("user", "data"),
    State("spe-query-status", "value"),
    State("spe-query-model", "value"),
    State("spe-query-doctype", "value"),
    State("spe-query-spec", "value"),
    State("spe-query-date-multi", "start_date"),
    State("spe-query-date-multi", "end_date"),
    State("spe-query-date-single", "date"),
    prevent_initial_call=False,
)
def query_tabel_dataa(
    n,
    user,
    status=None,
    model=None,
    doc_type=None,
    spec=None,
    start=None,
    end=None,
    modified_date=None,
):
    ctx = callback_context
    input_id = ctx.triggered[0]["prop_id"].split(".")[0]
    if input_id == "spe-query-btn":
        nt_name = user.get("nt_name").lower()
        # dept = user.get('dept').lower()
        role = user.get("role_group").lower()
        dfx = data_pre_processing(nt_name, role)
        dfx["current"] = np.where(dfx.status.str.lower() == "close", "", dfx["current"])
        # dfx['revise_date'] = np.where(dfx.input_date.notnull(),dfx.input_date,dfx.modified_date)
        # dfx = dfx.sort_values(by=['id','revise_date'],ascending=False)
        # dfx = dfx.groupby('id').apply(merge_rows)
        # if role in ['ee', 'me']:
        #     dfx = dfx[dfx[role].isin([nt_name])]
        for i in ["input_date", "modified_date"]:
            dfx[i] = pd.to_datetime(dfx[i], errors="coerce").dt.strftime("%Y-%m-%d")
        condition = {
            "status": status,
            "model": model,
            "doc_type": doc_type,
            "spec": spec,
            "modified_date": modified_date,
        }
        # 流程结束才显示最终附件
        dfx.loc[dfx["status"].str.lower() != "close", "attachment"] = ""
        dfx["filename"] = dfx["attachment"].apply(
            lambda x: x.rsplit("/", 1)[1] if x else ""
        )
        repalce(dfx)
        for key, value in condition.items():
            if value is not None:
                dfx = dfx[(dfx[key] == value)]
        if start is not None:
            if end is not None:
                dfx = dfx[(dfx["input_date"] >= start) & (dfx["input_date"] <= end)]
            else:
                dfx = dfx[(dfx["input_date"] >= start)]
        else:
            if end is not None:
                dfx = dfx[(dfx["input_date"] <= end)]
        con_list = [status, model, doc_type, spec, start, end, modified_date]
        if dfx.empty or not any(con_list):
            df_empty = pd.DataFrame(
                columns=[
                    "status",
                    "owner",
                    "current",
                    "spec",
                    "doc_type",
                    "model",
                    "input_date",
                    "release_date",
                    "ee",
                    "me",
                    "id",
                    "attachment",
                    "filename",
                ]
            )
            df_empty = df_empty.to_dict("records")
            return df_empty
        else:
            dfx = dfx.to_dict("records")
            return dfx
    else:
        df_empty = pd.DataFrame(
            columns=[
                "status",
                "owner",
                "current",
                "spec",
                "doc_type",
                "model",
                "input_date",
                "release_date",
                "ee",
                "me",
                "id",
                "attachment",
                "filename",
            ]
        )
        df_empty = df_empty.to_dict("records")
        return df_empty
        # dfxs = dfx['status'].str.lower()
        # dfx=dfx[~dfxs.str.contains('close|cancel')]


@callback(
    Output("spe-query-status", "options"),
    Output("spe-query-model", "options"),
    Output("spe-query-doctype", "options"),
    Output("spe-query-spec", "options"),
    Input("spe-query-state", "data"),
    prevent_initial_call=False,
)
def query_condition(data):
    df = pd.DataFrame(data)
    if df.empty:
        raise PreventUpdate
    else:
        index = ["status", "model", "doc_type", "spec"]
        source = []
        for i in index:
            s = df[i].unique().tolist()
            while None in s:
                s.remove(None)
            source.append([{"label": j, "value": j} for j in s])
        return tuple(source)


# -----下载文件-----------------------------------------------------
@callback(
    Output("spe-download", "data"),
    Input("spe-query-result", "rowClicked"),
    prevent_initial_call=False,
)
def download_query_attachment(c):
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if id == "spe-query-result":
        url = c.get("attachment")
        if len(url):
            return dcc.send_file(url)
        else:
            raise PreventUpdate


def excel_pdf(path):
    # Excel转pdf
    pdf_path = path.replace("xls", "pdf")
    xlApp = client.CreateObject("Excel.Application")
    books = xlApp.Workbooks.Open(path)
    books.ExportAsFixedFormat(0, pdf_path)
    xlApp.Quit()
    del xlApp


# 生成主页创建表单卡片
def new_cards(type, src):
    content = dbc.Col(
        dbc.Card(
            dbc.CardBody(
                [
                    html.H5(type, className="card-title", style={"color": "white"}),
                    dbc.Button("创建", color="light", href=src, outline=True),
                ]
            ),
            style={"border": "none"},
            color="primary",
        ),
        width=2,
        className="mt-3",
    )
    return content


# apply input模板
def apply_input(label, i, size, order, required):
    x = dbc.Col(
        [
            dbc.Label(
                label, html_for={"index": i, "type": "input"}, className=required
            ),
            dbc.Input(id={"index": i, "type": "input"}),
        ],
        className="g-3",
        width=size,  # {"size": size, "order": order},
    )
    return x


# apply select模板
def apply_select(label, i, size, order, required, options):
    return dbc.Col(
        [
            dbc.Label(
                label, html_for={"index": i, "type": "select"}, className=required
            ),
            dcc.Dropdown(
                style={"width": "100%", "flex": 1, "border-radius": "4px"},
                id={"index": i, "type": "select"},
                options=[{"label": j, "value": j} for j in options],
            ),
        ],
        className="g-3",
        width=size,  # {"size": size, "order": order},
    )


# apply date模板
def apply_date(label, i, size, order, required, min_date_allowed):
    return dbc.Col(
        [
            dbc.Label(
                label,
                html_for={"index": i, "type": "date"},
                className=required,
            ),
            dcc.DatePickerSingle(
                id={"index": i, "type": "date"},
                display_format="YYYY-M-D",
                clearable=True,
                className="date-picker",
                style={"width": "100%"},
                min_date_allowed=min_date_allowed,
            ),
        ],
        className="g-3",
        width=size,  # {"size": size, "order": order},
    )


# apply uploader 模板
def apply_uploader(label, i, size, order, required):
    return dbc.Col(
        [
            dbc.Label(
                label, html_for={"index": i, "type": "upload"}, className=required
            ),
            du.Upload(
                id={"index": i, "type": "upload"},
                text="请上传附件",
                filetypes=["xls", "xlsx", "doc", "docx", "xlsm", "txt", "html"],
                text_completed="附件上传成功！",
            ),
        ],
        className="g-3 spe-upload-height",
        # style={"overflow": "hidden"},
        width=size,  # {"size": size, "order": order},
    )


# -------判断----------
def find_string(s, t):
    try:
        s.index(t)
        return True
    except ValueError:
        return False


# --------替换状态----------
def repalce(df):
    df["status"] = df["status"].str.lower()
    config = {
        "accepted": "规格已接收",
        "submitted_ee": "电子审核",
        "submitted_me": "机构审核",
        "modify_ee": "文件更新",
        "modify_me": "文件更新",
        "open": "尚未受理",
        "close": "流程结束",
        "cancel": "任务取消",
    }
    for key, value in config.items():
        df.loc[df["status"] == key, "status"] = value
    return df


# -----校验姓名-------------
@db_session
def dropdown_options(dept_id, role):
    # ee me修改为同dept group的成员
    dept_group = Dept.get(id=dept_id).dept_group
    dept_id_group = Dept.select(lambda d: d.dept_group == dept_group)[:]
    dept_id_group = [d.id for d in dept_id_group]

    params = [dept_id_group, role]
    sql = "select nt_name from ssp.user \
    where dept_id in %s and role_group=%s and termdate is null"
    df = read_sql(sql, params=params)

    df["nt_name"] = df["nt_name"].str.lower()
    l = df["nt_name"].tolist()
    return l
