# -*- coding: utf-8 -*-
import os
from datetime import date, datetime

from pony.orm import Database, Json, LongStr, Optional, PrimaryKey, Required, Set

db: Database = Database()
db.bind(
    provider="mysql",
    host=os.getenv("MYSQL_HOST"),
    user=os.getenv("MYSQL_USER"),
    passwd=os.getenv("MYSQL_PASSWORD"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    db="ssp_ext",
)


class Mag(db.Entity):
    _table_ = "mag"
    deltapn = Required(str)
    des = Optional(str)
    mfgname = Optional(str)
    mfgpn = Optional(str)
    stock_no = Required(str)
    qty = Required(int)


class Mag_stock_list(db.Entity):
    id = PrimaryKey(int, auto=True)
    deltapn = Required(str)
    qty = Required(int, default=0)
    gmt_create = Required(datetime, precision=0, default=lambda: datetime.now())
    stock_ins = Set("Mag_stock_in")
    stock_nos = Set("Mag_stock_no")
    stock_outs = Set("Mag_stock_out")


class Mag_stock_out(db.Entity):
    id = PrimaryKey(int, auto=True)
    owner = Required(str)
    qty = Required(int)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    mag_stock_list = Required(Mag_stock_list)


class Mag_stock_in(db.Entity):
    id = PrimaryKey(int, auto=True)
    owner = Required(str)
    qty = Required(int)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    mag_stock_list = Required(Mag_stock_list)
    version = Required(str)


class Mag_stock_no(db.Entity):
    id = PrimaryKey(int, auto=True)
    stock_no = Required(str)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    mag_stock_list = Required(Mag_stock_list)


# *--------meeting----------------
class Meeting(db.Entity):
    id = PrimaryKey(int, auto=True)
    recorded_by = Required(str)
    subject = Required(str)
    category = Required(str)
    action_item_description = Required(LongStr)
    action_item_update = Optional(LongStr)
    owner = Required(str)
    due_day = Required(date, default=lambda: datetime.now())
    approver = Required(str)
    actual_close_day = Optional(date)
    status = Required(str, default="open")
    attendee = Required(str)
    approver_comment = Optional(str)
    dept_id = Required(int)
    gmt_create = Required(datetime, default=lambda: datetime.now())


class Meeting_category(db.Entity):
    id = PrimaryKey(int, auto=True)
    dept_id = Required(int)
    category = Required(str)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    owner = Required(str)
    # meeting = Optional(Meeting)


class Meeting_subject(db.Entity):
    id = PrimaryKey(int, auto=True)
    dept_id = Required(int)
    subject = Required(str)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    owner = Required(str)


# --------NRE-DATA---------
class Nre_project(db.Entity):
    id = PrimaryKey(int, auto=True)
    lob = Optional(str)
    classification = Optional(str)
    dept = Required(str)
    project = Required(str)
    model = Required(str)
    a_code = Required(str)
    data = Set("Nre_data")
    gmt_create = Required(datetime, default=lambda: datetime.now())
    owner = Required(str)
    status = Required(str, default="open")
    close_time = Optional(datetime)


class Nre_data(db.Entity):
    id = PrimaryKey(int, auto=True)
    project = Required(Nre_project)
    data = Optional(Json)
    gmt_create = Required(datetime, default=lambda: datetime.now())
    owner = Required(str)
    year = Required(int)
    month = Required(int)
    role = Required(str)


db.generate_mapping(create_tables=True)
