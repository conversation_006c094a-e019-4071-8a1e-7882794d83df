# -*- coding: utf-8 -*-
import dash_ag_grid as dag
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import numpy as np
import orjson
import pandas as pd
from dash import set_props
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    html,
)
from dash_player import DashPlayer

from common import id_factory, parse_search, read_sql
from config import pool
from tasks import bg_label_print, task_take_off_shelf
import feffery_utils_components.alias as fuc

id = id_factory(__name__)


def get_roll_data(
    roll_id,
    placer,
    first: str = "先发旧料",
    enough: bool = False,
):
    sql = "select id,stockno,checkcode,prtnobt,stock_id,uid,qty as bom_qty,\
        short as short_qty,finished_date,prtno,share,share_prtno,placer,share_uid \
        from ssp.stock_roll where roll_id=%s"
    params = [roll_id]
    df = read_sql(sql, params=params)
    # sql = "select * from ssp.stock_roll where roll_id=%s"
    # params = [roll_id]
    # df = df.drop("short", axis=1)

    prtno = df["prtno"].iloc[0]
    prtno = prtno.split(",")
    sql = "select id as stockout_id,stock_id,source,qty from stockout \
        where prtno in %s and type=%s"
    params = (prtno, "sm")
    stockout = read_sql(sql, params=params)
    stockout["source"] = stockout["source"].replace({"bom": "qty"})
    stockout_id = stockout.groupby("stock_id", as_index=False).agg(
        {"stockout_id": list}
    )

    stockout = (
        pd.pivot_table(
            stockout,
            index="stock_id",
            columns="source",
            values="qty",
            aggfunc="sum",
        )
        .reset_index()
        .reindex(columns=["stock_id", "short", "qty"])
    )
    # stockout = stockout.groupby("stock_id", as_index=False).agg({"short": "sum"})

    df = df.merge(stockout, on="stock_id", how="left").merge(
        stockout_id, on="stock_id", how="left"
    )
    c1 = df["qty"].isna()
    c2 = df["short"].isna()

    df["qty"] = np.where(c1 & c2, df["bom_qty"], df["qty"])
    df["short"] = np.where(c1 & c2, df["short_qty"], df["short"])
    df["qty"] = df["qty"].fillna(0).astype(int)
    # print(df)

    # 过滤掉既有散料，又有卷料，出库是散料的记录
    # 已经在生成卷料清单时过滤了，这里不需要，可以删除
    # prtno = df["prtno"].iloc[0]
    # prtno = prtno.split(",")
    # sql = "select stock_id,stockno as stockout_stockno from stockout \
    #     where prtno in %s and type=%s"
    # params = (prtno, "sm")
    # stockout = read_sql(sql, params=params)
    # stockout = stockout.groupby("stock_id").first().reset_index()
    # df = df.merge(stockout, on="stock_id", how="left")
    # c1 = df["stockno"].str.match(r"^\d{7}$", na=False)
    # c2 = ~df["stockout_stockno"].str.match(r"^\d{7}$", na=True)
    # df = df.loc[~(c1 & c2)]

    df["checked"] = np.where(df["uid"].notna(), True, False)
    df["share"] = (
        df["share"]
        .str.replace("^,", "", regex=True)
        .str.replace("[SHZW]{4}\d{4}", "", regex=True)
    )

    # placer = df["placer"].iloc[0]
    # prtno = df["prtno"].iloc[0]
    stockno = df["stockno"].dropna().tolist()
    finished = df["finished_date"].notna().any()

    dfx = df.loc[df["uid"].notna()]
    df = df.loc[df["uid"].isna()]

    if not finished and not df.empty:
        shape0 = df.shape[0]
        df = df.drop(["uid", "stockno", "share_uid", "share_prtno"], axis=1)
        stock_id = df["stock_id"].values.tolist()

        # -----内部考虑共用料-----
        if placer != "FLX2010":
            sql = "select id,stock_id,uid as share_uid,placer,prtno,finished_date, \
                check_date from stock_roll \
                where check_date is not null \
                and roll_id!=%s \
                and uid in \
                (select distinct uid from stock_in_out \
                where type=%s and uid not in \
                (select uid from stockno_list where uid is not null))"
            params = [roll_id, "备料"]
            dfr = read_sql(sql, params=params)

            dfr["check_date"] = pd.to_datetime(dfr["check_date"], errors="coerce")
            dfr = dfr.loc[dfr.groupby("share_uid")["check_date"].idxmax()]

            dfr1 = dfr[dfr["placer"] == placer]
            dfr1 = dfr1.rename(columns={"prtno": "share_prtno"})
            # dfr1 = dfr1.loc[dfr1.groupby("stock_id")["id"].idxmax()]

            dfr2 = dfr[dfr["placer"] != placer]
            # dfr2 = dfr2.loc[dfr2.groupby("stock_id")["id"].idxmin()]
            dfr2 = dfr2.rename(
                columns={"share_uid": "share_uid2", "prtno": "share_prtno2"}
            )
            # dfr1 = dfr1.loc[~dfr1["share_uid"].isin(dfr2["share_uid2"])]

            df = df.merge(
                dfr1[["stock_id", "share_uid", "share_prtno"]],
                on="stock_id",
                how="left",
            ).merge(
                dfr2[["stock_id", "share_uid2", "share_prtno2"]],
                on="stock_id",
                how="left",
            )
            c1 = df["share_uid2"].notna()
            c2 = df["share"].isna()
            df["share"] = np.where(c1 & c2, df["share_prtno2"], df["share"])
        else:
            df["share_uid"] = None
            df["share"] = None
            df["share_prtno"] = None
            df["share_prtno2"] = None

        with pool.connection() as conn:
            with conn.cursor() as cu:
                sql = "update ssp.stockno_list set block=null where block=%s"
                cu.execute(sql, [roll_id])
                conn.commit()

        sql = "select b.stock_id,a.uid,a.stockno,a.id as stockno_id \
            from stockno_list a \
            left join stock_uid b on a.uid=b.uid \
            where b.stock_id in %s and type=%s and block is null"

        params = [stock_id, "电子料架"]
        df1 = read_sql(sql, params=params)
        if not df1.empty:
            df = df.merge(df1, on="stock_id", how="left")
            df["uid2"] = df["uid"].str.slice(1).fillna(0).astype("int64")

            if enough:
                sql = "select uid,sum(qty) as sum_qty from stock_in_out \
                    where uid in %s group by uid"
                params = df1["uid"].values.tolist()
                dfs = read_sql(sql, params=[params])
                df = df.merge(dfs, on="uid", how="left")

                c1 = df["sum_qty"] < df["qty"]
                if first == "先发新料":
                    df["uid2"] = np.where(c1, 0, df["uid2"])
                else:
                    df["uid2"] = np.where(c1, 999999999999999999, df["uid2"])

            if first == "先发新料":
                df = df.loc[df.groupby("stock_id")["uid2"].idxmax()]
            else:
                df = df.loc[df.groupby("stock_id")["uid2"].idxmin()]

            assert df.shape[0] == shape0
        else:
            df["stockno"] = None
            df["uid"] = None

        # --------线上还回来的共用料--------
        c1 = df["share_uid"].notna()
        c2 = df["share_uid"] != df["uid"]
        df["stockno"] = np.where(c1 & c2, None, df["stockno"])
        df["uid"] = np.where(df["share_uid"].notna(), df["share_uid"], df["uid"])
        stockno = df["stockno"].dropna().tolist()

        if stockno:
            with pool.connection() as conn:
                with conn.cursor() as cu:
                    sql = "update ssp.stockno_list set block=%s where stockno in %s"
                    cu.execute(sql, [roll_id, stockno])
                    conn.commit()

        # --------剩余没有上电子料架的大卷料------------
        sql = "SELECT id as stock_id,stockno as stockno_o FROM stock \
            WHERE stockno REGEXP %s AND AREA=%s"
        dfo = read_sql(sql, params=["^[A-Z][0-9]{4}", "SH"])

        df = df.merge(dfo, on="stock_id", how="left")
        c1 = df["stockno"].isna()
        c2 = df["stockno_o"].notna()
        df["stockno"] = np.where(c1 & c2, df["stockno_o"], df["stockno"])

    df = pd.concat([dfx, df])
    df = df.sort_values(by=["share_prtno", "share", "stockno", "prtnobt", "checkcode"])
    precent = f"{len(stockno)}/{df.shape[0]}"
    return df, precent


def layout(user: dict, batch: int, placer: str, prtno: str, **kwargs):
    if placer == "FLX2010":
        df, precent = get_roll_data(batch, placer)
    else:
        df, precent = get_roll_data(batch, placer)

    table = dag.AgGrid(
        id=id("table"),
        className="ag-theme-quartz",
        rowStyle={"border": "1px solid black"},
        columnDefs=[
            {
                "field": "checked",
                "headerName": "",
                "width": 50,
                "cellRenderer": "agCheckboxCellRenderer",
                "cellRendererParams": {"disabled": True},
            },
            {"field": "stockno", "headerName": "库位号", "width": 100},
            {"field": "checkcode", "headerName": "料号", "width": 150},
            {"field": "prtnobt", "headerName": "板面", "width": 200},
            {"field": "qty", "headerName": "出库量", "width": 80},
            {"field": "short", "headerName": "缺料量", "width": 80},
            {"field": "share", "headerName": "机器共用", "width": 120},
            {"field": "share_prtno", "headerName": "已发共用", "width": 130},
        ],
        rowData=df.to_dict("records"),
        # columnSize="autoSize",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "multiple",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            "domLayout": "print",
            "animateRows": False,
        },
        getRowId="params.data.id",
    )
    return dmc.Container(
        dmc.Stack(
            [
                dmc.Group(
                    [
                        # dmc.RadioGroup(
                        #     [
                        #         dmc.Radio(label="内部", value="内部", color="red"),
                        #         dmc.Radio(label="外包", value="外包", color="blue"),
                        #     ],
                        #     id=id("share"),
                        # ),
                        fuc.ListenUnload(id=id("unload")),
                        dmc.RadioGroup(
                            [
                                dmc.Radio(
                                    label="先发旧料", value="先发旧料", color="red"
                                ),
                                dmc.Radio(
                                    label="先发新料", value="先发新料", color="blue"
                                ),
                            ],
                            id=id("first"),
                            value="先发旧料",
                        ),
                        dmc.Checkbox(id=id("enough-qty"), label="满足数量"),
                        dmc.Switch(
                            id=id("switch"),
                            label="亮灯开关",
                            color="green" if placer == "MY300" else "red",
                            onLabel=dmc.Text("打开", size=13),
                            offLabel=dmc.Text("关闭", size=13),
                        ),
                        dmc.Button(
                            "备料完成",
                            id=id("finish"),
                            size="xs",
                            variant="outline",
                            # disabled=True,
                        ),
                        dmc.Button(
                            "打印清单",
                            id=id("print"),
                            size="xs",
                            variant="outline",
                            color="orange",
                        ),
                    ],
                    position="apart",
                    align="end",
                ),
                dmc.Divider(),
                dbc.Input(id=id("uid"), autofocus=True, debounce=True),
                html.Div(id=id("alert")),
                DashPlayer(
                    id=id("fail-audio"),
                    url="/assets/fail.mp3",
                    playing=False,
                    controls=True,
                    style={"display": "none"},
                ),
                html.Div(
                    [
                        dmc.Center(
                            f"{prtno}卷料清单({precent})",
                            id=id("title"),
                            style={"font-weight": "bold", "font-size": "20px"},
                        ),
                        dmc.LoadingOverlay(table),
                    ],
                    id=id("print-content"),
                ),
            ]
        ),
        style={"page-break-after": "always"},
    )


# @callback(
#     Output(id("table"), "rowData"),
#     Output(id("share"), "value"),
#     Output(id("title"), "children"),
#     Input(id("share"), "value"),
#     State("url", "search"),
#     # prevent_initial_call=False,
# )
# def share_select(share, url):
#     url = parse_search(url)
#     roll_id = url.get("batch")
#     print(roll_id, share)
#     df, prtno, stockno, placer = get_roll_data(roll_id, share=share)
#     share_type = "外包" if placer == "FLX2010" else "内部"
#     # share_type = "内部"
#     title = f"{prtno}卷料清单({len(stockno)}/{df.shape[0]})"

#     # df, *_ = get_roll_data(roll_id, share=share)
#     # if share == "外包":
#     #     with pool.connection() as conn:
#     #         with conn.cursor() as cu:
#     #             sql = "update ssp.stock_roll set share_type=%s where roll_id=%s"
#     #             cu.execute(sql, (share, roll_id))
#     #             conn.commit()

#     return df.to_dict("records"), share_type, title


@callback(
    Input(id("unload"), "unloaded"),
    State("url", "search"),
)
def unload_block(unloaded, url):
    if unloaded is None:
        raise PreventUpdate

    url = parse_search(url)
    roll_id = url.get("batch")
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.stockno_list set block=null where block=%s"
            cu.execute(sql, [roll_id])
            conn.commit()


@callback(
    Output(id("table"), "rowData"),
    Input(id("first"), "value"),
    State(id("enough-qty"), "checked"),
    State("url", "search"),
)
def first_select(first, enough, url):
    url = parse_search(url)
    roll_id = url.get("batch")
    placer = url.get("placer")
    df, *_ = get_roll_data(roll_id, placer, first=first, enough=enough)
    return df.to_dict("records")


@callback(
    Output(id("table"), "rowData"),
    Input(id("enough-qty"), "checked"),
    State(id("first"), "value"),
    State("url", "search"),
)
def enough_qty(enough, first, url):
    url = parse_search(url)
    roll_id = url.get("batch")
    placer = url.get("placer")
    df, *_ = get_roll_data(roll_id, placer, first=first, enough=enough)
    return df.to_dict("records")


@callback(
    Output(id("finish"), "disabled"),
    Input(id("switch"), "checked"),
    State(id("table"), "rowData"),
    State(id("table"), "selectedRows"),
    running=[
        (Output(id("switch"), "disabled"), True, False),
    ],
)
def switch_light(checked, data, selected_rows):
    if checked is None:
        raise PreventUpdate

    df = pd.DataFrame(data)

    # 外部门数量等于0不亮灯
    prtno = df["prtno"].iloc[0]
    prtno = prtno.split(",")[0]
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select category from dept where id=(select dept_id from prt where prtno=%s)"
            cu.execute(sql, [prtno])
            res = cu.fetchone()
            if res:
                category = res["category"]
                if category == "OTHER":
                    df = df.loc[df["qty"] > 0]

    placer = df["placer"].iloc[0]
    finished = df["finished_date"].notna().any()
    if finished:
        sql = "select stockno from ssp.stockno_list where uid in %s"
        dfx = read_sql(sql, params=[df["uid"].dropna().tolist()])
        if dfx.empty:
            return True
        stockno = dfx["stockno"].tolist()
        if checked:
            if placer == "MY300":
                task_take_off_shelf(stockno, color="Green")
            else:
                task_take_off_shelf(stockno, color="Red")
        else:
            task_take_off_shelf(stockno, color="Gray")
        return True

    df = df.loc[~df["checked"]]
    df = df.loc[df["stockno"].str.match(r"^\d{7}$", na=False)]
    if df.empty:
        if checked:
            return True
        else:
            if not finished:
                return False
            else:
                return True

    stockno = df["stockno"].dropna().tolist()
    if checked:
        if placer == "MY300":
            task_take_off_shelf(stockno, color="Green")
        else:
            task_take_off_shelf(stockno, color="Red")
        return True
    else:
        task_take_off_shelf(stockno, color="Gray")
        if not finished:
            return False
        else:
            return True


@callback(
    Output(id("finish"), "disabled"),
    Output(id("print"), "disabled"),
    Input(id("finish"), "n_clicks"),
    State(id("table"), "rowData"),
    State("url", "search"),
    State("user", "data"),
    running=[
        (Output(id("finish"), "loading"), True, False),
    ],
)
def confirm_finish(n_clicks, data, url, user):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(url)
    roll_id = url.get("batch")
    prtno = url.get("prtno")
    prtno = prtno.split(",")
    operator = user.get("nt_name")
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "update ssp.stockno_list set block=null where block=%s"
            cu.execute(sql, [roll_id])
            sql = "update ssp.prt set mat2_date=now() where prtno in %s"
            cu.execute(sql, [prtno])

            df = pd.DataFrame(data)
            df["finished_date"] = pd.Timestamp.now()
            df["operator"] = operator
            df = df.replace({np.nan: None, "": None})

            sql = "update ssp.stock_roll set uid=%s,stockno=%s,share_uid=%s,\
                share_prtno=%s,finished_date=%s,operator=%s where id=%s"
            params = df[
                [
                    "uid",
                    "stockno",
                    "share_uid",
                    "share_prtno",
                    "finished_date",
                    "operator",
                    "id",
                ]
            ].values.tolist()
            cu.executemany(sql, params)

            c1 = df["uid"].notnull()
            c2 = df["uid"] != "z999999999999999999"
            c3 = df["qty"].notna()
            df1 = df.loc[c1 & c2 & c3]
            if not df1.empty:
                df1["type"] = "备料"
                df1["qty"] = -df1["qty"]
                sql = "insert into ssp.stock_in_out\
                    (roll_id,uid,stock_id,stockno_id,qty,owner,type) \
                    select %s,%s,%s,%s,%s,%s,%s from dual where exists \
                    (select id from ssp.stock_uid where uid=%s)"

                params = (
                    df1.reindex(
                        columns=[
                            "id",
                            "uid",
                            "stock_id",
                            "stockno_id",
                            "qty",
                            "operator",
                            "type",
                            "uid",
                        ]
                    )
                    .replace({np.nan: None})
                    .values.tolist()
                )
                cu.executemany(sql, params)

                # sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
                # params = df1[["uid"]].values.tolist()
                # cu.executemany(sql, params)
        conn.commit()

    return True, False


clientside_callback(
    ClientsideFunction(
        namespace="clientside",
        function_name="update_checked",
    ),
    Input(id("uid"), "n_submit"),
    State(id("table"), "id"),
    State(id("uid"), "value"),
)


@callback(
    Output(id("uid"), "value"),
    Input(id("uid"), "n_submit"),
    State(id("uid"), "value"),
    State(id("table"), "rowData"),
    State("user", "data"),
)
def check_uid(n, uid, data, user):
    if not n or not uid:
        raise PreventUpdate

    if uid.startswith("R"):
        uid = uid[1:]
    uid = uid.strip()

    data = [i for i in data if i["uid"] == uid]
    if data:
        data = data[0]
        # stockno = data["stockno"]
        roll_id = data["id"]
        placer = data["placer"]
        with pool.connection() as conn:
            with conn.cursor() as cu:
                sql = "select stockno from ssp.stockno_list where uid=%s"
                cu.execute(sql, [uid])
                res = cu.fetchone()
                if not res:
                    set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
                    set_props(
                        id("alert"),
                        {
                            "children": fac.Message(
                                content=f"{uid}该料盘不在料架上",
                                type="error",
                            )
                        },
                    )
                    return None

                stockno = res.get("stockno")
                task_take_off_shelf([stockno], color="Gray")
                sql = "update ssp.stockno_list set uid=null,stock_id=null where uid=%s"
                cu.execute(sql, [uid])

                sql = "update ssp.stock_roll set uid=%s,stockno=%s,check_date=now() where id=%s"
                cu.execute(sql, [uid, stockno, roll_id])

                sql = "update ssp.stockout set lable=now(),stockoutdate2=now(),owner2=%s where id in %s"
                params = [user.get("nt_name"), data["stockout_id"]]
                cu.execute(sql, params)

                conn.commit()
                if placer == "FLX2010":
                    prtno = data.get("prtno")
                    checkcode = data.get("checkcode")
                    sql = "select group_CONCAT(designno) AS designno,des \
                        from ssp.smbom where prtno=%s and checkcode=%s"
                    cu.execute(sql, [prtno, checkcode])
                    res = cu.fetchone()
                    designno: str = res.get("designno")
                    if designno:
                        data["designno"] = designno
                        data["des"] = res.get("des")
                        data["qpa"] = designno.count(",") + 1
                        data["qty"] = data["qty"] + (data["short"] or 0)

                    data["label_template"] = "stock_out"
                    bg_label_print(orjson.dumps([data]).decode("utf-8"))

        set_props(
            id("alert"),
            {"children": fac.Message(content=f"{uid}扫码核对正确", type="success")},
        )
    else:
        set_props(id("fail-audio"), {"playing": True, "seekTo": 0})
        set_props(
            id("alert"),
            {"children": fac.Message(content=f"{uid}料盘码有误", type="error")},
        )
    return None


clientside_callback(
    """
    function (n) {
    if (n>0) {
         var printContents = document.getElementById('pages-stock-roll-layout-print-content').innerHTML;
         var originalContents = document.body.innerHTML;

         document.body.innerHTML = printContents;

         window.print();

         document.body.innerHTML = originalContents;
         location.reload()

        return window.dash_clientside.no_update
        }
    }
    """,
    Input(id("print"), "n_clicks"),
    prevent_initial_call=True,
)
