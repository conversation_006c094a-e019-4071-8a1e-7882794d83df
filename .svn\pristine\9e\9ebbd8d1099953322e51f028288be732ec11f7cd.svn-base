window.myNamespace = Object.assign({}, window.myNamespace, {
  tabulator: {
    dateEditor: function (cell, onRendered, success, cancel) {
      //cell - the cell component for the editable cell
      //onRendered - function to call when the editor has been rendered
      //success - function to call to pass the successfuly updated value to Tabulator
      //cancel - function to call to abort the edit and return to a normal cell

      //create and style input
      var cellValue = moment(cell.getValue(), "YYYY-MM-DD").format("YYYY-MM-DD"),
        input = document.createElement("input");

      input.setAttribute("type", "date");

      input.style.padding = "4px";
      input.style.width = "100%";
      input.style.boxSizing = "border-box";

      input.value = cellValue;

      onRendered(function () {
        input.focus();
        input.style.height = "100%";
      });

      function onChange() {
        if (input.value != cellValue) {
          success(moment(input.value, "YYYY-MM-DD").format("YYYY-MM-DD"));
        } else {
          cancel();
        }
      }

      //submit new value on blur or change
      input.addEventListener("blur", onChange);

      //submit new value on enter
      input.addEventListener("keydown", function (e) {
        if (e.keyCode == 13) {
          onChange();
        }

        if (e.keyCode == 27) {
          cancel();
        }
      });

      return input;
    },

    // 关于隐藏列
    headerMenu: function (table) {
      var menu = [];
      var columns = table.getTable().getColumns();
      for (let column of columns) {
        //create checkbox element using font awesome icons
        let icon = document.createElement("i");
        icon.classList.add("fa");
        icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");
        //build label
        let label = document.createElement("span");
        let title = document.createElement("span");
        title.textContent = " " + column.getDefinition().title;
        label.appendChild(icon);
        label.appendChild(title);
        //create menu item
        menu.push({
          label: label,
          action: function (e) {
            //prevent menu closing
            e.stopPropagation();
            //toggle current column visibility
            column.toggle();
            //change menu item icon
            if (column.isVisible()) {
              icon.classList.remove("fa-square");
              icon.classList.add("fa-check-square");
            } else {
              icon.classList.remove("fa-check-square");
              icon.classList.add("fa-square");
            }
          }
        });
      }
      return menu;
    },
    // ------------------------------------
    headerMenu2: function (table) {
      var menu = [];
      var columns = table.getTable().getColumns();
      for (let column of columns) {
        // if ('headerMenu' in column) {
        //使用字体图标创建复选框元素

        console.log("我想要的列元素：", column);

        let icon = document.createElement("i");
        icon.classList.add("fa");
        icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");
        //build label
        let label = document.createElement("span");
        let title = document.createElement("span");
        title.textContent = " " + column.getDefinition().title;
        label.appendChild(icon);
        label.appendChild(title);
        //create menu item
        menu.push({
          label: label,
          action: function (e) {
            //prevent menu closing
            e.stopPropagation();
            //切换当前列的可见性
            column.toggle();
            //change menu item icon
            if (column.isVisible()) {
              icon.classList.remove("fa-square");
              icon.classList.add("fa-check-square");
            } else {
              icon.classList.remove("fa-check-square");
              icon.classList.add("fa-square");
            }
          }
        });
        // }
      }
      return menu;
    },

    headerMenu3: function (table) {
      var menu = [];
      var columns = table.getTable().getColumns();
      let arr = ['des', 'smd_dip', 'mfgname', 'mfgpn', 'plant', 'plant_qty', 'location', 'lot', 'price', 'total_price', 'pcb_date', 'start_date', 'pr_no', 'po_no', 'sub_deltapn', 'sub_stockno', 'pur', 'pur_date', 'es_date_lasttime', 'mat_group', 'mat_type', 'received_qty', 'qissue'];
      for (let column of columns) {
        // if ('headerMenu' in column) {
        //使用字体图标创建复选框元素
        if (arr.includes(column.getField())) {

          let icon = document.createElement("i");
          icon.classList.add("fa");
          icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");
          //build label
          let label = document.createElement("span");
          let title = document.createElement("span");
          title.textContent = " " + column.getDefinition().title;
          label.appendChild(icon);
          label.appendChild(title);
          //create menu item
          menu.push({
            label: label,
            action: function (e) {
              //prevent menu closing
              e.stopPropagation();
              //切换当前列的可见性
              column.toggle();
              //change menu item icon
              if (column.isVisible()) {
                icon.classList.remove("fa-square");
                icon.classList.add("fa-check-square");
              } else {
                icon.classList.remove("fa-check-square");
                icon.classList.add("fa-square");
              }
            }
          });
          // }
        }
      }
      return menu;
    },

    headerMenu4: function (table) {
      var menu = [];
      var columns = table.getTable().getColumns();
      let arr = ['plant', 'plant_qty', 'mat_catelogue', 'lot', 'price', 'total_price', 'pcb_date', 'sub_stockno', 'pr_no', 'po_no', 'signed', 'sub_deltapn', 'sub_stockno'];
      for (let column of columns) {
        // if ('headerMenu' in column) {
        //使用字体图标创建复选框元素
        if (arr.includes(column.getField())) {

          let icon = document.createElement("i");
          icon.classList.add("fa");
          icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");
          //build label
          let label = document.createElement("span");
          let title = document.createElement("span");
          title.textContent = " " + column.getDefinition().title;
          label.appendChild(icon);
          label.appendChild(title);
          //create menu item
          menu.push({
            label: label,
            action: function (e) {
              //prevent menu closing
              e.stopPropagation();
              //切换当前列的可见性
              column.toggle();
              //change menu item icon
              if (column.isVisible()) {
                icon.classList.remove("fa-square");
                icon.classList.add("fa-check-square");
              } else {
                icon.classList.remove("fa-check-square");
                icon.classList.add("fa-square");
              }
            }
          });
          // }
        }
      }
      return menu;
    },
    headerMenu5: function (table) {
      var menu = [];
      var columns = table.getTable().getColumns();
      let arr = ['quarter_times', 'quarter_qty', 'half_times', 'half_qty', 'year_times', 'year_qty', 'year_max', 'ss', 'safetystock', 'stock_qty', 'transit_qty', 'order_condition', 'order_qty', 'dept_qty'];
      for (let column of columns) {
        // if ('headerMenu' in column) {
        //使用字体图标创建复选框元素
        if (arr.includes(column.getField())) {

          let icon = document.createElement("i");
          icon.classList.add("fa");
          icon.classList.add(column.isVisible() ? "fa-check-square" : "fa-square");
          //build label
          let label = document.createElement("span");
          let title = document.createElement("span");
          title.textContent = " " + column.getDefinition().title;
          label.appendChild(icon);
          label.appendChild(title);
          //create menu item
          menu.push({
            label: label,
            action: function (e) {
              //prevent menu closing
              e.stopPropagation();
              //切换当前列的可见性
              column.toggle();
              //change menu item icon
              if (column.isVisible()) {
                icon.classList.remove("fa-square");
                icon.classList.add("fa-check-square");
              } else {
                icon.classList.remove("fa-check-square");
                icon.classList.add("fa-square");
              }
            }
          });
          // }
        }
      }
      return menu;
    },
    //----------------------------------

    getFormatter: function (cell, formatterparams) {
      let val = cell.getValue()
      return "<span style='color:#1abc9c'>" + val + "</span>";
    },
    downloadFiles: function (e, cell) {
      let val = cell.getValue()
      if (val !== null) {
        let filename = val.split("/").slice(-1)
        //let filename = doc.split(".").slice(0,1)
        let x = new XMLHttpRequest();　　　　　　　//禁止浏览器缓存；否则会报跨域的错误
        x.open("GET", val, true);
        x.responseType = 'blob';
        x.setRequestHeader("Access-Control-Allow-Origin", "*");
        x.onload = function (e) {
          var blob = new Blob([this.response], {
            type: "application/vnd.ms-excel"
          });
          let url = window.URL.createObjectURL(blob)  //生成同源url
          let a = document.createElement('a');
          a.href = url
          a.download = filename
          a.click()
        }
        x.send()
      }
    },
    group_header: function (value, count, data, group) {
      if (value == '待排定') {
        return value + '----' + count + '笔----条件：Now-PCB时间> 3工作天'
      }
      else if (value == '已排定') {
        return value + '----' + count + '笔----条件：上线时间-准备时间>3工作天'
      }
      else if (value == '贴片中') {
        return value + '----' + count + '笔----条件：Now-SMT开始时间>1天'
      }
      else if (value == '插件中') {
        return value + '----' + count + '笔----条件：Now-DIP开始时间>3工作天'
      }
      else {
        return value
      }
    },
    meetingProcessHeader: function (value, count, data, group) {
      if (value == 'open') {
        return '待处理 ' + '(' + count + ' items)'
      }
      else if (value == 'processing') {
        return '待审核 ' + '(' + count + ' items)'
      }
      else {
        return value
      }
    },
    cellClickSpQuery: function (e, cell) {
      return cell.getValue()
    },
    RowMenu: function (table) {
      var menu = [];
      menu.push({
        label: "<i class='fa fa-trash'></i> Delete Row",
        action: function (e, row) {
          row.delete();
        }
      });
      return menu;
    },
    RowMenu1: function (table) {
      var menu = [];
      menu.push({
        label: "<i class='fa fa-trash'></i> Delete Row",
        action: function (e, row) {
          row.delete();
        }
      });
      return menu;
    },
    RowMenu2: function (table) {
      menu = [
        {
          label: "Add Batch",
          action: function (e, row) {
            row.getCell('action').setValue('add');
            // debugger;
            // row.getData().action = 'qq';
          }
        },
      ]
      return menu;
    },
    RowMenu3: function (table) {
      menu = [
        {
          label: "Add Plan",
          action: function (e, row) {
            row.getCell('action').setValue('add');
            // debugger;
            // row.getData().action = 'qq';
          }
        },
      ]
      return menu;
    },

    // 打印处理模块 pending  now-材料发起日<3  的 某行颜色
    rowFormatter1: function (row) {
      var data = row.getData(); //get data object for row
      if (data.pur_status == "pending" && data.start_date) {
        row.getElement().style.backgroundColor = "#FF0000"; //apply css change to row element
      }
    },

    // 打印调料模块 工厂库存 为0   工厂库存 < 需求数量的 台达料号不存在 某行颜色
    rowFormatter2: function (row) {
      var data = row.getData();
      if (data.plant_qty == "0" || data.plant_qty == null || data.id == null) {
        row.getElement().style.backgroundColor = "#FA8072";
      } else if (data.plant_qty < data.qty) {
        row.getElement().style.backgroundColor = "#FFD700";
      }

    },
    // 收件箱模块 厂商不存在（id为空） 某行颜色
    rowFormatter3: function (row) {
      var data = row.getData();
      if (data.mfgname == null) {
        row.getElement().style.backgroundColor = "#FA8072";
      }
    },

    // 处理模块的颜色提醒 时间的判断
    rowFormatter4: function (row) {
      var data = row.getData();
      function getBeforeDate(n) {
        var now = new Date();
        var aftertime = new Date(n);
        var year = now.getFullYear();
        var mon = now.getMonth() + 1;
        var day = now.getDate();
        var year_after = aftertime.getFullYear();
        var mon_after = aftertime.getMonth() + 1;
        var day_after = aftertime.getDate();
        var chs = 0;
        //获取当月的天数
        function DayNumOfMonth(Year, Month) {
          return 32 - new Date(Year, Month - 1, 32).getDate();
        }
        if (aftertime.getTime() - now.getTime() < 0) {
          var temp1 = day_after;
          var temp2 = mon_after;
          var temp3 = year_after;
          day_after = day;
          mon_after = mon;
          year_after = year;
          day = temp1;
          mon = temp2;
          year = temp3;
        }
        if (year == year_after) {//不跨年
          if (mon == mon_after) {//不跨年不跨月
            chs += day_after - day;
          } else {//不跨年跨月
            chs += DayNumOfMonth(year, mon) - day + 1;//加上第一个不满的
            for (var i = 1; i < mon_after - mon; i++) {
              chs += DayNumOfMonth(year, mon + i);
            }
            chs += day_after - 1;//加上
          }
        } else {//存在跨年
          chs += DayNumOfMonth(year, mon) - day + 1;//加上开始年份不满的一个月
          for (var m = 1; m < 12 - mon; m++) {
            chs += DayNumOfMonth(year, mon + m);
          }
          for (var j = 1; j < year_after - year; j++) {
            if ((year + j) % 400 == 0 || (year + j) % 4 == 0 && (year + j) % 100 != 0) {
              chs += 366;
            } else {
              chs += 365;
            }
          }
          for (var n = 1; n <= mon_after; n++) {
            chs += DayNumOfMonth(year_after, n);
          }
          chs += day_after - 1;
        }
        if (aftertime.getTime() - now.getTime() < 0) {
          return -chs;
        } else {
          return chs;
        }
      }

      if (data.es_date != null && getBeforeDate(data.es_date) < 0) {
        row.getElement().style.backgroundColor = "#FFD700";
      } else if (data.pur_status == "pending" && getBeforeDate(data.start_date) >= 3 && getBeforeDate(data.start_date) > 0) {
        row.getElement().style.backgroundColor = "#FA8072";
      }
    },


    // 新增模块 台达料号不存在，（描述为空） 某行颜色
    rowFormatter5: function (row) {
      var data = row.getData();
      if (data.in_db == 0) {
        row.getElement().style.backgroundColor = "#FA8072";
      }
    },
    // formatter6: function (cell, formatterParams) {
    //   var value = cell.getValue();
    //   if (value) {
    //     return "<span style='color:red; font-weight:bold;'>" + value + "</span>";
    //   } else {
    //     return value;
    //   }
    // },

    //厂商不存在的提醒
    formatter6: function (cell, formatterParams) {
      var value = cell.getValue();
      // debugger;
      var row = cell.getRow();
      var data = row.getData();
      if (data.vendor_exists == true) {
        return value;
      } else {
        return "<span style='color:red; font-weight:bold;'>" + value + "</span>";
      }
    },
    formatter7: function (cell, formatterParams) {
      // 获取行的数据
      var value = cell.getValue();
      // 使用CSS样式实现自动换行
      cell.getElement().style.whiteSpace = "normal";

      var data = cell.getData();

      // 获取描述列的内容
      var description = data.action_item_description;

      // 设置行高，基于描述列的内容长度
      var height = Math.ceil(description.length / 20) * 20; // 每行20个字符
      cell._row.setHeight(height);
      // return value;
    },
    // //插件日期取电子料最晚交期，机构料最晚交期，贴片完成日期最大值
    // formatter7: function (cell, formatterParams, onRendered) {
    //   // onRendered(function () {
    //   //   $(cell.getElement());
    //   // });
    //   var row = cell.getRow();
    //   var ee_delivery_date = row.getData().ee_delivery_date;
    //   var me_delivery_date = row.getData().me_delivery_date;
    //   if ((ee_delivery_date == null) || (me_delivery_date == null)) {
    //     return null;
    //   } else {
    //     // debugger;
    //     dip_start_date = new Date(Math.max(new Date(ee_delivery_date), new Date(me_delivery_date)));
    //     // debugger;
    //     console.log(dip_start_date, ee_delivery_date, me_delivery_date);
    //     return dip_start_date.toISOString().slice(0, 10)
    //   }
    // },
    headerClick: function (e, column) {
      // window.alert(column);
      // debugger;
      column.getTable().getColumn('prtno').getCells()[0].edit();
      column.getTable().getColumn('prtno').getCells()[0].cancelEdit();
      // debugger;
      //e - the click event object
      //column - column component
    },
    actionValue: function (cell) {
      // var rows = table.getRows();
      // console.log(cell);
      // debugger;
      var values;
      if (cell.getData().purpose != null) {
        return { values: [] };
      };
      if (cell.getData().state == '已赠予') {
        return { values: [] };
      };
      if (cell.getData().purpose == null & cell.getData().state == '空闲') {
        values = ["自用", "外寄", "赠予"];

      } else {
        values = ["延期", "归还"];
      };

      // rows.forEach(function(row){
      //     var data = row.getData();

      //     values[data.fullname] = data.fullname;
      // });

      return { values: values };
    },

    actionValue2: function (cell) {
      // var rows = table.getRows();
      // console.log(cell);
      // debugger;
      var values;
      if (cell.getData().purpose == null) {
        values = ["更新", "删除", '转移'];

      } else {
        values = ["同意", "拒绝"];
      };

      // rows.forEach(function(row){
      //     var data = row.getData();

      //     values[data.fullname] = data.fullname;
      // });

      return { values: values };
    },

    actionValue3: function (cell) {
      var values;
      if (cell.getData().doc_type == "COST QUERY") {
        values = ["release", "canceled"];

      } else {
        values = ["release", "canceled", "follow_up"];
      };


      return { values: values };
    },
    cellEdited: function (cell) {
      console.log(cell);
      var row = cell.getRow();
      var bCell = row.getCell("action");
      bCell.setValue("update");
    },

    smEditorParams: function (cell) {
      var values;
      if (cell.getData().status == "未申请") {
        values = ["申请"];

      } else {
        values = [];
      };


      return { values: values };
    },
  }

});