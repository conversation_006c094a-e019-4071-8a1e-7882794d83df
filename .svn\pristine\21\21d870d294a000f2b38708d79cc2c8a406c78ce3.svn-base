# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import html

from common import id_factory

id = id_factory(__name__)


checkbox = dmc.CheckboxGroup(
    children=[
        dmc.Checkbox(label="物质成分", size="xs", mb=10),
        dmc.Checkbox(label="操作温度", size="xs", mb=10),
        dmc.Checkbox(label="lifetime/TCT", size="xs", mb=10),
        dmc.Checkbox(label="封装", size="xs", mb=10),
        dmc.Checkbox(label="reflow", size="xs", mb=10),
        dmc.Checkbox(label="耐燃等级", size="xs", mb=10),
        dmc.Checkbox(label="产地调查", size="xs", mb=10),
        dmc.Checkbox(label="国产厂商和替代料", size="xs", mb=10),
        dmc.Checkbox(label="材料属性:HF/WS/ESD/MSL/LF", size="xs", mb=10),
        dmc.Checkbox(label="材料属性: ANTI-S", size="xs", mb=10),
        dmc.Checkbox(label="材料属性: 双85", size="xs", mb=10),
        dmc.Checkbox(label="工业等级", size="xs", mb=10),
        dmc.Checkbox(label="Rohs", size="xs", mb=10),
        dmc.Checkbox(label="Automotive Parts Survey Form", size="xs", mb=10),
        dmc.Checkbox(label="重量调查", size="xs", mb=10),
        dmc.Checkbox(label="X-Ray Sensitivity Survey", size="xs", mb=10),
        dmc.Checkbox(label="ECCN", size="xs", mb=10),
    ],
    id="checkbox-group",
    label="调查内容",
    orientation="horizontal",
    withAsterisk=True,
    offset="md",
    size="xs",
    mb=5,
)


form_part_1 = dmc.Stack(
    [
        dmc.Center(dmc.Text("材料调查申请单", weight=700)),
        dmc.Divider(),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("材料信息填写", size="xs", weight=700),
                        dmc.Text(
                            "material information information",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.Select(
                            label="申请人",
                            # placeholder="Select one",
                            size="xs",
                            data=["A", "B"],
                        ),
                        dmc.Select(
                            label="申请部门",
                            size="xs",
                            data=["A", "B"],
                        ),
                        dmc.Select(
                            label="机种名",
                            # placeholder="Select one",
                            size="xs",
                            data=[
                                "A",
                                "B",
                            ],
                        ),
                        dmc.Select(
                            label="客户",
                            # placeholder="Select one",
                            size="xs",
                            data=[
                                "A",
                                "B",
                            ],
                        ),
                        dmc.Select(
                            label="直接生成报表",
                            placeholder="Select one",
                            size="xs",
                            data=[
                                "是",
                                "否",
                            ],
                        ),
                    ],
                    spacing=0,
                    align="end",
                    grow=True,
                ),
                dmc.Space(h=10),
                dmc.Paper(
                    [
                        checkbox,
                    ],
                    withBorder=True,
                    shadow="xs",
                    p="xs",
                    style={"background-color": "#f1f5f8"},
                ),
                dmc.Space(h=10),
                fac.AntdDraggerUpload(
                    apiUrl="/upload/",
                    text="上传",
                    id=id("attachment"),
                    lastUploadTaskRecord={},
                    # style={"width": "455px"},
                ),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
    ]
)

layout = dmc.Container(dmc.Stack([form_part_1]))
