# -*- coding: utf-8 -*-
from datetime import datetime

import dash_ag_grid as dag
import dash_mantine_components as dmc
import diskcache as dc
import feffery_antd_components.alias as fac
import numpy as np
import pandas as pd
from dash import ALL, MATCH, Patch, ctx
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dcc, html
from dash_iconify import DashIconify

from common import id_factory, read_sql
from components import create_sidebar
from config import cache
from tasks import bg_access_record

id = id_factory(__name__)
sub_type = {
    "全新料号": "new",
    "临时料号": "temp",
    "客户专用": "customer",
    "料号升级": "update",
    "规格书更新": "spec",
    "IC+软体组合件": "sw",
}
status_dict = {
    "accepted": "规格已接收",
    "submitted_ee": "电子审核",
    "submitted_me": "机构审核",
    "modify_ee": "文件更新",
    "modify_me": "文件更新",
    "open": "尚未受理",
    "close": "流程结束",
    "cancel": "任务取消",
}
D3_DATE_FMT = "params.value?d3.timeFormat('%m/%d')(params.value):''"
admin = ["zhen.liu", "weiming.li", "danfeng.chen", "mmeng.chen"]

column_defs1 = {
    "失效分析": [
        {
            "field": "id",
            "headerName": "",
            "width": 100,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
            "floatingFilter": False,
        },
        {"field": "status", "headerName": "状态", "width": 120},
        {"field": "deltapn", "headerName": "台达料号", "width": 200},
        {"field": "mfgname", "headerName": "厂商", "width": 200},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 120,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "gmt_update",
            "headerName": "更新日期",
            "width": 120,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.gmt_update)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "ce", "headerName": "处理人", "width": 150},
        {
            "field": "ce_comment",
            "headerName": "备注",
            "width": 300,
            "tooltipField": "ce_comment",
        },
    ],
    "料号申请": [
        {"field": "status", "headerName": "状态", "width": 120},
        {"field": "sub_type", "headerName": "料号类型", "width": 150},
        {"field": "mfgname", "headerName": "厂商", "width": 200},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "gmt_update",
            "headerName": "更新日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.gmt_update)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "ce", "headerName": "处理人", "width": 150},
        {
            "field": "ce_comment",
            "headerName": "备注",
            "width": 300,
            "tooltipField": "ce_comment",
        },
    ],
    "规格发行": [
        {"field": "status", "headerName": "状态", "width": 100},
        {"field": "owner", "headerName": "申请人", "width": 100},
        {"field": "doc_type", "headerName": "规格类型", "width": 150},
        {"field": "model", "headerName": "机种名称", "width": 200},
        {
            "field": "input_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.input_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "current", "headerName": "当前处理人", "width": 150},
    ],
    "机种缺料": [
        {"field": "proj", "headerName": "机种名称", "width": 200},
        {"field": "prtno", "headerName": "项目号", "width": 160},
        {"field": "deltapn", "headerName": "台达料号", "width": 160},
        {"field": "des", "headerName": "描述", "width": 160},
        {"field": "mfgname", "headerName": "厂商", "width": 150},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {"field": "qty", "headerName": "需求数量", "width": 100},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "es_date",
            "headerName": "预计交期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.es_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pur", "headerName": "处理人", "width": 100},
        {
            "field": "pur_remark",
            "headerName": "备注",
            "width": 100,
            "tooltipField": "pur_remark",
        },
    ],
    "测试用料": [
        {"field": "rd", "headerName": "申请人", "width": 130},
        {"field": "deltapn", "headerName": "台达料号", "width": 150},
        {"field": "mfgname", "headerName": "厂商", "width": 100},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 150},
        {"field": "qty", "headerName": "需求数量", "width": 90},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 90,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "es_date",
            "headerName": "预计交期",
            "width": 90,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.es_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pur", "headerName": "处理人", "width": 100, "tooltipField": "pur"},
        {
            "field": "pur_remark",
            "headerName": "备注",
            "width": 200,
            "tooltipField": "pur_remark",
        },
    ],
    "样制-待排定": [
        # {"field": "smstatus", "headerName": "状态", "width": 60},
        {"field": "id", "headerName": "id", "hide": True},
        {
            "field": "link",
            "headerName": "详细",
            "width": 90,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
        },
        {"field": "prtno", "headerName": "项目号", "width": 130},
        {"field": "project", "headerName": "项目名", "width": 150},
        {"field": "board", "headerName": "机种名", "width": 200},
        {"field": "pcbpn", "headerName": "PCB料号", "width": 100},
        {"field": "qty", "headerName": "需求数量", "width": 80},
        {
            "field": "startdate_sch",
            "headerName": "开始日期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.startdate_sch)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "fsdate_sch",
            "headerName": "首样交期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.fsdate_sch)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pcbstatus", "headerName": "PCB状态", "width": 100},
        {
            "field": "bom",
            "headerName": "BOM状态",
            "width": 100,
            "editable": False,
            "cellRendererSelector": {"function": "bomDownload(params)"},
            # "cellRenderer": "DMC_Button",
            # "cellRendererParams": {
            #     "leftIcon": "material-symbols:download",
            #     "color": "green",
            #     "radius": "sm",
            # },
        },
    ],
    "样制-生产中": [
        # {"field": "smstatus", "headerName": "状态", "width": 60},
        {"field": "id", "headerName": "id", "hide": True},
        {
            "field": "link",
            "headerName": "详细",
            "width": 90,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
        },
        {
            "field": "prtno",
            "headerName": "项目号",
            "width": 130,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
        },
        {"field": "project", "headerName": "项目名", "width": 150},
        {"field": "board", "headerName": "机种名", "width": 200},
        {"field": "pcbpn", "headerName": "PCB料号", "width": 100},
        {"field": "qty", "headerName": "需求数量", "width": 80},
        {
            "field": "startdate_sch",
            "headerName": "开始日期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.startdate_sch)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "fsdate_sch",
            "headerName": "首样交期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.fsdate_sch)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pcbstatus", "headerName": "PCB状态", "width": 100},
        {
            "field": "bom",
            "headerName": "BOM状态",
            "width": 100,
            "editable": False,
            "cellRendererSelector": {"function": "bomDownload(params)"},
            # "cellRenderer": "DMC_Button",
            # "cellRendererParams": {
            #     "leftIcon": "material-symbols:download",
            #     "color": "green",
            #     "radius": "sm",
            # },
        },
    ],
}

column_defs2 = {
    "失效分析": [
        {
            "field": "id",
            "headerName": "",
            "width": 100,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
        },
        {"field": "deltapn", "headerName": "台达料号", "width": 200},
        {"field": "mfgname", "headerName": "厂商", "width": 200},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "end_date",
            "headerName": "完成日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.end_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "ce", "headerName": "处理人", "width": 150},
        {
            "field": "conclusion",
            "headerName": "结论",
            "width": 300,
            "tooltipField": "conclusion",
        },
    ],
    "料号申请": [
        {"field": "sub_type", "headerName": "申请类型", "width": 200},
        {"field": "new_deltapn", "headerName": "正式料号", "width": 200},
        {"field": "mfgname", "headerName": "厂商", "width": 200},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "end_date",
            "headerName": "完成日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.end_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "ce", "headerName": "处理人", "width": 150},
        {
            "field": "ce_comment",
            "headerName": "备注",
            "width": 300,
            "tooltipField": "ce_comment",
        },
    ],
    "规格发行": [
        {"field": "owner", "headerName": "申请人", "width": 100},
        {"field": "doc_type", "headerName": "规格类型", "width": 150},
        {"field": "model", "headerName": "机种名称", "width": 200},
        {
            "field": "input_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.input_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "gmt_update",
            "headerName": "完成日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.gmt_update)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "spec", "headerName": "处理人", "width": 150},
        {
            "field": "attachment",
            "headerName": "文件下载",
            "width": 150,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
        },
        # {"field": "ce_comment", "headerName": "备注", "width": 300},
    ],
    "机种缺料": [
        {"field": "proj", "headerName": "机种名称", "width": 200},
        {"field": "prtno", "headerName": "项目号", "width": 160},
        {"field": "deltapn", "headerName": "台达料号", "width": 160},
        {"field": "mfgname", "headerName": "厂商", "width": 150},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {"field": "qty", "headerName": "需求数量", "width": 100},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "mat_receiveddate",
            "headerName": "到料日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.mat_receiveddate)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pur", "headerName": "处理人", "width": 100},
        {
            "field": "pur_remark",
            "headerName": "备注",
            "width": 100,
            "tooltipField": "pur_remark",
        },
    ],
    "测试用料": [
        {"field": "rd", "headerName": "申请人", "width": 130},
        {"field": "deltapn", "headerName": "台达料号", "width": 200},
        {"field": "mfgname", "headerName": "厂商", "width": 200},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 200},
        {"field": "qty", "headerName": "需求数量", "width": 100},
        {
            "field": "start_date",
            "headerName": "申请日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "mat_receiveddate",
            "headerName": "到料日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.mat_receiveddate)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pur", "headerName": "处理人", "width": 100},
        {
            "field": "pur_remark",
            "headerName": "备注",
            "width": 100,
            "tooltipField": "pur_remark",
        },
    ],
    "材料领用": [
        {"field": "deltapn", "headerName": "台达料号", "width": 150},
        {"field": "des", "headerName": "描述", "width": 200},
        {"field": "mfgname", "headerName": "厂商", "width": 150},
        {"field": "mfgpn", "headerName": "厂商料号", "width": 150},
        {"field": "qty", "headerName": "领用数量", "width": 100},
        {
            "field": "stockoutdate",
            "headerName": "领用日期",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.stockoutdate)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
    ],
    "工具领用": [
        {"field": "品名", "headerName": "品名", "width": 200},
        {"field": "描述", "headerName": "描述", "width": 200},
        {"field": "厂商", "headerName": "厂商", "width": 200},
        {"field": "类别", "headerName": "类别", "width": 100},
        {"field": "领用数量", "headerName": "领用数量", "width": 100},
        {
            "field": "领用时间",
            "headerName": "领用时间",
            "width": 100,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.领用时间)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
    ],
    "样制记录": [
        # {"field": "smstatus", "headerName": "状态", "width": 60},
        {"field": "id", "headerName": "id", "hide": True},
        {
            "field": "link",
            "headerName": "详细",
            "width": 90,
            "cellRenderer": "markdown",
            "linkTarget": "_blank",
            "editable": False,
        },
        {"field": "prtno", "headerName": "项目号", "width": 130},
        {"field": "project", "headerName": "项目名", "width": 150},
        {"field": "board", "headerName": "机种名", "width": 200},
        {"field": "pcbpn", "headerName": "PCB料号", "width": 100},
        {"field": "qty", "headerName": "需求数量", "width": 80},
        {
            "field": "smtstadate",
            "headerName": "开始日期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.smtstadate)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {
            "field": "fsdate_sch",
            "headerName": "首样交期",
            "width": 80,
            "valueGetter": {
                "function": "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.fsdate_sch)"
            },
            "valueFormatter": {"function": D3_DATE_FMT},
        },
        {"field": "pcbstatus", "headerName": "PCB状态", "width": 100},
        {
            "field": "bom",
            "headerName": "BOM状态",
            "width": 100,
            "editable": False,
            "cellRendererSelector": {"function": "bomDownload(params)"},
            # "cellRenderer": "DMC_Button",
            # "cellRendererParams": {
            #     "leftIcon": "material-symbols:download",
            #     "color": "green",
            #     "radius": "sm",
            # },
        },
    ],
}


def open_layout(user):
    nt_name = user.get("nt_name").lower()
    # *-----CE退件部分-----*
    sql = "select id,type,sub_type,deltapn,mfgname,mfgpn,status,ce_comment as remark,\
        ce as owner from ce.task where status=%s and applicant=%s"
    df1 = read_sql(sql, params=["reject", nt_name])
    if nt_name in admin:
        sql = (
            "select id,type,sub_type,deltapn,mfgname,mfgpn,status,ce_comment as remark,\
            ce as owner from ce.task where status=%s"
        )
        df1 = read_sql(sql, params=["reject"])

    df1 = df1.fillna("")
    df1["owner"] = df1["owner"].str.title()
    df1["status"] = "退件"
    c1 = df1["type"] == "失效分析"
    df1["subject"] = np.where(c1, df1["type"] + "+" + df1["deltapn"], "")

    df1["subject"] = np.where(
        c1,
        df1.apply(lambda x: f"**[{x['subject']}](/ce/fa/rd?tid={x['id']})**", axis=1),
        df1["subject"],
    )

    c1 = df1["type"] == "料号申请"
    df1["subject"] = np.where(
        c1,
        df1["type"] + "+" + df1["mfgname"] + "+" + df1["mfgpn"],
        df1["subject"],
    )
    df1["subject"] = np.where(
        c1,
        df1.apply(
            lambda x: f"**[{x['subject']}](/ce/pn/{sub_type.get(x['sub_type'])}/rd?tid={x['id']})**",
            axis=1,
        ),
        df1["subject"],
    )

    # *------规格待审核部分-----*
    sub_query = "(select task_id,ee,me,status from ssp_spec.modification \
                where id in (select max(id) from ssp_spec.modification group by task_id))"
    sql = f"select id,doc_type,model,spec as owner,remark \
        from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
            where b.status in %s and (b.ee=%s or b.me=%s)"
    df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"], nt_name, nt_name])

    if nt_name in admin:
        sql = f"select id,doc_type,model,spec as owner,remark \
            from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
                where b.status in %s"
        df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"]])

    df2 = df2.fillna("")
    df2["owner"] = df2["owner"].str.title()
    df2["status"] = "待审核"
    df2["subject"] = "规格发行" + "+" + df2["doc_type"] + "+" + df2["model"]
    df2["subject"] = df2.apply(
        lambda x: f"**[{x['subject']}](/spec-ee?page=solve2#{x['id']})**", axis=1
    )

    df = pd.concat([df1, df2])
    task_table = dag.AgGrid(
        id=id("task-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "subject",
                "headerName": "主旨",
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
            },
            {"field": "status", "headerName": "状态", "width": 50},
            {
                "field": "remark",
                "headerName": "备注",
                "width": 200,
                "tooltipField": "remark",
            },
            {"field": "owner", "headerName": "处理人", "width": 100},
        ],
        rowData=df.to_dict(orient="records"),
        columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "tooltipShowDelay": 0,
            "enableCellTextSelection": True,
        },
        style={"height": "85vh"},
    )
    return task_table


def create_ongoing_table(type, df):
    return dag.AgGrid(
        className="ag-theme-quartz",
        id={"type": id("ongoing-table"), "index": type},
        columnDefs=column_defs1.get(type),
        rowData=df.to_dict(orient="records"),
        columnSize="responsiveSizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
            # "floatingFilter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "enableCellTextSelection": True,
            "tooltipShowDelay": 0,
            # "domLayout": "autoHeight",
        },
        style={"max-height": "300px"},
        getRowId="params.data.id",
    )


def create_done_table(type, df):
    return dag.AgGrid(
        className="ag-theme-quartz",
        id={"type": id("done-table"), "index": type},
        columnDefs=column_defs2.get(type),
        rowData=df.to_dict(orient="records"),
        columnSize="responsiveSizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
            # "floatingFilter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "enableCellTextSelection": True,
            "tooltipShowDelay": 0,
        },
        style={"max-height": "300px", "min-height": "100px"},
        getRowId="params.data.id",
    )


def ongoing_layout():
    div = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("我的", value="我的", color="green"),
                    dmc.Tab("全部", value="全部", color="red"),
                ],
            ),
            dmc.Space(h=10),
            dmc.ScrollArea(dcc.Loading(id=id("ongoing-content")), h="80vh", w="88vw"),
        ],
        value="我的",
        id=id("ongoing-tabs"),
    )
    return div


def done_layout():
    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("我的", value="我的", color="green"),
                    dmc.Tab("全部", value="全部", color="red"),
                    dmc.Group(
                        fac.Space(
                            [
                                "记录时间段:",
                                fac.DatePicker(
                                    picker="month",
                                    format="YYYY-MM",
                                    disabledDatesStrategy=[
                                        {
                                            "mode": "gt",
                                            "target": "specific-date",
                                            "value": datetime.now().strftime(
                                                "%Y-%m-%d"
                                            ),
                                        },
                                    ],
                                    id=id("done-month"),
                                    value=datetime.now().strftime("%Y-%m"),
                                ),
                            ],
                        ),
                        ml="auto",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dmc.ScrollArea(dcc.Loading(id=id("done-content")), h="80vh", w="88vw"),
        ],
        color="red",
        value="我的",
        id=id("done-tabs"),
    )
    return layout


def new_layout():
    common = dmc.Group(
        [
            dmc.Anchor("领料申请", href="/material", target="_blank"),
            dmc.Anchor("样制申请", href="/project", target="_blank"),
            dmc.Anchor("工具领用", href="/tools", target="_blank"),
            dmc.Anchor("资产管理", href="/asset", target="_blank"),
        ]
    )
    other = dmc.Group(
        [
            dmc.Anchor("失效分析", href="/ce/fa/rd", target="_blank"),
            dmc.Anchor("全新料号", href="/ce/pn/new/rd", target="_blank"),
            dmc.Anchor("工具领用", href="/tools", target="_blank"),
            dmc.Anchor("资产管理", href="/asset", target="_blank"),
        ]
    )
    layout = dmc.Group(
        [
            dmc.Anchor("规格发行", href="/spec-ee?page=apply", target="_blank"),
            dmc.Anchor("失效分析", href="/ce/fa/rd", target="_blank"),
            dmc.Anchor("料号申请", href="/ce/pn", target="_blank"),
            dmc.Anchor("库存领料", href="/material", target="_blank"),
            dmc.Anchor("申请买料", href="/material", target="_blank"),
            dmc.Anchor("工具领用", href="/tools", target="_blank"),
        ]
    )
    layout = dmc.Stack(
        [
            fac.Descriptions(
                [
                    fac.DescriptionItem(
                        html.A("材料查询", href="/material", target="_blank"),
                        label="材料查询",
                    ),
                    fac.DescriptionItem(
                        html.A("失效分析", href="/ce/fa/rd", target="_blank"),
                        label="失效分析",
                    ),
                    fac.DescriptionItem(
                        html.A("料号申请", href="/ce/pn", target="_blank"),
                        label="料号申请",
                    ),
                ],
                title="材料相关",
                bordered=True,
                layout="vertical",
                labelStyle={"fontWeight": "bold"},
            ),
            fac.Descriptions(
                [
                    fac.DescriptionItem(
                        html.A("资产管理", href="/material", target="_blank"),
                        label="资产管理",
                    ),
                    fac.DescriptionItem(
                        html.A("工具领用", href="/ce/fa/rd", target="_blank"),
                        label="工具领用",
                    ),
                ],
                title="设备相关",
                bordered=True,
                layout="vertical",
                labelStyle={"fontWeight": "bold"},
            ),
            fac.Descriptions(
                [
                    fac.DescriptionItem(
                        html.A("规格发行", href="/material", target="_blank"),
                        label="规格发行",
                    ),
                ],
                title="规格相关",
                bordered=True,
                layout="vertical",
                labelStyle={"fontWeight": "bold"},
            ),
            fac.Descriptions(
                [
                    fac.DescriptionItem(
                        html.A("样制申请", href="/material", target="_blank"),
                        label="样制申请",
                    ),
                    fac.DescriptionItem(
                        html.A("样制查询", href="/material", target="_blank"),
                        label="样制查询",
                    ),
                ],
                title="样制相关",
                bordered=True,
                layout="vertical",
                labelStyle={"fontWeight": "bold"},
            ),
        ]
    )
    return layout


styles = {
    "root": {
        "display": "flex",
        "flexDirection": "row",
        "alignItems": "center",
    },
    "label": {
        "width": 100,
        "fontSize": 13,
    },
    "input": {
        "width": 180,
    },
}


def search_layout():
    div = html.Div("111")
    return div


sidebar_items = [
    {
        "key": "0",
        "title": "个人中心",
        "label": "个人中心",
        "icon": "material-symbols:home",
        "href": "/info/rd",
        "page": "home",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
    },
    {
        "key": "1",
        "title": "待确认",
        "label": "待确认",
        "icon": "material-symbols:hourglass-disabled",
        "href": "/info/rd?page=open",
        "page": "open",
        "status": "open",
        "count": 2,
    },
    {
        "key": "2",
        "title": "在途",
        "label": "在途",
        "icon": "material-symbols:hourglass-top",
        "href": "/info/rd?page=ongoing",
        "page": "ongoing",
        "status": "ongoing",
        "count": 0,
    },
    {
        "key": "3",
        "title": "已完成",
        "label": "已完成",
        "icon": "material-symbols:hourglass-bottom",
        "href": "/info/rd?page=closed",
        "page": "closed",
        "status": "closed",
        "count": 0,
    },
]


def info_sidebar(page):
    active_state = {
        "task": False,
        "ongoing": False,
        "done": False,
        "new": False,
        "search": False,
    }
    active_state[page] = True
    sidebar = dmc.Navbar(
        dmc.Stack(
            [
                dmc.NavLink(
                    label="个人中心",
                    href="/info/rd",
                    icon=DashIconify(icon="material-symbols:home", height=16),
                    target="_self",
                    color="red",
                    refresh=False,
                    style={
                        "font-weight": "bolder",
                        "color": "rgb(0, 159, 232)",
                    },
                ),
                dmc.Divider(),
                dmc.NavLink(
                    label="待确认",
                    href="/info/rd?page=task",
                    icon=DashIconify(
                        icon="material-symbols:hourglass-disabled", height=16
                    ),
                    target="_self",
                    color="red",
                    refresh=False,
                    active=active_state.get("task"),
                    id=id("task"),
                ),
                dmc.NavLink(
                    label="在途",
                    href="/info/rd?page=ongoing",
                    icon=DashIconify(icon="material-symbols:hourglass-top", height=16),
                    target="_self",
                    color="red",
                    refresh=False,
                    active=active_state.get("ongoing"),
                ),
                dmc.NavLink(
                    label="已完成",
                    href="/info/rd?page=done",
                    icon=DashIconify(
                        icon="material-symbols:hourglass-bottom", height=16
                    ),
                    target="_self",
                    color="red",
                    refresh=False,
                    active=active_state.get("done"),
                ),
                # dmc.Divider(),
                # dmc.NavLink(
                #     label="新需求",
                #     href="/info/rd?page=new",
                #     icon=DashIconify(icon="material-symbols:new-window", height=16),
                #     target="_self",
                #     color="red",
                #     refresh=False,
                #     active=active_state.get("new"),
                # ),
                # dmc.NavLink(
                #     label="查询",
                #     href="/info/rd?page=search",
                #     icon=DashIconify(icon="material-symbols:search-rounded", height=16),
                #     color="red",
                #     active=active_state.get("search"),
                # ),
            ],
            spacing=5,
        ),
        py=80,
        width={"base": 120},
        style={"backgroundColor": "rgba(240,240,240,1)"},
    )
    return sidebar


# @dc.memoize_stampede(cache, expire=3600)
def home_page(user):
    return
    nt_name = user.get("nt_name")
    # *-----CE退件部分-----*
    sql = "select id from ce.task where status=%s and applicant=%s"
    df1 = read_sql(sql, params=["reject", nt_name])
    if nt_name in admin:
        sql = "select id from ce.task where status=%s"
        df1 = read_sql(sql, params=["reject"])

    # *------规格待审核部分-----*
    sub_query = "(select task_id,ee,me,status from ssp_spec.modification \
                where id in (select max(id) from ssp_spec.modification group by task_id))"
    sql = f"select id from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
            where b.status in %s and (b.ee=%s or b.me=%s)"
    df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"], nt_name, nt_name])

    if nt_name in admin:
        sql = (
            f"select id from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
                where b.status in %s"
        )
        df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"]])
    count1 = df1.shape[0] + df2.shape[0]

    fa, pn = ce_ongoing("我的", user)
    spec = spec_ongoing("我的", user)
    project, debug = pur_ongoing("我的", user)
    prt1, prt2 = prt_ongoing("我的", user)
    count2 = sum(
        [
            fa.shape[0],
            pn.shape[0],
            spec.shape[0],
            project.shape[0],
            debug.shape[0],
            prt1.shape[0],
            prt2.shape[0],
        ]
    )


@dc.memoize_stampede(cache, expire=3600)
def sidebar_count(user):
    nt_name = user.get("nt_name")
    # *-----CE退件部分-----*
    sql = "select id from ce.task where status=%s and applicant=%s"
    df1 = read_sql(sql, params=["reject", nt_name])
    if nt_name in admin:
        sql = "select id from ce.task where status=%s"
        df1 = read_sql(sql, params=["reject"])

    # *------规格待审核部分-----*
    sub_query = "(select task_id,ee,me,status from ssp_spec.modification \
                where id in (select max(id) from ssp_spec.modification group by task_id))"
    sql = f"select id from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
            where b.status in %s and (b.ee=%s or b.me=%s)"
    df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"], nt_name, nt_name])

    if nt_name in admin:
        sql = (
            f"select id from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
                where b.status in %s"
        )
        df2 = read_sql(sql, params=[["submitted_ee", "submitted_me"]])
    count1 = df1.shape[0] + df2.shape[0]

    fa, pn = ce_ongoing("我的", user)
    spec = spec_ongoing("我的", user)
    project, debug = pur_ongoing("我的", user)
    prt1, prt2 = prt_ongoing("我的", user)
    count2 = sum(
        [
            fa.shape[0],
            pn.shape[0],
            spec.shape[0],
            project.shape[0],
            debug.shape[0],
            prt1.shape[0],
            prt2.shape[0],
        ]
    )

    return count1, count2


def layout(user, page="", **kwargs):
    if not page:
        content = home_page(user)
    elif page == "open":
        content = open_layout(user)
    elif page == "ongoing":
        content = ongoing_layout()
    elif page == "closed":
        content = done_layout()
    elif page == "new":
        content = new_layout()
    elif page == "search":
        content = search_layout()
    else:
        content = fac.Empty()

    bg_access_record(user, "个人中心", "访问", page)
    c1, c2 = sidebar_count(user)
    sidebar_items[1]["count"] = c1
    sidebar_items[2]["count"] = c2
    sidebar = create_sidebar(page, sidebar_items)

    appshell = dmc.AppShell(content, navbar=sidebar, style={"position": "fixed"})
    return appshell


def pur_ongoing(tabs, user):
    nt_name = user.get("nt_name")
    dept_id = user.get("dept_id")
    if tabs == "我的":
        sql = "select application,rd,proj,prtno,deltapn,mfgname,mfgpn,qty,\
            des,start_date,es_date,pur,pur_remark \
            from ssp.pur where rd=%s and pur_status not in %s"
        params = [nt_name, ("cancel", "closed")]
    else:
        sql = "select application,rd,proj,prtno,deltapn,mfgname,mfgpn,qty,\
            des,start_date,es_date,pur,pur_remark \
            from ssp.pur where dept_id=%s and pur_status not in %s"
        params = [dept_id, ("cancel", "closed")]
    df = read_sql(sql, params=params)

    df1 = df.loc[df["application"].str.lower() == "project"]
    df2 = df.loc[df["application"].str.lower() == "debug"]
    return df1, df2


def prt_ongoing(tabs, user):
    if tabs != "我的":
        return pd.DataFrame(), pd.DataFrame()
    nt_name = user.get("nt_name").lower()

    if nt_name in admin:
        sql = "select (select project from ssp.project where id=project_id) as project,\
            id,board,prtno,pcbpn,startdate_sch,qty,pcbstatus,smstatus,\
            fsdate_sch,b_ee,b_me,b_mag from ssp.prt \
            where smstatus not in %s order by id desc limit 500"
        params = [["cancel", "close"]]
        df = read_sql(sql, params=params)

        sql = "select * from ssp.prt_temp where pm=%s or ee=%s or me=%s"
        df1 = read_sql(sql, params=[nt_name, nt_name, nt_name])
        df1.columns = df1.columns.str.lower()
        df1["project"] = df1["proj"]
        df1["smstatus"] = "planning"
        df1 = df1.reindex(columns=df.columns)
        df = pd.concat([df, df1])
    else:
        sql = "select (select project from ssp.project where id=project_id) as project,\
            id,board,prtno,pcbpn,startdate_sch,qty,pcbstatus,dept,smstatus,\
            fsdate_sch, b_ee,b_me,b_mag from ssp.prt \
            where ((pm=%s) or (ee=%s) or (me=%s) or (mag=%s)) \
            and smstatus not in %s \
            order by id desc"
        params = [nt_name, nt_name, nt_name, nt_name, ["cancel", "close"]]
        df = read_sql(sql, params=params)

        sql = "select * from ssp.prt_temp where pm=%s or ee=%s or me=%s or mag=%s"
        df1 = read_sql(sql, params=[nt_name, nt_name, nt_name, nt_name])
        df1.columns = df1.columns.str.lower()
        df1["project"] = df1["proj"]
        df1["smstatus"] = "planning"
        df1 = df1.reindex(columns=df.columns)
        df = pd.concat([df, df1])

    df["smstatus"] = df.smstatus.str.lower()
    df["smstatus"] = np.where(df["smstatus"] == "planning", "待排定", "样制中")
    # df["prtno"] = df["prtno"].apply(
    #     lambda x: f"[{x}](/center?type=page-2-1s&prtno={x})"
    # )
    df["link"] = df["prtno"]
    df["link"] = df["link"].apply(
        lambda x: f"[点击查看](/center?type=page-2-1s&prtno={x})"
    )
    c1 = (df[["b_ee", "b_me", "b_mag"]].isin(["X", "NA"])).all(axis=1)
    df["bom"] = np.where(c1, "已完成", "未完成")
    df1 = df.loc[df["smstatus"] == "待排定"]
    df1["startdate_sch"] = None
    df2 = df.loc[df["smstatus"] == "样制中"]
    return df1, df2


def ce_ongoing(tabs, user):
    nt_name = user.get("nt_name")
    if tabs == "我的":
        sql = "select * from ce.task where type in %s and status in %s and applicant=%s"
        params = [("失效分析", "料号申请"), ("open", "ongoing", "approve"), nt_name]
    else:
        sql = "select * from ce.task where type in %s and status in %s and cc like %s"
        params = [
            ("失效分析", "料号申请"),
            ("open", "ongoing", "approve"),
            f"%{nt_name}%",
        ]
        if nt_name in admin:
            sql = "select * from ce.task where type in %s and status in %s"
            params = [("失效分析", "料号申请"), ("open", "ongoing", "approve")]

    df = read_sql(sql, params=params)
    df["status"] = np.where(df["status"] == "open", "未受理", "处理中")
    fa = df.loc[df["type"] == "失效分析"]
    pn = df.loc[df["type"] == "料号申请"]
    return fa, pn


def spec_ongoing(tabs, user):
    nt_name = user.get("nt_name")
    dept_id = user.get("dept_id")
    # *只显示ssp申请的任务,状态转成中文，处理人改成当前处理人
    sub_query = "(select task_id,current,status from ssp_spec.modification \
                where id in (select max(id) from ssp_spec.modification group by task_id))"

    if tabs == "我的":
        sql = f"select b.status,b.current,a.owner,a.doc_type,a.model,a.input_date \
            from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
            where owner=%s and b.status not in %s and new_source is null"
        params = [nt_name, ("cancel", "close")]
    else:
        sql = f"select b.status,b.current,a.owner,a.doc_type,a.model,a.input_date \
            from ssp_spec.task a left join {sub_query} b on a.id=b.task_id \
            where dept_id=%s and b.status not in %s and new_source is null"
        params = [dept_id, ("cancel", "close")]
    df = read_sql(sql, params=params)
    df["status"] = df["status"].apply(lambda x: status_dict.get(x))
    return df


# *----------callbacks----------*
@callback(
    Output(id("ongoing-content"), "children"),
    Input(id("ongoing-tabs"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def ongoing_tabs_active(tabs, user):
    child = []
    fa, pn = ce_ongoing(tabs, user)
    if not fa.empty:
        fa["id"] = fa["id"].apply(lambda x: f"**[点击查看](/ce/fa/ce?task={x})**")
        child.append(ongoing_accordion_item("失效分析", fa))

    if not pn.empty:
        child.append(ongoing_accordion_item("料号申请", pn))

    spec = spec_ongoing(tabs, user)
    if not spec.empty:
        child.append(ongoing_accordion_item("规格发行", spec))

    project, debug = pur_ongoing(tabs, user)
    if not project.empty:
        child.append(ongoing_accordion_item("机种缺料", project))

    if not debug.empty:
        child.append(ongoing_accordion_item("测试用料", debug))

    prt1, prt2 = prt_ongoing(tabs, user)
    if not prt1.empty:
        child.append(ongoing_accordion_item("样制-待排定", prt1))

    if not prt2.empty:
        child.append(ongoing_accordion_item("样制-生产中", prt2))

    if not child:
        return fac.Empty()

    # accordion = dmc.Accordion(
    #     chevron=DashIconify(icon="ant-design:plus-outlined"),
    #     chevronPosition="left",
    #     children=child,
    #     variant="contained",
    # )
    accordion = fac.Accordion(items=child, collapsible="header", key=tabs)
    return accordion


def ongoing_accordion_item(category, df):
    return {
        "title": fac.Badge(
            category, count=df.shape[0], offset=[20, 6], overflowCount=9999
        ),
        "key": category,
        "children": create_ongoing_table(category, df),
        "extra": [
            fac.Button(
                "导出",
                size="small",
                type="link",
                id={"type": id("export"), "index": category},
            ),
            dcc.Download(id={"type": id("download"), "index": category}),
        ],
        "forceRender": True,
        # "showArrow": False,
    }
    return dmc.AccordionItem(
        [
            dmc.AccordionControl(
                dmc.Group(
                    dmc.Indicator(
                        category,
                        position="middle-end",
                        label=df.shape[0],
                        size=16,
                        offset=-20,
                        overflowCount=9999,
                    )
                ),
                style={"background-color": "aliceblue", "height": "30px"},  ##f5f5f5
            ),
            dmc.AccordionPanel(create_ongoing_table(category, df)),
        ],
        value=category,
    )


def done_accordion_item(category, df):
    return {
        "title": fac.Badge(
            category, count=df.shape[0], offset=[20, 6], overflowCount=9999
        ),
        "key": category,
        "children": create_done_table(category, df),
        # "extra": [
        #     fac.Button(
        #         "导出",
        #         size="small",
        #         type="link",
        #         id={"type": id("export"), "index": category},
        #     ),
        #     dcc.Download(id={"type": id("download"), "index": category}),
        # ],
        "forceRender": True,
        # "style": {
        #     "padding": "-5px",
        # },
    }
    return dmc.AccordionItem(
        [
            dmc.AccordionControl(
                dmc.Group(
                    dmc.Indicator(
                        category,
                        position="middle-end",
                        label=df.shape[0],
                        size=16,
                        offset=-20,
                        overflowCount=9999,
                    )
                ),
                style={"background-color": "aliceblue", "height": "30px"},
            ),
            dmc.AccordionPanel(create_done_table(category, df)),
        ],
        value=category,
    )


@dc.memoize_stampede(cache, expire=3600)
def pur_done(tabs, start, end, user) -> tuple[pd.DataFrame, pd.DataFrame]:
    nt_name = user.get("nt_name")
    dept_id = user.get("dept_id")
    if tabs == "我的":
        sql = "select rd,application,proj,prtno,deltapn,mfgname,mfgpn,qty,\
            start_date,es_date,pur,pur_remark,mat_receiveddate \
            from ssp.pur where rd=%s and pur!=%s and start_date between %s and %s \
            and pur_status=%s"
        params = [nt_name, "bo.sm.wang", start, end, "closed"]
    else:
        sql = "select rd,application,proj,prtno,deltapn,mfgname,mfgpn,qty,\
            start_date,es_date,pur,pur_remark,mat_receiveddate \
            from ssp.pur where dept_id=%s and pur!=%s and start_date between %s and %s \
            and pur_status=%s"
        params = [dept_id, "bo.sm.wang", start, end, "closed"]
    df = read_sql(sql, params=params)

    df1 = df.loc[df["application"].str.lower() == "project"]
    df2 = df.loc[df["application"].str.lower() == "debug"]
    return df1, df2


@dc.memoize_stampede(cache, expire=3600)
def picking_done(tabs, start, end, user):
    if tabs != "我的":
        return pd.DataFrame()

    nt_name = user.get("nt_name").lower()
    sql = "SELECT distinct s0.dept,s0.deltapn,s1.des,s1.mfgname,s1.mfgpn,\
         s0.qty,s0.stockoutdate FROM ssp.stockout s0 left join ssp_csg.mat_info s1\
         ON s0.deltapn=s1.deltapn \
         WHERE s0.owner1=%s AND s0.type=%s \
         AND s0.stockoutdate between %s and %s"
    params = [nt_name, "debug", start, end]

    if nt_name in admin:
        sql = "SELECT distinct s0.dept,s0.deltapn,s1.des,s1.mfgname,s1.mfgpn,\
            s0.qty,s0.stockoutdate FROM ssp.stockout s0 left join ssp_csg.mat_info s1\
            ON s0.deltapn=s1.deltapn \
            WHERE s0.type=%s AND s0.stockoutdate between %s and %s limit 100"
        params = ["debug", start, end]

    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()
    df = df.drop_duplicates(["deltapn", "stockoutdate"])
    return df


@dc.memoize_stampede(cache, expire=3600)
def prt_done(tabs, start, end, user):
    if tabs != "我的":
        return pd.DataFrame()
    nt_name = user.get("nt_name").lower()
    if nt_name in admin:
        sql = "select (select project from ssp.project where id=project_id) as project,\
            id,board,prtno,pcbpn,smtstadate,qty,pcbstatus,smstatus,\
            fsdate_sch,b_ee,b_me,b_mag from ssp.prt \
            where smstatus =%s and FSDate_Act between %s and %s \
            order by id desc limit 500"
        params = ["close", start, end]
    else:
        # sql = "select (select project from ssp.project where id=project_id) as project,\
        #     id,board,prtno,pcbpn,smtstadate,qty,pcbstatus,dept,smstatus,\
        #     fsdate_sch,b_ee,b_me,b_mag from ssp.prt \
        #     where ((pm=%s) or (ee=%s) or (me=%s)) \
        #     and smstatus=%s and FSDate_Act between %s and %s \
        #     order by id desc"
        # params = [nt_name, nt_name, nt_name, "close", start, end]
        sql = "select (select project from ssp.project where id=project_id) as project,\
            id,board,prtno,pcbpn,smtstadate,qty,pcbstatus,dept,smstatus,\
            fsdate_sch,b_ee,b_me,b_mag from ssp.prt \
            where ((pm=%s) or (ee=%s) or (me=%s)) \
            and smstatus=%s order by id desc"
        params = [nt_name, nt_name, nt_name, "close"]
    df = read_sql(sql, params=params)
    # df["prtno"] = df["prtno"].apply(
    #     lambda x: f"[{x}](/center?type=page-2-1s&prtno={x})"
    # )
    df["link"] = df["prtno"]
    df["link"] = df["link"].apply(
        lambda x: f"[点击查看](/center?type=page-2-1s&prtno={x})"
    )
    c1 = (df[["b_ee", "b_me", "b_mag"]] == "X").any(axis=1)
    df["bom"] = np.where(c1, "已完成", "未完成")
    return df


@dc.memoize_stampede(cache, expire=3600)
def tool_done(tabs, start, end, user):
    if tabs != "我的":
        return pd.DataFrame()

    nt_name = user.get("nt_name").lower()
    sql = "SELECT t1.料号ID,t0.料号,t0.品名,t0.描述,t0.厂商,t0.类别,t0.区域,\
        t0.部门专用,t1.领用数量,t1.领用时间 FROM ssp_asset.tool_list t0 \
        LEFT JOIN ssp_asset.tool_stockout t1 ON t0.id=t1.料号ID \
        WHERE t1.领用人=%s and t1.领用时间 between %s and %s"
    params = [user.get("nt_name"), start, end]

    if nt_name in admin:
        sql = "SELECT t1.料号ID,t0.料号,t0.品名,t0.描述,t0.厂商,t0.类别,t0.区域,\
            t0.部门专用,t1.领用数量,t1.领用时间 FROM ssp_asset.tool_list t0 \
            LEFT JOIN ssp_asset.tool_stockout t1 ON t0.id=t1.料号ID \
            WHERE t1.领用时间 between %s and %s limit 100"
        params = [start, end]

    df = read_sql(sql, params=params)
    return df


@dc.memoize_stampede(cache, expire=3600)
def pn_done(tabs, start, end, user):
    nt_name = user.get("nt_name").lower()
    db_list = [
        "pn_new",
        "pn_temp",
        "pn_customer",
        "pn_spec",
        "pn_sw",
        "pn_update",
    ]
    dfc = []
    for db in db_list:
        if tabs == "我的":
            sql = (
                f"select b.*,a.mfgname,a.mfgpn,a.new_deltapn,b.sub_type from ce.{db} a join \
                (select id,type,sub_type,start_date,end_date,ce,ce_comment from ce.task \
                where type=%s and status=%s and start_date between %s and %s and applicant=%s) b \
                    on a.task_id=b.id"
            )
            params = ["料号申请", "close", start, end, nt_name]
        else:
            sql = (
                f"select b.*,a.mfgname,a.mfgpn,a.new_deltapn,b.sub_type from ce.{db} a join \
                (select id,type,sub_type,start_date,end_date,ce,ce_comment from ce.task \
                where type=%s and status=%s and start_date between %s and %s and cc like %s) b \
                    on a.task_id=b.id"
            )
            params = ["料号申请", "close", start, end, f"%{nt_name}%"]

            # *------管理员权限-----*
            if nt_name in admin:
                sql = (
                    f"select b.*,a.mfgname,a.mfgpn,a.new_deltapn,b.sub_type from ce.{db} a join \
                    (select id,type,sub_type,start_date,end_date,ce,ce_comment from ce.task \
                    where type=%s and status=%s and start_date between %s and %s)b \
                        on a.task_id=b.id limit 100"
                )
                params = ["料号申请", "close", start, end]

        dfi = read_sql(sql, params=params)
        if not dfi.empty:
            dfc.append(dfi)
    if dfc:
        df = pd.concat(dfc)
    else:
        df = pd.DataFrame()
    return df


@dc.memoize_stampede(cache, expire=3600)
def fa_done(tabs, start, end, user) -> pd.DataFrame:
    nt_name = user.get("nt_name")
    if tabs == "我的":
        sql = "select * from ce.task a left join (select task_id,conclusion from ce.fa)b \
            on a.id=b.task_id \
            where type=%s and status=%s and start_date between %s and %s and applicant=%s"
        params = ["失效分析", "close", start, end, nt_name]
    else:
        sql = "select * from ce.task a left join \
            (select task_id,conclusion from ce.fa)b on a.id=b.task_id \
            where type=%s and status=%s and start_date between %s and %s and cc like %s"

        params = ["失效分析", "close", start, end, f"%{nt_name}%"]

        # *------管理员权限-----*
        if nt_name in admin:
            sql = "select * from ce.task a left join \
                (select task_id,conclusion from ce.fa)b \
                on a.id=b.task_id \
                where type=%s and status=%s and start_date between %s and %s"
            params = ["失效分析", "close", start, end]

    df = read_sql(sql, params=params)
    df["id"] = df["id"].apply(lambda x: f"**[点击查看](/ce/fa/ce?task={x})**")
    return df


@dc.memoize_stampede(cache, expire=3600)
def spec_done(tabs, start, end, user):
    nt_name = user.get("nt_name")
    dept_id = user.get("dept_id")
    if tabs == "我的":
        sql = "select a.owner,a.doc_type,a.model,a.input_date,a.gmt_update,\
            a.customer,a.spec,b.attachment from ssp_spec.task a left join \
            (SELECT task_id,attachment,status from ssp_spec.modification \
            where status=%s) b on a.id=b.task_id \
            where owner=%s and b.status=%s and input_date between %s and %s"
        params = ["close", nt_name, "close", start, end]
    else:
        sql = "select a.owner,a.doc_type,a.model,a.input_date,a.gmt_update,\
            a.customer,a.spec,b.attachment from ssp_spec.task a left join \
            (SELECT task_id,attachment,status from ssp_spec.modification \
            where status=%s) b on a.id=b.task_id \
            where a.dept_id=%s and b.status =%s \
            and a.input_date between %s and %s"
        params = ["close", dept_id, "close", start, end]
    df = read_sql(sql, params=params)
    df["attachment"] = df["attachment"].str.replace(
        "//10.146.136.68/SSP/3. Document backup", "http://sup.deltaww.com", regex=False
    )
    df["attachment"] = df["attachment"].str.replace(" ", "%20")
    df["attachment"] = df["attachment"].apply(lambda x: f"**[点击下载]({x})**")

    return df


@callback(
    Output(id("done-content"), "children"),
    Input(id("done-tabs"), "value"),
    Input(id("done-month"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def done_tabs_active(tabs, month, user):
    if not (tabs and month):
        return fac.Empty()

    start = pd.Timestamp(month)
    end = start + pd.offsets.MonthBegin() - pd.offsets.Second()

    child = []
    fa = fa_done(tabs, start, end, user)
    if not fa.empty:
        child.append(done_accordion_item("失效分析", fa))

    pn = pn_done(tabs, start, end, user)
    if not pn.empty:
        child.append(done_accordion_item("料号申请", pn))

    spec = spec_done(tabs, start, end, user)
    if not spec.empty:
        child.append(done_accordion_item("规格发行", spec))

    project, debug = pur_done(tabs, start, end, user)
    # if not project.empty:
    #     child.append(done_accordion_item("机种缺料", project))

    if not debug.empty:
        child.append(done_accordion_item("测试用料", debug))

    picking = picking_done(tabs, start, end, user)
    if not picking.empty:
        child.append(done_accordion_item("材料领用", picking))

    tool = tool_done(tabs, start, end, user)
    if not tool.empty:
        child.append(done_accordion_item("工具领用", tool))

    prt = prt_done(tabs, start, end, user)
    if not prt.empty:
        child.append(done_accordion_item("样制记录", prt))

    if not child:
        return fac.Empty()

    # accordion = dmc.Accordion(
    #     chevron=DashIconify(icon="ant-design:plus-outlined"),
    #     chevronPosition="left",
    #     children=child,
    # )
    accordion = fac.Accordion(items=child, collapsible="header")
    return accordion


# @callback(
#     Output(id("graph"), "figure"),
#     Input(id("date-picker"), "value"),
# )
# def update_date_picker(value):
#     sql = "select * from sm_work_record where status in %s and date(date)=%s"
#     params = [["checkin", "checkout"], value]
#     df = read_sql(sql, params=params)
#     if df.empty:
#         return html.Div("暂无数据")
#     df["user"] = df["user"].str.title()
#     df["day"] = df["date"].dt.date
#     df = df.pivot_table(
#         index=["user", "prtno", "day"],
#         columns="status",
#         values="date",
#         aggfunc="first",
#     ).reset_index()

#     df["checkout"] = np.where(
#         df["checkout"].isna(),
#         df["checkin"].dt.normalize() + pd.Timedelta("17:30:00"),
#         df["checkout"],
#     )
#     fig = px.timeline(df, x_start="checkin", x_end="checkout", y="user", color="prtno")
#     return fig


@callback(
    Output(id("task-table"), "rowData"),
    Input(id("task-table"), "cellClicked"),
)
def click_table_delete_row(cell_clicked):
    """
    删除行
    """
    if cell_clicked.get("colId") != "subject":
        raise PreventUpdate
    table_data = Patch()
    row_idx = cell_clicked.get("rowIndex")
    del table_data[row_idx]
    return table_data


@callback(
    Output("download", "data"),
    Input({"type": id("ongoing-table"), "index": ALL}, "cellClicked"),
    Input({"type": id("done-table"), "index": ALL}, "cellClicked"),
    State("user", "data"),
)
def download_bom(data1, data2, user):
    if not any(data1) and not any(data2):
        raise PreventUpdate
    tid = ctx.triggered_id
    if "ongoing" in tid.get("type"):
        data = data1
    else:
        data = data2
    x = next(i for i in data if i)
    if x.get("colId") != "bom":
        raise PreventUpdate
    prt_id = x.get("rowId")

    sql = "select prtno,designno,deltapn,des,mfgname,mfgpn,packaging from ssp.smbom \
        where prt_id=%s"
    params = [prt_id]
    smbom = read_sql(sql, params=params)
    if smbom.empty:
        raise PreventUpdate
    filename = smbom["prtno"].iloc[0]
    sdf = dcc.send_data_frame(smbom.to_excel, f"{filename}.xlsx", index=False)
    bg_access_record(user, "个人中心", "下载BOM", filename)
    return sdf


@callback(
    Output({"type": id("download"), "index": MATCH}, "data"),
    Input({"type": id("export"), "index": MATCH}, "nClicks"),
    State({"type": id("ongoing-table"), "index": MATCH}, "rowData"),
    State({"type": id("ongoing-table"), "index": MATCH}, "columnDefs"),
    State("user", "data"),
)
def export_table(nclicks, data, columns, user):
    df = pd.DataFrame(data)
    columns = {i.get("field"): i.get("headerName") for i in columns}
    if "prtno" in columns:
        df["prtno"] = df["prtno"].str.extract(r"\[?(\w+)\]?")
    if "link" in columns:
        columns.pop("link")
    df = df.reindex(columns=columns).rename(columns=columns)

    fn = ctx.triggered_id.get("index")

    bg_access_record(user, "个人中心", "导出", fn)
    return dcc.send_data_frame(df.to_excel, f"{fn}.xlsx", index=False)
