# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash_table, dcc

from common import id_factory, parse_search
from components import CeCancelAIO, CeRejectAIO
from components.notice import notice
from config import UPLOAD_FOLDER_ROOT
from datetime import datetime

# from dbtool import db
from utils import db

id = id_factory(__name__)

tab_add_row = dbc.Button(
    className="fa fa-plus",
    size="sm",
    color="light",
    id="tab_add_row",
)

columns = [
    {"name": "台达料号", "id": "deltapn", "presentation": "input"},
    {"name": "描述", "id": "des", "presentation": "input"},
    {"name": "厂商", "id": "mfgname", "presentation": "input"},
    {"name": "厂商型号", "id": "mfgpn", "presentation": "input"},
    {"name": "材料类型1", "id": "cat1", "presentation": "dropdown"},
    {"name": "材料类型2", "id": "cat2", "presentation": "dropdown"},
    {"name": "材料类型3", "id": "cat3", "presentation": "dropdown"},
    {"name": "变更内容", "id": "change_content", "presentation": "input"},
    # {"name": "最新的规格文件(附件)", "id": "attachment", "presentation": "input"},
    # {"name": "部门", "id": "dept", "presentation": "input"},
    # {"name": "产品代码", "id": "product_code", "presentation": "input"},
    # {"name": "项目名称", "id": "project_name", "presentation": "input"},
    # {"name": "申请人", "id": "applicant", "presentation": "input"},
    {"name": "厂商代码", "id": "mfg_code", "presentation": "input"},
    {"name": "新台达料号", "id": "new_deltapn", "presentation": "input"},
    {"name": "新描述", "id": "new_des", "presentation": "input"},
    {"name": "新厂商", "id": "new_mfgname", "presentation": "input"},
    {"name": "新厂商型号", "id": "new_mfgpn", "presentation": "input"},
    {"name": "CE备注", "id": "ce_remark", "presentation": "input"},
]


def task_information(task):
    dept = db.find_one("ssp.dept", {"id": task.get("dept_id")})
    settings = db.find_one("ce.settings", {"dept_id": task.get("dept_id")})
    return fac.AntdDescriptions(
        [
            fac.AntdDescriptionItem(task.get("dept"), label="部门"),
            fac.AntdDescriptionItem(task.get("applicant"), label="申请人"),
            fac.AntdDescriptionItem(task.get("type"), label="类型"),
            fac.AntdDescriptionItem(task.get("status"), label="状态"),
            fac.AntdDescriptionItem(dept.get("product_code"), label="产品代码"),
            fac.AntdDescriptionItem(settings.get("project_name"), label="项目名称"),
        ],
        labelStyle={"fontWeight": "bold"},
    )


def table(data):
    table = dash_table.DataTable(
        data=data,
        columns=columns,
        editable=True,
        row_deletable=True,
        is_focused=True,
        id=id("table"),
        style_cell={
            "whiteSpace": "normal",
            "height": "auto",
            "textAlign": "left",
            "font-family": "Helvetica",
            "font-size": "10px",
        },
        css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    )
    return table


title = dmc.Center(dmc.Text("规格书更新申请单", weight=700))
submit = dmc.Button("提交", id=id("submit"))


def get_attachment(pn):
    folder = pn[0].get("attachment")
    div = [
        dmc.Anchor(
            f"{p.name}",
            href=f"/upload/{folder}/{p.name}",
            target="_blank",
            variant="link",
        )
        for p in (UPLOAD_FOLDER_ROOT / folder).glob("*.*")
    ]
    return dmc.Group(div)


# download = dmc.Button(
#     "下载", variant="subtle", color="orange", size="xs", id=id("download-btn"), ml="auto"
# )
download = dmc.Group(
    [
        dmc.Button(
            "下载",
            variant="subtle",
            color="orange",
            size="xs",
            id=id("download-btn"),
        ),
        dcc.Download(id=id("download")),
    ],
    position="right",
)


def layout(**query):
    task_id = query.get("task")

    task = db.find_one("ce.task", {"id": task_id})
    pn = db.execute("select * from ce.pn_spec where task_id = %s", (task_id,))
    return dmc.Container(
        dmc.Stack(
            [
                download,
                title,
                dmc.Divider(),
                task_information(task),
                table(pn),
                get_attachment(pn),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit")),
                        CeRejectAIO(__name__),
                        CeCancelAIO(__name__),
                    ],
                    grow=True,
                ),
            ]
        )
    )


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State(id("table"), "data"),
    State("url", "search"),
)
def ce_submit(n_clicks, data, search):
    if not n_clicks:
        raise PreventUpdate

    for item in data:
        db.update("ce.pn_spec", item)

    url = parse_search(search)
    task_id = url.get("task")

    if all(item["new_deltapn"] for item in data):
        db.update(
            "ce.task", {"status": "close", "id": task_id, "end_date": datetime.now()}
        )
    else:
        db.update("ce.task", {"status": "ongoing", "id": task_id})

    return notice("提交成功"), True


@callback(
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State("url", "search"),
)
def download_fa_data(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    task_id = url.get("task")

    task = db.execute("select * from ce.task where id = %s", (task_id,))
    df0 = pd.DataFrame(task)
    df0 = df0.reindex(columns=["type", "dept", "applicant", "status"])

    fa = db.execute("select * from ce.pn_spec where task_id = %s", (task_id,))
    df = pd.DataFrame(fa)
    df = pd.concat([df0, df], axis=1)
    df = df.fillna("")
    for col in [
        "attachment",
    ]:
        df[col] = np.where(
            df[col] == "",
            df[col],
            UPLOAD_FOLDER_ROOT.as_uri() + "/" + df[col],
        )
    df = df.reindex(
        columns=df.columns.difference(["id", "task_id", "create_time", "update_time"])
    )
    col1 = db.execute("SHOW FULL COLUMNS FROM ce.task")
    col1 = {i.get("field"): i.get("comment") for i in col1}
    col2 = db.execute("SHOW FULL COLUMNS FROM ce.pn_spec")
    col2 = {i.get("field"): i.get("comment") for i in col2}
    col1.update(col2)
    df = df.rename(columns=col1)
    return dcc.send_data_frame(df.T.to_excel, f"FA_{task_id}.xlsx")
