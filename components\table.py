# -*- coding: utf-8 -*-
import dash_ag_grid as dag
import dash_mantine_components as dmc


class Grid(dag.AgGrid):
    def __init__(self, id=None, columnDefs=None, rowData=None, **kwargs):
        super().__init__(**kwargs)
        self.id = id
        self.className = "ag-theme-quartz"
        self.columnDefs = columnDefs
        self.rowData = rowData
        self.defaultColDef = {
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        }
        self.dashGridOptions = {
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            "suppressRowClickSelection": True,
            "animateRows": False,
        }
        self.getRowId = "params.data.id"
        self.style = kwargs.get("style", {"height": 400})


def ag_grid(id=None, columnDefs=None, rowData=[{}]):
    return dmc.Stack(
        [
            dmc.Group(dmc.Button("AddRow", size="xs", compact=True, color="lime")),
            dag.AgGrid(
                id=id,
                className="ag-theme-quartz",
                columnDefs=columnDefs,
                columnSize="responsiveSizeToFit",
                rowData=rowData,
                defaultColDef={
                    "resizable": True,
                    "sortable": True,
                    "filter": True,
                    "wrapHeaderText": True,
                    "autoHeaderHeight": True,
                },
                dashGridOptions={
                    "rowSelection": "single",
                    "stopEditingWhenCellsLoseFocus": True,
                    "singleClickEdit": True,
                    "rowHeight": 35,
                    "enableCellTextSelection": True,
                    "ensureDomOrder": True,
                    "suppressRowClickSelection": True,
                },
                getRowId="params.data.id",
                style={"height": 300},
            ),
        ],
        spacing=0,
    )
