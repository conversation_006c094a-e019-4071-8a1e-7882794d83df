# coding: utf-8
from sqlalchemy import (
    Column,
    DECIMAL,
    Date,
    DateTime,
    Float,
    ForeignKey,
    Index,
    String,
    Text,
    text,
    TIMESTAMP,
)
from sqlalchemy.dialects.mysql import INTEGER, VARCHAR
from sqlalchemy.orm import relationship
from config import db

Base = db.Model

# class AccessRecord(Base):
#     __tablename__ = "access_record"

#     id = Column(INTEGER(11), primary_key=True)
#     dept = Column(String(50))
#     owner = Column(String(50))
#     interface = Column(String(255), comment="界面")
#     action = Column(String(255), comment="操作")
#     date = Column(DateTime)
#     area = Column(String(50))
#     keywords = Column(String(255))


# class BomInitial(Base):
#     __tablename__ = "bom_initial"
#     __table_args__ = (
#         Index("check1", "Item_BOM", "DESIGNNo", "DeltaPN", "SourceStatus"),
#         Index("Download", "Item_BOM", "PrtNo"),
#         {"comment": "备料BOM原始数据库"},
#     )

#     ID = Column(INTEGER(11), primary_key=True)
#     Item_BOM = Column(String(45))
#     PrtNo = Column(String(255))
#     DeltaPN = Column(String(255))
#     DES = Column(String(255))
#     MFGName = Column(String(255))
#     MFGPN = Column(String(255))
#     DESIGNNo = Column(String(255))
#     Source = Column(String(45))
#     SourceStatus = Column(String(45))
#     BOMType = Column(String(45))
#     Assembly = Column(String(45))
#     Remark = Column(String(255))
#     DesignPart = Column(String(45))
#     First_Date = Column(DateTime)
#     Status = Column(String(45))
#     PCB_Remark = Column(String(255))
#     Owner = Column(String(45))
#     Packaging = Column(String(45))
#     CheckCode = Column(String(45))


# class BomRecord(Base):
#     __tablename__ = "bom_record"
#     __table_args__ = {"comment": "BOM数据统计"}

#     ID = Column(INTEGER(11), primary_key=True)
#     Item_BOM = Column(String(45))
#     PrtNo = Column(String(255))
#     BOMType = Column(String(45))
#     Update_Date = Column(DateTime)
#     Prepare_Date = Column(DateTime)
#     Status = Column(String(45))
#     Source = Column(String(45))
#     Owner1 = Column(String(45))
#     Owner2 = Column(String(45))
#     ProcessingMode = Column(String(45), nullable=False, server_default=text("''"))
#     test = Column(String(45), nullable=False, server_default=text("''"))


class Ce(Base):
    __tablename__ = "ce"

    id = Column(INTEGER(11), primary_key=True)
    item_pur = Column(String(255))
    application = Column(String(255))
    property = Column(String(255))
    dept = Column(String(255))
    prtno = Column(String(255))
    proj = Column(String(255))
    rd = Column(String(255))
    mat_catelogue = Column(String(255))
    mat_group = Column(String(255))
    mat_type = Column(String(255))
    plant = Column(String(255))
    ce = Column(String(255))
    deltapn = Column(String(255))
    des = Column(String(255))
    mfgname = Column(String(255))
    mfgpn = Column(String(255))
    qty = Column(INTEGER(11))
    start_date = Column(DateTime)
    req_date = Column(DateTime)
    pur_date = Column(DateTime)
    es_date_lasttime = Column(Date)
    es_date = Column(Date)
    price = Column(DECIMAL(10, 4))
    pur_remark = Column(String(255))
    owner_m = Column(String(255))
    status = Column(String(255))
    finish_date = Column(DateTime)
    ce_remark = Column(String(255))
    sub_deltapn = Column(String(255))
    sub_desc = Column(String(255))
    sub_mfgname = Column(String(255))
    sub_mfgpn = Column(String(255))
    mat_remark = Column(String(255))
    designno = Column(String(255))
    bomqpa = Column(String(45))
    assign_date = Column(DateTime)


class CeTempPn(Base):
    __tablename__ = "ce_temp_pn"

    id = Column(INTEGER(11), primary_key=True)
    temp_pn = Column(String(255), nullable=False, unique=True)
    deltapn = Column(String(255), unique=True)
    stockno = Column(String(255))
    des = Column(String(255))
    mfgname = Column(String(255))
    mfgpn = Column(String(255))
    source = Column(String(255))
    ce_remark = Column(String(255))
    date = Column(String(255), nullable=False, server_default=text("'D_1990/01/01'"))
    ce = Column(String(255))


class CeUpdate(Base):
    __tablename__ = "ce_update"

    id = Column(INTEGER(11), primary_key=True)
    item_pur = Column(String(255))
    up_type = Column(String(255))
    remark_1 = Column(String(255))
    remark_2 = Column(String(255))
    up_date = Column(DateTime)
    owner = Column(String(255))


# class CeWorkRecord(Base):
#     __tablename__ = "ce_work_record"

#     ID = Column(INTEGER(11), primary_key=True)
#     Type = Column(String(255))
#     ReqType = Column(String(255))
#     Status = Column(String(255))
#     Dept = Column(String(255))
#     Eng = Column(String(255))
#     ReqDate = Column(DateTime)
#     ProjName = Column(String(255))
#     ApplicationSituation = Column(String(255))
#     ApplicationType = Column(String(255))
#     Circuit = Column(String(255))
#     MatType = Column(String(255))
#     MatType1 = Column(String(255))
#     MatType2 = Column(String(255))
#     MatType3 = Column(String(255))
#     PN = Column(String(255))
#     DES = Column(String(255))
#     MFGNAME = Column(String(255))
#     MFGPN = Column(String(255))
#     RdReq = Column(String(1600))
#     GroupFA = Column(String(2048))
#     Result = Column(String(255))
#     CeRemark = Column(String(1600))
#     StartDate = Column(DateTime)
#     EndDate = Column(DateTime)
#     Owner = Column(String(255))
#     Qty = Column(INTEGER(11), server_default=text("'1'"))
#     OriginalSpec = Column(String(255))
#     Issue = Column(String(500))
#     Reason = Column(String(1024))
#     TagID = Column(String(255))


class Dept(Base):
    __tablename__ = "dept"

    id = Column(INTEGER(10), primary_key=True)
    dept = Column(String(50), nullable=False, unique=True)
    sm_qty_limit = Column(INTEGER(11), nullable=False)
    toolbox_permit = Column(
        VARCHAR(1),
        nullable=False,
        server_default=text("'N'"),
        comment="工具领用许可，默认N，不允许",
    )
    purchasing_permit = Column(
        VARCHAR(1),
        nullable=False,
        server_default=text("'N'"),
        comment="买料许可，默认N，不允许",
    )
    area = Column(String(2))
    dept_group = Column(String(255), nullable=False, server_default=text("''"))
    dept_name = Column(String(255), nullable=False, server_default=text("''"))
    product_code = Column(String(255), server_default=text("''"))
    system_block = Column(
        String(255), server_default=text("''"), comment="system_block"
    )
    std_material = Column(String(255))
    charge_code = Column(String(255), server_default=text("''"), comment="费用代码")
    often_type = Column(String(255))
    category = Column(String(255))


class SmWorkRecord(Base):
    __tablename__ = "sm_work_record"
    __table_args__ = {"comment": "样制工时"}

    id = Column(INTEGER(10), primary_key=True)
    prtno = Column(String(50), index=True)
    start = Column(DateTime, comment="开始时间")
    end = Column(DateTime, comment="结束时间")
    user = Column(String(50), index=True)


# class DipStdTime(Base):
#     __tablename__ = "dip_std_time"

#     id = Column(INTEGER(11), primary_key=True)
#     fixture = Column(INTEGER(11), server_default=text("'0'"), comment="是否用治具")
#     hand_welding = Column(INTEGER(11), server_default=text("'0'"), comment="是否手焊")
#     material_preparation = Column(
#         String(255), server_default=text("'15'"), comment="材料准备，固定15分钟"
#     )
#     rework = Column(String(255), server_default=text("'0'"), comment="重工")
#     pre_inspection = Column(
#         String(255), server_default=text("'0'"), comment="插件前目检,每秒3个贴片点位"
#     )
#     fixed_position = Column(
#         String(255), server_default=text("'0'"), comment="定位，每3分钟一种子板"
#     )
#     molding = Column(
#         String(255),
#         server_default=text("'0'"),
#         comment="插件加工，包胶带,套管一个元件1分钟，常规10个元件1分钟，一个散热器5分钟",
#     )
#     glue = Column(String(255), server_default=text("'0'"), comment="堵胶，每分钟3000立方毫米")
#     plugin = Column(String(255), server_default=text("'0'"), comment="插件，每分钟6个元件")
#     wave_solder = Column(String(255), server_default=text("'0'"), comment="波峰焊")
#     tear_glue = Column(String(255), server_default=text("'0'"), comment="撕胶，固定每块3分钟")
#     clip_pin = Column(String(255), server_default=text("'0'"), comment="剪脚，每分钟27个点")
#     repair = Column(String(255), server_default=text("'0'"), comment="维修，每分钟2个点")
#     clean_pcb = Column(String(255), server_default=text("'0'"), comment="洗板，固定每块3分钟")
#     split_pcb = Column(String(255), server_default=text("'0'"), comment="分板，固定每块5分钟")
#     post_inspection = Column(
#         String(255), server_default=text("'0'"), comment="终检，固定每块10分钟"
#     )


# class FrontEndVersion(Base):
#     __tablename__ = "front_end_version"

#     id = Column(INTEGER(10), primary_key=True, comment="自增id")
#     gmt_create = Column(DateTime, nullable=False, comment="创建时间")
#     gmt_modified = Column(DateTime, nullable=False, comment="修改时间")
#     front_end = Column(String(255), nullable=False, unique=True, comment="前端程式名")
#     version = Column(String(255), nullable=False, comment="版本号")
#     changelog = Column(String(255))


# class Inventory(Base):
#     __tablename__ = "inventory"

#     Id = Column(INTEGER(11), primary_key=True)
#     CheckCode = Column(String(255), nullable=False)
#     StockNo = Column(String(255))
#     DES = Column(String(255))
#     MFGNAME = Column(String(255))
#     MFGPN = Column(String(255))
#     Qty = Column(INTEGER(255), comment="盘点前数量")
#     Actual_Qty = Column(INTEGER(11), comment="盘点后实际数量")
#     Owner = Column(String(255), nullable=False, comment="盘点作业人员")
#     Date = Column(DateTime, nullable=False, comment="盘点日期")
#     Area = Column(String(255), nullable=False, comment="物料区域")
#     type = Column(String(255), comment="daily每日盘点customize定制")
#     wait_qty = Column(
#         INTEGER(10), nullable=False, server_default=text("'0'"), comment="待出库数量"
#     )
#     stock_qty = Column(
#         INTEGER(10), nullable=False, server_default=text("'0'"), comment="当前库存量"
#     )


# class Notice(Base):
#     __tablename__ = "notice"

#     id = Column(INTEGER(10), primary_key=True, comment="自增id")
#     gmt_create = Column(DateTime, nullable=False, comment="创建时间")
#     type = Column(String(255), nullable=False, comment="通知类型")
#     notice = Column(String(2048), nullable=False, comment="通知内容")
#     sender = Column(String(255), nullable=False, comment="发送方")
#     receiver = Column(
#         String(255), nullable=False, server_default=text("'ALL'"), comment="接收方"
#     )


# class NoticeStatu(Base):
#     __tablename__ = "notice_status"

#     id = Column(INTEGER(10), primary_key=True, comment="自增id")
#     gmt_create = Column(DateTime, nullable=False, comment="创建时间")
#     gmt_modified = Column(DateTime, nullable=False, comment="修改时间")
#     notice_id = Column(INTEGER(11), nullable=False, comment="通知内容")
#     owner = Column(
#         String(255),
#         nullable=False,
#         server_default=text("'0'"),
#         comment="是否已阅，1已阅读，0未阅读",
#     )
#     status = Column(String(255), nullable=False, comment="发送方")


# class Nudd(Base):
#     __tablename__ = "nudd"

#     id = Column(INTEGER(11), primary_key=True)
#     action = Column(String(255))
#     status = Column(String(255))
#     ce_owner = Column(String(255))
#     update_date = Column(DateTime)
#     delta_nudd_type = Column(String(255))
#     dept_nudd_type = Column(String(255))
#     DeltaPN = Column(String(255))
#     DES = Column(String(255))
#     MFGNAME = Column(String(255))
#     MFGPN = Column(String(255))
#     sample_date = Column(DateTime)
#     mp_date = Column(DateTime)
#     ce_remark = Column(String(255))
#     rd_require_date = Column(DateTime)
#     rd_name = Column(String(255))
#     module_name = Column(String(255))
#     customer = Column(String(255))
#     module_mp_date = Column(DateTime)
#     annual_consumption = Column(String(255))


# class Pcb(Base):
#     __tablename__ = "pcb"
#     __table_args__ = (Index("unique_idx", "model_name", "board_name", unique=True),)

#     id = Column(INTEGER(11), primary_key=True)
#     model_name = Column(String(255), nullable=False)
#     board_name = Column(String(255), nullable=False)
#     pn = Column(String(255))
#     description = Column(String(255))
#     size = Column(String(255))
#     layer = Column(String(255))
#     thickness = Column(String(255))
#     copper = Column(String(255))
#     layout = Column(String(255))
#     pm = Column(String(255))
#     ee = Column(String(255))
#     me = Column(String(255))
#     mag = Column(String(255))
#     gmt_create = Column(DateTime)
#     gmt_modified = Column(DateTime)


# class PcbStock(Base):
#     __tablename__ = "pcb_stock"

#     ID = Column(INTEGER(11), primary_key=True)
#     PWB = Column(String(255), unique=True)
#     StockNo = Column(String(255), unique=True)
#     DEPT = Column(String(255))
#     Proj = Column(String(255))
#     FirstStockInDate = Column(DateTime)
#     StockInDate = Column(DateTime)
#     Qty = Column(INTEGER(11))
#     Memo = Column(String(255))
#     Area = Column(String(45))


# class PcbStockInoutRecord(Base):
#     __tablename__ = "pcb_stock_inout_record"

#     ID = Column(INTEGER(11), primary_key=True)
#     Date = Column(DateTime)
#     PrtNo = Column(String(255))
#     DEPT = Column(String(255))
#     Proj = Column(String(255))
#     PWB = Column(String(255))
#     Qty = Column(INTEGER(11))
#     Owner = Column(String(255))
#     Memo = Column(String(255))


# class Printer(Base):
#     __tablename__ = "printer"

#     id = Column(INTEGER(10), primary_key=True)
#     area = Column(String(50), nullable=False, server_default=text("'SH'"), comment="区域")
#     type = Column(
#         String(50), nullable=False, server_default=text("'0'"), comment="标签类型"
#     )
#     printer = Column(
#         String(50), nullable=False, server_default=text("'0'"), comment="打印机名称"
#     )


# class Project(Base):
#     __tablename__ = "project"
#     __table_args__ = (Index("unique_project", "project", "owner", unique=True),)

#     id = Column(INTEGER(10), primary_key=True, comment="自增id")
#     gmt_create = Column(DateTime, nullable=False, comment="创建日期")
#     gmt_modified = Column(DateTime, nullable=False, comment="修改日期")
#     owner = Column(VARCHAR(255), nullable=False)
#     project = Column(VARCHAR(255), nullable=False, comment="项目名称")
#     stage = Column(VARCHAR(255))
#     type = Column(VARCHAR(255), server_default=text("'平台机种'"), comment="平台机种,衍生机种")
#     customer = Column(VARCHAR(255), server_default=text("'NA'"), comment="客户")
#     application = Column(VARCHAR(255), server_default=text("'NA'"), comment="应用")
#     power = Column(VARCHAR(255), server_default=text("'NA'"), comment="功率")
#     annually_forecast = Column(VARCHAR(255), server_default=text("'NA'"), comment="年产量")
#     kick_off_date = Column(Date, comment="立项日期")
#     sm_forecast = Column(JSON, comment="样制次数")
#     mp_date = Column(Date, comment="量产日期")
#     mp_plant = Column(VARCHAR(255), server_default=text("'NA'"), comment="量产地")
#     pm = Column(VARCHAR(255), nullable=False)
#     ee = Column(VARCHAR(255), nullable=False)
#     me = Column(VARCHAR(255), nullable=False)
#     layout = Column(VARCHAR(255), nullable=False)
#     board = Column(JSON)
#     mag = Column(VARCHAR(255))
#     dept_id = Column(INTEGER(11), nullable=False)


# class PrtTemp(Base):
#     __tablename__ = "prt_temp"

#     ID = Column(INTEGER(11), primary_key=True)
#     AppDate = Column(DateTime, nullable=False)
#     project_id = Column(INTEGER(11), comment="项目id")
#     stage = Column(INTEGER(11), comment="阶段")
#     board = Column(String(255), comment="板子名")
#     scheme = Column(String(255))
#     DEPT = Column(String(255), nullable=False)
#     PM = Column(String(255), nullable=False)
#     EE = Column(String(255), nullable=False)
#     ME = Column(String(255), nullable=False)
#     Mag = Column(String(255), nullable=False)
#     Layout = Column(String(255))
#     Proj = Column(String(255), nullable=False, comment="项目名+板子名+方案名")
#     pcbpn = Column(String(255))
#     PCBStatus = Column(String(255), nullable=False)
#     Qty = Column(INTEGER(11), nullable=False)
#     FSQty = Column(INTEGER(11), nullable=False)
#     FSDate_Req = Column(DateTime, nullable=False)
#     approved = Column(INTEGER(1), server_default=text("'1'"), comment="1表示批准，0表示不批准")
#     dept_id = Column(INTEGER(11))


class Pur(Base):
    __tablename__ = "pur"

    id = Column(INTEGER(11), primary_key=True, autoincrement=True, comment="id")
    item_pur = Column(String(255), unique=True, comment="采购号")
    application = Column(String(255), nullable=False, comment="用途")
    dept = Column(String(255), nullable=False, comment="部门")
    prtno = Column(String(255), comment="项目号")
    proj = Column(String(255), comment="机种名")
    rd = Column(String(255), comment="工程师")
    mat_catelogue = Column(String(255), comment="材料类别")
    mat_group = Column(String(255), comment="材料大类")
    mat_type = Column(String(255), comment="材料类型")
    plant = Column(String(255), comment="厂区")
    plant_qty = Column(String(45), comment="工厂库存")
    location = Column(String(45), comment="仓别")
    lot = Column(String(45), comment="架位")
    pur = Column(String(255), comment="采购人员")
    deltapn = Column(String(255), nullable=False, index=True, comment="台达料号")
    des = Column(String(255), comment="描述")
    mfgname = Column(String(255), comment="厂商")
    mfgpn = Column(String(255), comment="厂商料号")
    qty = Column(INTEGER(11), nullable=False, comment="需求数量")
    r_qty = Column(INTEGER(11), comment="采购数量")
    start_date = Column(DateTime, nullable=False, comment="材料发起日")
    pur_date = Column(DateTime, comment="采购申请日")
    req_date = Column(DateTime, nullable=False, comment="需求日")
    es_date_lasttime = Column(Date, comment="上一次预计到货日")
    es_date = Column(DateTime, comment="预计到货日")
    es_maint_date = Column(DateTime, comment="交期维护日期")
    price = Column(DECIMAL(10, 4), comment="单价")
    pur_remark = Column(String(255), comment="采购备注")
    inv_no = Column(String(255), comment="发票号")
    po_no = Column(String(255), comment="PO单号")
    pr_no = Column(String(255), comment="PR单号")
    pur_status = Column(
        String(255), nullable=False, server_default=text("'SA'"), comment="状态"
    )
    mat_receiveddate = Column(DateTime, comment="材料接收日期")
    owner_m = Column(String(255), comment="材料接收人")
    mat_remark = Column(String(255), comment="物料备注")
    received_qty = Column(INTEGER(11), comment="到货数量")
    dispatch_date = Column(DateTime, comment="派送日期")
    sub_deltapn = Column(String(255), comment="替代料号")
    sub_stockno = Column(String(255), comment="替代库位号")
    ce_remark = Column(String(255), comment="工程师备注")
    bomqpa = Column(String(45), comment="工程师备注")
    standardcomponent = Column(String(45), comment="standardcomponent")
    # pcb_date = Column(DateTime, comment="到板日期")  # 已删除，关联prt的pcbstatus
    qissue = Column(String(255), comment="品质问题")
    dest_area = Column(String(255), comment="材料归属地")
    checkcode = Column(String(255), nullable=False, index=True, comment="系列化料号")
    dept_id = Column(INTEGER(11), nullable=False, comment="部门ID")


class PurPlant(Base):
    __tablename__ = "pur_plant"

    id = Column(INTEGER(11), primary_key=True, nullable=False)
    item_pur = Column(INTEGER(11), nullable=False)
    application = Column(String(255))
    dept = Column(String(255))
    prtno = Column(String(255))
    proj = Column(String(255))
    rd = Column(String(255))
    mat_catelogue = Column(String(255))
    mat_group = Column(String(255))
    mat_type = Column(String(255))
    plant = Column(String(255))
    plant_qty = Column(String(45))
    location = Column(String(45))
    lot = Column(String(45))
    deltapn = Column(String(255))
    des = Column(String(255))
    mfgname = Column(String(255))
    mfgpn = Column(String(255))
    qty = Column(INTEGER(11))
    r_qty = Column(INTEGER(11))
    start_date = Column(DateTime)
    req_date = Column(DateTime)
    pur_date = Column(DateTime, comment="采购发起日期")
    es_date = Column(DateTime)
    es_date_lasttime = Column(DateTime)
    pur_remark = Column(String(255))
    pur_status = Column(String(255))
    mat_receiveddate = Column(DateTime)
    owner_m = Column(String(255))
    mat_remark = Column(String(255))
    received_qty = Column(INTEGER(11))
    checkcode = Column(String(45))
    pur_id = Column(INTEGER(11))
    dept_id = Column(INTEGER(11), nullable=False, comment="部门ID")


class PurUpdate(Base):
    __tablename__ = "pur_update"

    id = Column(INTEGER(11), primary_key=True)
    item_pur = Column(String(255), nullable=False)
    up_type = Column(String(255), comment="更新类别")
    remark1 = Column(String(255))
    remark2 = Column(String(255))
    up_date = Column(DateTime, comment="更新时间")
    owner = Column(String(255))


class SafetyStockAuto(Base):
    __tablename__ = "safetystock_auto"
    __table_args__ = (
        Index("unqiue", "Area", "limitUseDept", "Status", "Checkcode", unique=True),
    )

    id = Column("ID", INTEGER(11), primary_key=True)
    area = Column("Area", String(45))
    limitusedept = Column("limitUseDept", String(255))
    status = Column("Status", String(45))
    latestdate = Column("LatestDate", DateTime)
    checkcode = Column("Checkcode", String(45))
    deltapn = Column("DeltaPN", String(45))
    des = Column("DES", String(255))
    mfgname = Column("MFGNAME", String(255))
    mfgpn = Column("MFGPN", String(255))
    adddate = Column("AddDate", DateTime)
    canceldate = Column("CancelDate", DateTime)
    latestowner = Column("LatestOwner", String(255))
    addtype = Column("AddType", String(45))
    tbd1 = Column("TBD1", String(255))
    tbd2 = Column("TBD2", String(255))
    tbd3 = Column("TBD3", String(255))
    tbd4 = Column("TBD4", String(255))
    tbd5 = Column("TBD5", String(255))
    tbd6 = Column("TBD6", String(255))
    tbd7 = Column("TBD7", String(255))
    autocalculateqty = Column("AutoCalculateQty", INTEGER(11))
    autoorderqty = Column("AutoOrderQty", INTEGER(11))
    safetystock1 = Column(
        "SafetyStock1",
        INTEGER(11),
        server_default=text("'0'"),
        comment="采购手动触发量",
    )
    purqty = Column(
        "PurQty", INTEGER(11), server_default=text("'0'"), comment="采购手动安全库存量"
    )
    stockqty = Column("StockQty", INTEGER(11))
    yearusetime = Column("YearUseTime", INTEGER(11))
    halfyearstockout = Column("HalfYearStockout", INTEGER(11))
    averagepurlt = Column("AveragePurLT", String(45))
    annualpurqty = Column("AnnualPurQty", INTEGER(11))
    annualpurtime = Column("AnnualPurTime", INTEGER(11))
    shortagetime = Column("ShortageTime", INTEGER(11))
    dept = Column("Dept", String(255))
    memo = Column("Memo", String(455))


class Safetystock(Base):
    __tablename__ = "safetystock"

    id = Column(INTEGER(11), primary_key=True)
    area = Column(String(45))
    limituse = Column(String(255), comment="部门专用")
    status = Column(String(45), comment="tbd,Processing,cancel")
    checkcode = Column(String(45))
    deltapn = Column(String(45))
    des = Column(String(255))
    mfgname = Column(String(255))
    mfgpn = Column(String(255))
    adddate = Column(
        DateTime, comment="添加日期", server_default=text("CURRENT_TIMESTAMP")
    )
    canceldate = Column(DateTime, comment="取消日期")
    latestowner = Column(String(255), comment="采购")
    addtype = Column(String(45), comment="添加类型")
    custom_qty = Column(INTEGER(11), server_default=text("'0'"), comment="触发量")
    pur_qty = Column(INTEGER(11), server_default=text("'0'"), comment="采购量")
    memo = Column(String(455), comment="备注")
    gmt_create = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
    gmt_update = Column(TIMESTAMP)


# class Satisfaction(Base):
#     __tablename__ = "satisfaction"
#     __table_args__ = {"comment": "满意度调查"}

#     ID = Column(INTEGER(11), primary_key=True)
#     投诉人 = Column(String(255))
#     投诉人部门 = Column(String(255))
#     被投诉部门 = Column(String(45))
#     得分ID = Column(String(255))
#     交期 = Column(INTEGER(11), comment="1好评，0差评")
#     响应速度 = Column(INTEGER(11), comment="1好评，0差评")
#     服务态度 = Column(INTEGER(11), comment="1好评，0差评")
#     专业能力 = Column(INTEGER(11), comment="1好评，0差评")
#     配合资源 = Column(INTEGER(11), comment="1好评，0差评")
#     更多建议 = Column(String(255))
#     好评内容 = Column(String(255))
#     差评内容 = Column(String(45))
#     评价类型 = Column(String(255))
#     投诉状态 = Column(String(45), server_default=text("'open'"))
#     投诉日期 = Column(DateTime)
#     处理日期 = Column(String(255))
#     处理人 = Column(String(255))
#     初始结论 = Column(String(255))
#     最终结论 = Column(String(255))
#     需求日期 = Column(DateTime)
#     初始完成日期 = Column(DateTime)
#     最终完成日期 = Column(DateTime)


class Smbom(Base):
    __tablename__ = "smbom"
    __table_args__ = (Index("query", "prtno", "deltapn"),)

    id = Column(INTEGER(11), primary_key=True)
    prtno = Column(String(255), nullable=False)
    designno = Column(Text, nullable=False)
    deltapn = Column(String(255), index=True)
    des = Column(String(255))
    mfgname = Column(String(255))
    mfgpn = Column(String(255))
    stockno = Column(String(255))
    pcb_remark = Column(String(255))
    up_date = Column(DateTime, nullable=False)
    item_pur = Column(String(255))
    qty = Column(String(255))
    status = Column(String(255))
    designpart = Column(String(255), server_default=text("''"))
    commonpart = Column(String(255))
    category = Column(String(255))
    packaging = Column(String(45), nullable=False)
    checkcode = Column(String(255), nullable=False, index=True)


# class SmdLeadTime(Base):
#     __tablename__ = "smd_lead_time"

#     ID = Column(INTEGER(11), primary_key=True)
#     Placer = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="设备名称"
#     )
#     preparation = Column(String(255), server_default=text("'0'"))
#     load_material = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="上料时间"
#     )
#     check_material = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="核对材料"
#     )
#     picking_position_alignment = Column(
#         String(255), server_default=text("'0'"), comment="取料位置对中"
#     )
#     mark = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="装卸料枪"
#     )
#     offset = Column(String(255), nullable=False, server_default=text("'0'"))
#     create_panel = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="在线调试"
#     )
#     modify_data = Column(String(255), server_default=text("'0'"), comment="修正数据")
#     check_first_sample = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="首样制确认"
#     )
#     solder_paste = Column(String(255), server_default=text("'0'"), comment="刷锡膏")
#     placement = Column(
#         String(255), nullable=False, server_default=text("'0'"), comment="贴装时间"
#     )
#     alarm = Column(String(255), server_default=text("'0'"), comment="报警处理")
#     reflow = Column(String(255))
#     feeder = Column(String(255), comment="BM221feeder调整")


# class Smddip(Base):
#     __tablename__ = "smddip"

#     ID = Column(INTEGER(11), primary_key=True)
#     C_DeltaPN = Column(String(45))
#     C_DES = Column(String(45))
#     C_memo = Column(String(225))
#     C_feedback = Column(String(225))
#     R_ED = Column(String(225))
#     R_Sequence = Column(INTEGER(11))
#     R_Type = Column(String(225))


# class SpecWorkRecord(Base):
#     __tablename__ = "spec_work_record"

#     ID = Column(INTEGER(11), primary_key=True)
#     Status = Column(String(255))
#     Model = Column(String(255))
#     Rd = Column(String(255))
#     Dept = Column(String(255))
#     DocType = Column(String(255), comment="文件类别")
#     EcnNo = Column(String(255))
#     Qty = Column(INTEGER(11), comment="数量")
#     ES = Column(INTEGER(11))
#     TS = Column(INTEGER(11))
#     SN = Column(INTEGER(11))
#     RC = Column(INTEGER(11))
#     Others = Column(INTEGER(11))
#     PN = Column(INTEGER(11))
#     RunningDate = Column(DateTime)
#     Remark = Column(String(1024))
#     InputDateAuto = Column(DateTime)
#     InputDateSpec = Column(DateTime)
#     ReleaseDate = Column(DateTime)
#     FollowupDate = Column(DateTime)
#     Owner = Column(String(255))
#     StdLT = Column(INTEGER(11))
#     ActLT = Column(INTEGER(11))
#     ReleaseOnTime = Column(String(255))
#     FollowUpLT = Column(INTEGER(11))
#     FollowUpOnTime = Column(String(255))
#     urgent = Column(Enum("NO", "YES"), server_default=text("'NO'"))


# class SspIssue(Base):
#     __tablename__ = "ssp_issue"

#     id = Column(INTEGER(10), primary_key=True, comment="自增id")
#     gmt_create = Column(DateTime, nullable=False, comment="创建时间")
#     gmt_modified = Column(DateTime, nullable=False, comment="修改时间")
#     user = Column(String(255), nullable=False, comment="需求人")
#     type = Column(String(255), nullable=False, comment="需求类型")
#     program = Column(
#         String(255), nullable=False, server_default=text("''"), comment="程式名(excel文件名)"
#     )
#     description = Column(String(5000), nullable=False, comment="内容描述")
#     benefit = Column(
#         String(5000), nullable=False, server_default=text("''"), comment="效益评估"
#     )
#     owner = Column(
#         String(255), nullable=False, server_default=text("''"), comment="程式处理人"
#     )
#     demand_date = Column(DateTime, comment="需求日期")
#     start_date = Column(DateTime, comment="开始处理日期")
#     finish_date = Column(DateTime, comment="完成日期")
#     status = Column(
#         String(255), nullable=False, server_default=text("'Open'"), comment="状态"
#     )
#     satisfaction = Column(
#         INTEGER(2), nullable=False, server_default=text("'0'"), comment="满意度"
#     )
#     remark = Column(
#         String(255), nullable=False, server_default=text("''"), comment="备注信息"
#     )


# class StockModifyrecord(Base):
#     __tablename__ = "stock_modifyrecord"

#     ID = Column(INTEGER(11), primary_key=True)
#     DeltaPN = Column(String(255), nullable=False)
#     Modify_Type = Column(String(255))
#     Modify1 = Column(String(255))
#     Modify2 = Column(String(255))
#     M_Date = Column(DateTime)
#     Owner = Column(String(255))
#     area = Column(String(2), comment="库存区域")


# class Stockin(Base):
#     __tablename__ = "stockin"

#     ID = Column(INTEGER(11), primary_key=True)
#     StockInDate = Column(DateTime, nullable=False, comment="入库时间")
#     Source = Column(String(50), comment="来源")
#     DeltaPN = Column(String(40), nullable=False, index=True, comment="料号")
#     Qty = Column(INTEGER(7), nullable=False, comment="数量")
#     Owner = Column(String(26), nullable=False, comment="入料人")
#     Area = Column(CHAR(2))
#     checkcode = Column(String(45), nullable=False, index=True)


# class StocknoList(Base):
#     __tablename__ = "stockno_list"

#     ID = Column(INTEGER(11), primary_key=True)
#     StockNo = Column(String(255), nullable=False, unique=True)
#     Type = Column(String(45))
#     Category = Column(String(45))
#     Packing = Column(String(45))
#     CreateDate = Column(
#         TIMESTAMP, nullable=False, server_default=text("CURRENT_TIMESTAMP")
#     )
#     OccupyDate = Column(TIMESTAMP, server_default=text("CURRENT_TIMESTAMP"))
#     Area = Column(String(45))
#     InUse = Column(String(45))


# class Stockout(Base):
#     __tablename__ = "stockout"

#     ID = Column(INTEGER(11), primary_key=True)
#     StockOutDate = Column(DateTime, nullable=False, comment="领料需求发起时间")
#     DEPT = Column(String(255), nullable=False, comment="部门")
#     TYPE = Column(String(255), nullable=False, comment="出库类型")
#     PrtNo = Column(String(255), nullable=False, comment="项目号")
#     DeltaPN = Column(String(255), nullable=False, index=True)
#     QTY = Column(
#         INTEGER(11), nullable=False, server_default=text("'0'"), comment="quantity"
#     )
#     Owner1 = Column(String(255), nullable=False, comment="Bom Owner")
#     StockOutDate2 = Column(DateTime, comment="picking time point")
#     Owner2 = Column(String(255), comment="picking")
#     Lable = Column(String(45))
#     Area = Column(CHAR(2), nullable=False)
#     Memo = Column(String(1000))
#     Checkcode = Column(String(255), nullable=False, index=True)
#     source = Column(
#         String(50),
#         server_default=text("''"),
#         comment="出库记录来源,picking:工程师领料，Internal_picking:库存查询领料，BOM:扣库作业",
#     )
#     dept_id = Column(INTEGER(11), nullable=False)


class User(Base):
    __tablename__ = "user"

    id = Column(INTEGER(11), primary_key=True)
    onno = Column(String(255))
    name = Column(String(45), nullable=False)
    area = Column(String(45), nullable=False)
    dept = Column(String(45), nullable=False)
    deptcode = Column(String(255))
    role_group = Column(String(45), nullable=False)
    dept_detail = Column(String(255))
    ext = Column(String(45))
    job_title = Column(String(255))
    nt_name = Column(String(255), nullable=False, unique=True)
    email = Column(String(45))
    u_date = Column(DateTime)
    termdate = Column(DateTime, comment="离职日期")
    remark = Column(String(255))
    有无资产 = Column(String(255))
    dept_id = Column(INTEGER(11), nullable=False)


class Vendor(Base):
    __tablename__ = "vendor"

    id = Column(INTEGER(10), primary_key=True)
    commodity = Column(String(255))
    vendor_name = Column(String(255))
    agent_name = Column(String(255))
    oa_code = Column(String(255))
    window = Column(String(255))
    chinese_name = Column(String(255))
    english_name = Column(String(255))
    title = Column(String(255))
    tel = Column(String(255))
    mobile = Column(String(255))
    mail = Column(String(255))


# class VendorList(Base):
#     __tablename__ = "vendor_list"

#     ID = Column(INTEGER(11), primary_key=True)
#     Category1 = Column(String(255))
#     Category2 = Column(String(255))
#     VendorName = Column(String(255))


# class ZGrossStockout(Base):
#     __tablename__ = "z_gross_stockout"

#     id = Column(INTEGER(11), primary_key=True)
#     c_deltapn = Column(String(255))
#     c_des = Column(String(255))
#     c_area = Column(String(255))
#     ratio = Column(String(255))
#     loss = Column(String(255))
#     remark = Column(String(255))


# class ZLossRate(Base):
#     __tablename__ = "z_loss_rate"

#     ID = Column(INTEGER(11), primary_key=True)
#     C_DeltaPN = Column(String(255), nullable=False)
#     C_StockNo = Column(String(255), nullable=False)
#     C_DES = Column(String(255), nullable=False)
#     D_PRICE = Column(String(255), nullable=False)
#     C_AREA = Column(String(255), nullable=False)
#     DISCOUNT = Column(Float(asdecimal=True), nullable=False)
#     LOSS = Column(BIGINT(20), nullable=False)
#     REMARK = Column(String(255))


# class ZProcessDotting(Base):
#     __tablename__ = "z_process_dotting"

#     ID = Column(INTEGER(11), primary_key=True)
#     Up_time = Column(DateTime)
#     Owner = Column(String(45))
#     A_Process = Column(String(45))
#     Memo1 = Column(String(225))
#     Memo2 = Column(String(225))
#     A_Dot = Column(String(600))


# class ZProgramVersioning(Base):
#     __tablename__ = "z_program_versioning"

#     ID = Column(INTEGER(11), primary_key=True)
#     ProgramName = Column(String(255))
#     LatestVersion = Column(String(255))
#     Update_Date = Column(DateTime)
#     Owner = Column(String(45))
#     LastVersion = Column(String(255))
#     Changes = Column(String(1255))


# class ZSerialization(Base):
#     __tablename__ = "z_serialization"

#     ID = Column(INTEGER(11), primary_key=True)
#     DesignNo = Column(String(45))
#     C_DeltaPN = Column(String(45))
#     C_DES = Column(String(45))
#     C_DES_notlike = Column(String(45))
#     C_MFGNAME = Column(String(45))
#     C_MFGPN = Column(String(45))
#     CT_1 = Column(String(45))
#     CT_2 = Column(String(45))
#     CT_3 = Column(String(45))
#     CT_4 = Column(String(45))
#     B2 = Column(String(255))
#     B4 = Column(String(255))
#     B3 = Column(String(255))
#     Sequence = Column(DECIMAL(10, 1))
#     TypeAB = Column(String(45))
#     Memo = Column(String(455))


class Prt(Base):
    __tablename__ = "prt"
    __table_args__ = (Index("prtproj", "PrtNo", "Proj"),)

    ID = Column(INTEGER(11), primary_key=True, comment="自增ID")
    project_id = Column(INTEGER(11), nullable=False)
    stage = Column(INTEGER(11), nullable=False)
    board = Column(String(255), nullable=False)
    PrtNo = Column(String(255), nullable=False, unique=True, comment="项目号")
    Proj = Column(String(255), nullable=False, comment="机种名")
    PCBPN = Column(String(255))
    Qty = Column(INTEGER(11), nullable=False, comment="样制数量")
    SMStatus = Column(String(255), nullable=False, comment="样制状态")
    Dept = Column(String(255), nullable=False, comment="部门")
    PM = Column(
        String(255), nullable=False, server_default=text("'NA'"), comment="项目管理"
    )
    EE = Column(
        String(255), nullable=False, server_default=text("'NA'"), comment="电子工程师"
    )
    ME = Column(
        String(255), nullable=False, server_default=text("'NA'"), comment="机构工程师"
    )
    MAG = Column(
        String(255), nullable=False, server_default=text("'NA'"), comment="磁工程师"
    )
    Layout = Column(
        String(255),
        nullable=False,
        server_default=text("'NA'"),
        comment="项目画板工程师",
    )
    AppDate = Column(DateTime, nullable=False, comment="申请时间")
    FSQty = Column(INTEGER(11))
    FSDate_Req = Column(DateTime, nullable=False, comment="首样需求时间")
    PCBStatus = Column(String(255), comment="PCB状态")
    mat_ready_date = Column(DateTime, comment="材料确认日期")
    StartDate_Sch = Column(DateTime, comment="计划开始时间")
    FSDate_Sch = Column(DateTime, comment="首样计划时间")
    FSDate_Act = Column(DateTime, comment="首样完成时间")
    SMTStaDate = Column(DateTime, comment="SMD开始时间")
    SMTFin_Date = Column(DateTime, comment="SMD结束时间")
    DIPStaDate = Column(DateTime, comment="插件开始时间，开始时间当前未使用")
    FinDate_Sch = Column(String(255), comment="全部样品结束时间和生管备注")
    FinDate_Act = Column(String(255), comment="样制完成时间")
    Area = Column(String(45), nullable=False)
    B_EE = Column(
        String(45),
        nullable=False,
        server_default=text("'Y'"),
        comment="默认Y：需要,NA：不需要，X：BOM完成",
    )
    B_ME = Column(
        String(45),
        nullable=False,
        server_default=text("'Y'"),
        comment="默认Y：需要,NA：不需要，X：BOM完成",
    )
    B_MAG = Column(
        String(45),
        nullable=False,
        server_default=text("'Y'"),
        comment="默认Y：需要,NA：不需要，X：BOM完成",
    )
    PrtBOMReceive = Column(
        String(45),
        nullable=False,
        server_default=text("'Y'"),
        comment="默认Y，表示未锁定，可以接受BOM修改，N表示锁定，X表示解锁状态，X时BOM作业上传一次后变为N",
    )
    Placer = Column(
        String(255),
        nullable=False,
        server_default=text("'未指派'"),
        comment="用于两台贴片机选择MY300和BM221",
    )
    LeadTime = Column(
        Float(3),
        nullable=False,
        server_default=text("'0.0'"),
        comment="根据公式预估的项目工时,单位小时",
    )
    smd_deadline = Column(DateTime, comment="SMD截止时间(根据开始时间和工时计算而得)")
    MatStatus = Column(
        String(255),
        server_default=text("''"),
        comment="散料备料并行作业，增加一个状态栏位",
    )
    mat1_date = Column(DateTime, comment="散料备料完成时间")
    mat2_date = Column(DateTime, comment="卷料备料完成时间")
    prog_date = Column(DateTime, comment="编程完成时间")
    order = Column(
        INTEGER(11),
        nullable=False,
        server_default=text("'1'"),
        comment="每日SMD开始顺序",
    )
    panel = Column(
        INTEGER(11), nullable=False, server_default=text("'1'"), comment="拼板数量"
    )
    side = Column(
        INTEGER(11),
        nullable=False,
        server_default=text("'2'"),
        comment="单面板1，双面板2",
    )
    smd_count = Column(
        INTEGER(11), nullable=False, server_default=text("'0'"), comment="SMD材料种类数"
    )
    smd_sum = Column(
        INTEGER(11),
        nullable=False,
        server_default=text("'0'"),
        comment="每块板SMD点位数，总和要乘以QTY",
    )
    dip_count = Column(
        INTEGER(10), nullable=False, server_default=text("'0'"), comment="插件种类"
    )
    dip_sum = Column(
        INTEGER(10),
        nullable=False,
        server_default=text("'0'"),
        comment="每块板插件点位数，总和要乘以QTY",
    )
    dip_std_time = Column(
        INTEGER(11), server_default=text("'0'"), comment="插件标准工时"
    )
    dip_start_1st = Column(DateTime)
    dip_end_1st = Column(DateTime)
    dip_start_2nd = Column(DateTime)
    dip_end_2nd = Column(DateTime)
    dip_start_3rd = Column(DateTime)
    dip_end_3rd = Column(DateTime)
    ChangeQty = Column(String(600), comment="待删除，扣库上传有用到")
    completed_qty = Column(
        INTEGER(11), server_default=text("'0'"), comment="已完成数量(插件分配作业)"
    )
    Plate = Column(String(225), server_default=text("''"), comment="钢板")
    delete_ReturnNote = Column(String(600))
    delete_M_Owner = Column(String(255))
    PCB_QTY = Column(INTEGER(11), comment="功能未使用PCB买板数量，可能大于样制数量")
    FShort_Qty = Column(INTEGER(11), comment="KPI程式有用到")
    QtyNote = Column(String(255), comment="bom上传程式有用到")
    delete_PCBSize = Column(String(255))
    delete_MatNote = Column(String(255))
    delete_BOMNote = Column(String(1255))
    Delay_Remark = Column(String(255), comment="KPI程式有用到")
    delete_Mat_Provide_Date = Column(DateTime)
    delete_MaterialNoteDate = Column(DateTime)
    delete_MaterialOwnerNote = Column(String(255))
    dept_id = Column(
        ForeignKey("dept.id", ondelete="CASCADE"), nullable=False, index=True
    )

    dept = relationship("Dept")


# class Stock(Base):
#     __tablename__ = "stock"
#     __table_args__ = (
#         Index("idx1", "DeltaPN", "Area"),
#         Index("unique_idx", "Area", "CheckCode", unique=True),
#     )

#     ID = Column(INTEGER(11), primary_key=True)
#     DeltaPN = Column(String(50), nullable=False, comment="料号")
#     StockNo = Column(
#         ForeignKey("stockno_list.StockNo", onupdate="CASCADE"),
#         nullable=False,
#         unique=True,
#         comment="库位号",
#     )
#     DES = Column(String(150), server_default=text("''"), comment="描述")
#     MFGNAME = Column(String(70), server_default=text("''"), comment="厂商名称")
#     MFGPN = Column(String(70), server_default=text("''"), comment="厂商料号")
#     QTY = Column(INTEGER(8), nullable=False, comment="数量")
#     SMMargin = Column(INTEGER(5), server_default=text("'2'"))
#     FirstStockInDate = Column(DateTime, comment="首次入库时间")
#     StockInDate = Column(DateTime, comment="最近一次入库时间")
#     LimitUse = Column(
#         String(100), nullable=False, server_default=text("'ALL'"), comment="限制使用,默认值ALL"
#     )
#     Area = Column(String(4), nullable=False, comment="区域")
#     Type_del = Column(String(10))
#     TOL_del = Column(String(16))
#     Package = Column(String(36))
#     V1_del = Column(String(14))
#     V2_del = Column(String(18))
#     CheckCode = Column(String(30), nullable=False, index=True)

#     stockno_list = relationship("StocknoList")

db.create_all()
