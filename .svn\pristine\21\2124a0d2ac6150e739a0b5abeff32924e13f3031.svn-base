# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import dash_table, html

from common import dropdown_conditional, get_nt_name, get_ssp_user, id_factory

id = id_factory(__name__)


tab_add_row = dbc.<PERSON><PERSON>(
    className="fa fa-plus",
    size="sm",
    color="light",
    id=id("add-row"),
)
columns = [
    {"name": "台达料号", "id": "deltapn", "presentation": "input"},
    {"name": "描述", "id": "des", "presentation": "input"},
    {"name": "厂商", "id": "mfgname", "presentation": "input"},
    {"name": "厂商型号", "id": "mfgpn", "presentation": "input"},
    {"name": "材料类型1", "id": "cat1", "presentation": "dropdown"},
    {"name": "材料类型2", "id": "cat2", "presentation": "dropdown"},
    {"name": "材料类型3", "id": "cat3", "presentation": "dropdown"},
    {"name": "旧料号规格是否作废", "id": "old_invalid", "presentation": "dropdown"},
    {"name": "变更内容", "id": "change_content", "presentation": "input"},
    # {"name": "最新的规格文件(附件)", "id": "attachment", "presentation": "input"},
]

table = dash_table.DataTable(
    data=[{}],
    columns=columns,
    editable=True,
    row_deletable=True,
    is_focused=True,
    id=id("table"),
    style_cell={
        "whiteSpace": "normal",
        "height": "auto",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
    },
    style_cell_conditional=[
        {"if": {"column_id": "change_content"}, "width": "30%"},
    ],
    dropdown={
        "old_invalid": {
            "options": [{"label": "是", "value": "是"}, {"label": "否", "value": "否"}]
        }
    },
    css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    dropdown_conditional=dropdown_conditional(),
)

title = dmc.Center(dmc.Text("料号升级申请单", weight=700, id=id("title")))

attachment = fac.AntdDraggerUpload(
    apiUrl="/upload/",
    text="最新的规格文件",
    id=id("attachment"),
    lastUploadTaskRecord={},
    # style={"width": "455px"},
)

submit = dmc.Button("提交", id=id("rd-submit"))


def layout(**kwargs):
    nt_name = get_nt_name()
    users = get_ssp_user()
    user = users.loc[users["nt_name"] == nt_name]
    role_group = user["role_group"].iloc[0]
    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True
    applicant = dmc.Select(
        label="申请人(Applicant)",
        withAsterisk=True,
        size="xs",
        id=id("applicant"),
        placeholder="申请人(Applicant)",
        value=nt_name,
        data=users["nt_name"].tolist(),
        disabled=applicant_disabled,
        searchable=True,
        style={"width": 150},
    )
    return dmc.Container(
        dmc.Stack(
            [
                title,
                dmc.Divider(),
                applicant,
                html.Div([table, tab_add_row]),
                attachment,
                submit,
            ]
        )
    )
