# -*- coding: utf-8 -*-
from datetime import datetime

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, callback, dash, html

from config import pool

dash.register_page(__name__, path="/", title="首页")
satisfaction_modal = dbc.<PERSON>(
    [
        dbc.<PERSON>eader("满意度调查", style={"font-weight": "bold"}),
        dbc.ModalBody("本月为满意度调查月,请您抽出宝贵的时间,对我们的工作进行评价"),
        dbc.ModalFooter(
            [
                dbc.InputGroup(
                    [
                        dbc.<PERSON><PERSON>(
                            "立即前往",
                            href="/satisfaction",
                            className="ml-auto",
                            color="warning",
                            style={"width": "100%"},
                        )
                    ],
                    className="mb-3",
                ),
            ]
        ),
    ],
    id="modal-notice",
    centered=True,
    is_open=False,
)

colors = {
    "专用(Special)": {
        50: "#63CFCF",
        100: "#4FCACA",
        200: "#3ABFBF",
        300: "#37B4B4",
        400: "#33A8A8",
        500: "#2E9999",
        600: "#2B8D8D",
        700: "#257979",
        800: "#1E6262",
        900: "#154646",
        950: "#113737",
    },
    "公用(Public)": {
        50: "#C27070",
        100: "#BF6969",
        200: "#BA5E5E",
        300: "#B45050",
        400: "#A84848",
        500: "#9A4141",
        600: "#8C3B3B",
        700: "#813737",
        800: "#723131",
        900: "#642B2B",
        950: "#5E2828",
    },
    "Public": {
        50: "#C27070",
        100: "#BF6969",
        200: "#BA5E5E",
        300: "#B45050",
        400: "#A84848",
        500: "#9A4141",
        600: "#8C3B3B",
        700: "#813737",
        800: "#723131",
        900: "#642B2B",
        950: "#5E2828",
    },
    "工程服务(Service)": {
        50: "#87AEED",
        100: "#7EA8EC",
        200: "#719FEA",
        300: "#5F93E7",
        400: "#528AE5",
        500: "#4581E3",
        600: "#3375E1",
        700: "#256CDF",
        800: "#1F63D1",
        900: "#1D5DC3",
        950: "#1C59BC",
    },
}

# -----------------config------------------
service = {
    "title": "工程服务(Service)",
    "data": [
        {
            "icon": "antd-app-store",
            "text": "规格发行",
            "href": "/spec-ee",
        },
        {
            "icon": "antd-branches",
            "text": "失效分析",
            "href": "/ce/fa/rd",
        },
        {
            "icon": "antd-build",
            "text": "料号申请",
            "href": "/ce/pn",
            "permission": [
                {"dept": "DCBU_DCBU", "role": ["ALL"]},
                {"dept": "ADP_ADP", "role": ["ALL"]},
                {"dept": "NBE_IPS", "role": ["ALL"]},
                {"dept": "AMP_AMP", "role": ["ALL"]},
                {"dept": "ATI_ATI", "role": ["ALL"]},
                {"dept": "EVCS_MODULE", "role": ["ALL"]},
                {"dept": "IDC_IDC", "role": ["ALL"]},
                {"dept": "HPRT_HPRT", "role": ["ALL"]},
                {"dept": "EVCS_SYSTEM", "role": ["ALL"]},
                {"dept": "BABG_LGT", "role": ["ALL"]},
                {"dept": "TPS_TPS", "role": ["ALL"]},
                {"dept": "DES_CDBU", "role": ["ALL"]},
                {"dept": "APE_APE", "role": ["ALL"]},
                {"dept": "LGT_LGT", "role": ["PM", "EE"]},
                {"dept": "DES_IMBU", "role": ["ALL"]},
                {"dept": "NBE_HVG", "role": ["ALL"]},
            ],
        },
        {"icon": "antd-table", "text": "个人中心", "href": "/info/rd"},
    ],
}

public = {
    "title": "公用(Public)",
    "data": [
        {"icon": "antd-file-search", "text": "材料查询", "href": "/material"},
        {"icon": "antd-team", "text": "样制", "href": "/project"},
        {"icon": "antd-repair", "text": "工具领用", "href": "/tools"},
        {"icon": "antd-account-book", "text": "资产管理", "href": "/asset"},
        {"icon": "md-description", "text": "常用表单", "href": "/file"},
        {"icon": "antd-global", "text": "内部培训", "href": "/trainning"},
    ],
}

overseas = {
    "title": "Public",
    "data": [
        {"icon": "antd-file-search", "text": "Part Query", "href": "/material"},
    ],
}

special = {
    "title": "专用(Special)",
    "data": [
        {
            "icon": "antd-app-store-add",
            "text": "PCB",
            "href": "/pcb",
            "permission": [
                {"dept": "DES_CDBU", "role": ["LAYOUT"]},
                {"dept": "APE_APE", "role": ["LAYOUT"]},
                {"dept": "DES_IMBU", "role": ["LAYOUT"]},
            ],
            "dept": {"DES_CDBU", "APE_APE", "DES_IMBU"},
            "role": {"LAYOUT"},
        },
        {
            "icon": "antd-app-store-add",
            "text": "DummyLoad",
            "href": "/dummyload",
            "dept": {"DES_CDBU", "APE_APE", "DES_IMBU"},
            "role": {"PM", "EE", "ME"},
            "permission": [
                {"dept": "DES_CDBU", "role": ["PM", "EE", "ME"]},
                {"dept": "APE_APE", "role": ["PM", "EE", "ME"]},
                {"dept": "DES_IMBU", "role": ["PM", "EE", "ME"]},
            ],
        },
        {
            "icon": "antd-file-search",
            "text": "变更单检查",
            "href": "/ypcs21",
            "dept": {"DCBU_DCBU", "LGT_LGT", "SUP_SUP"},
            "role": {"SPEC", "PM", "EE", "ME"},
            "permission": [
                {"dept": "DCBU_DCBU", "role": ["PM", "EE", "ME"]},
                {"dept": "LGT_LGT", "role": ["PM", "EE", "ME"]},
                {"dept": "SUP_SUP", "role": ["SPEC"]},
            ],
        },
        {
            "icon": "antd-folder-open",
            "text": "EMC预约",
            "href": "http://sup.deltaww.com:8083/EMCtest/index/",
            "dept": {"AMP_AMP", "ATI_ATI"},
            "permission": [
                {"dept": "AMP_AMP", "role": ["ALL"]},
                {"dept": "ATI_ATI", "role": ["ALL"]},
            ],
        },
        {
            "icon": "antd-key",
            "text": "LessonLearn",
            "href": "http://sup.deltaww.com/amp/lesson_learn",
            "dept": {"AMP_AMP", "ATI_ATI"},
            "permission": [
                {"dept": "AMP_AMP", "role": ["ALL"]},
                {"dept": "ATI_ATI", "role": ["ALL"]},
                {"dept": "SUP_SUP", "role": ["CE"]},
            ],
        },
        {
            "icon": "antd-pushpin",
            "text": "磁组",
            "href": "/magnetics",
            "dept": {"MES_MES", "MSBU_MSBU"},
            "permission": [
                {"dept": "MES_MES", "role": ["ALL"]},
                {"dept": "MSBU_MSBU", "role": ["ALL"]},
            ],
        },
        {
            "icon": "antd-highlight",
            "text": "NRE",
            "href": "/nre",
            "dept": {"DES_CDBU", "APE_APE", "DES_IMBU"},
            "permission": [
                {"dept": "DES_CDBU", "role": ["ALL"]},
                {"dept": "APE_APE", "role": ["ALL"]},
                {"dept": "DES_IMBU", "role": ["ALL"]},
            ],
        },
        {
            "icon": "antd-highlight",
            "text": "重工",
            "href": "/rework",
            "dept": {"SUP_SUP"},
            "permission": [{"dept": "SUP_SUP", "role": ["PUR"]}],
        },
        {
            "icon": "antd-branches",
            "text": "失效分析",
            "href": "/ce/fa/rd",
            "dept": {"SUP_SUP"},
            "role": {"CE"},
            "permission": [{"dept": "SUP_SUP", "role": ["CE"]}],
        },
        {
            "icon": "antd-build",
            "text": "料号申请",
            "href": "/ce/pn",
            "dept": {"SUP_SUP"},
            "role": {"CE"},
            "permission": [{"dept": "SUP_SUP", "role": ["CE"]}],
        },
        {
            "icon": "antd-car",
            "text": "领料",
            "href": "/picking",
            "dept": {"SUP_SUP"},
            "permission": [{"dept": "SUP_SUP", "role": ["ALL"]}],
        },
    ],
}


def antd_card(config: dict, dept: str, role: str):
    items = []
    for i in config["data"]:
        item = fac.AntdCardGrid(
            html.A(
                [
                    fac.AntdFlex(
                        [
                            fac.AntdIcon(icon=i["icon"], style={"fontSize": "55px"}),
                            fac.AntdTitle(
                                i["text"],
                                level=5,
                                style={
                                    "color": "#fff",
                                    "whiteSpace": "nowrap",
                                    "overflow": "hidden",
                                    "textOverflow": "ellipsis",
                                    "fontSize": "clamp(10px, 2vw, 16px)",
                                    "lineHeight": "1.2",
                                },
                            ),
                        ],
                        vertical=True,
                        align="center",
                    )
                ],
                href=i["href"],
                style={
                    "color": "#fff",
                    "font-size": "17px",
                    "font-weight": "bold",
                },
                target="_blank",
            ),
            style={
                "width": "130px",
                "height": "130px",
                "display": "flex",
                "justifyContent": "center",
                "alignItems": "center",
            },
            hoverable=True,
        )
        permission = i.get("permission")
        if permission:
            x = [
                i
                for i in permission
                if i.get("dept") == dept
                and (role in i.get("role") or "ALL" in i.get("role"))
            ]
            if x:
                items.append(item)
        else:
            items.append(item)

    color = list(colors.get(config["title"]).values())
    for i, j in enumerate(items):
        j.style["background"] = color[i]

    if dept == "SUP_SUP":
        cols = 4
    else:
        cols = 3

    div = fac.AntdCard(
        dmc.SimpleGrid(items, cols=cols, ml=-10),
        title=config["title"],
        styles={
            "header": {
                "font-size": "2rem",
                "font-weight": "bold",
                "color": "rgb(0, 159, 232)",
            }
        },
        style={
            "background": "rgb(235, 245, 253)",
            "box-shadow": " 0 4px 8px 0 rgb(0 0 0 / 10%)",
            "border-bottom": "4px solid rgb(112, 184, 240)",
            "height": "85vh",
        },
        size="small",
    )
    return div


# ===============layout====================
def layout(**kwargs):
    layout = dbc.Container([html.Div(id="index"), satisfaction_modal], fluid=True)
    return layout


# ================回调函数====================
@callback(
    Output("index", "children"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def index_content(user):
    dept = user.get("dept")
    role = user.get("role_group")
    dept_id = user.get("dept_id")
    area = user.get("area")

    if dept_id == 10:
        div = dmc.SimpleGrid(
            [
                antd_card(public, dept, role),
                antd_card(special, dept, role),
            ],
            cols=2,
        )
    else:
        div = dmc.SimpleGrid(
            [
                antd_card(service, dept, role),
                antd_card(public, dept, role),
                antd_card(special, dept, role),
            ],
            cols=3,
        )
    # 海外区域
    if area in ["DE", "TH", "IN"]:
        div = antd_card(overseas, dept, role)

    return div


@callback(
    Output("modal-notice", "is_open"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def satisfaction_survey(user):
    """4月，11月满意度调查"""
    survey_months = [4, 11]
    now = datetime.now()

    if now.month not in survey_months:
        raise PreventUpdate

    owner = user.get("nt_name")
    if owner is None:
        raise PreventUpdate
    dept_id = user.get("dept_id")

    if dept_id == 10:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select id from ssp.satisfaction \
                where 投诉人=%s and year(投诉日期)=%s and month(投诉日期)=%s"
            params = [owner, now.year, now.month]
            cu.execute(sql, params)
            res = cu.fetchone()
            if res:
                return False
            else:
                return True
