# -*- coding: utf-8 -*-
from datetime import datetime
from functools import reduce

import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dash,
)

from common import add_mat_category, df_insert_cu, read_sql
from components.notice import notice
from config import UPLOAD_FOLDER_ROOT, pool
# from dbtool import db

from . import layout
from utils import db

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/survey/rd", title="材料调查-RD")

bom_cols = {
    "DeltaPN\n台达料号": "deltapn",
    "Description\n描述": "des",
    "MFG NAME\n厂商名称": "mfgname",
    "MFG PART\n厂商料号": "mfgpn",
    "%\n主次SOURCE区分": "source",
    "DESGIN NO \n位置号": "designno",
    "Remark\nRD备注": "remark",
}

survey_db = {
    "life_time": ["life", "tct", "y_n"],
    "operation_temp": ["ta_min", "ta_max"],
    "flammability": ["flammability"],
    "auto_motive": [
        "device_sop_date",
        "device_total_volume",
        "process_sop_date",
        "process_total_volume",
    ],
}


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State(id("applicant"), "value"),
    State(id("model"), "value"),
    State(id("customer"), "value"),
    State(id("assign"), "value"),
    State(id("survey"), "value"),
    State(id("attachment"), "lastUploadTaskRecord"),
    State("user", "data"),
)
def rd_submit(n_clicks, rd, model, customer, assign, survey, attachment, user):
    if not n_clicks:
        raise PreventUpdate

    if not (rd and model and customer and assign and survey and attachment):
        return notice("请填写数据", "warning"), False

    res = db.find_one("ssp.user", {"nt_name": rd})
    rd_dept = res["dept"]
    rd_dept_id = res["dept_id"]
    model_id = f"{rd}{datetime.now():%y%m%d%H%M%S}"

    nt_name = user.get("nt_name")
    attach = f'{attachment.get("taskId")}'
    file_name = f'{attachment.get("fileName")}'
    df = pd.read_excel(
        UPLOAD_FOLDER_ROOT / attach / file_name, dtype=str, keep_default_na=False
    )
    df = df.rename(columns=bom_cols)
    if (df["designno"] == "").any():
        return notice("位置号不能为空", "warning"), False

    if (df["deltapn"] == "").any():
        return notice("料号不能为空", "warning"), False
    # print(df["deltapn"])
    # df = df.reset_index()

    for db_name in survey:
        # sql = f"select * from ce.{db_name}"
        # dfi = read_sql(sql)
        # t1 = time.perf_counter()
        # cols = ",".join(survey_db.get(db_name))
        # print(cols)
        # sql = f"select * from df a left join (select df.index,b.* from df,LATERAL \
        #     (select {cols} from {db_name} \
        #         where df.deltapn glob deltapn \
        #             and df.des glob des \
        #                 and df.mfgname glob mfgname \
        #                     and df.mfgpn glob mfgpn \
        #                         ORDER BY id limit 1)b)c on a.index=c.index"
        # dfx = duck.sql(sql).df()
        # print(time.perf_counter() - t1)

        sql = f"select id as {db_name},deltapn from ce.{db_name} where deltapn in %s"
        params = [df["deltapn"].unique().tolist()]
        dfi = read_sql(sql, params=params)
        dfi = dfi.drop_duplicates("deltapn")
        df = df.merge(dfi, on="deltapn", how="left")

    task = {
        "type": "材料调查",
        "sub_type": "汇总",
        "applicant": rd,
        "ce": nt_name,
        "status": "ongoing",
        "urgent": "一般",
        "dept": rd_dept,
        "dept_id": rd_dept_id,
        "model": model,
        "model_id": model_id,
    }
    task_id = db.insert("ce.task", task)

    df["model"] = model
    df["model_id"] = model_id
    df["customer"] = customer
    df["task_id"] = task_id
    df["attachment"] = attach
    df["survey_items"] = ",".join(survey)

    conn = pool.connection()
    cu = conn.cursor()
    df_insert_cu("ce.survey", df, cu)
    df = add_mat_category(df)
    df["ce"] = df["ce"].fillna("")

    conds = [df[i].isna() for i in survey]
    cond = reduce(lambda x, y: x | y, conds)
    df1 = df.loc[cond]

    for ce in df1["ce"].unique():
        dfi = df1.loc[df1["ce"] == ce]
        if ce == "":
            dfi_grp = dfi.groupby("deltapn", as_index=False).agg(
                {"mfgname": "first", "mfgpn": "first", "des": "first"}
            )
            for i in dfi_grp.itertuples():
                task = {
                    "type": "材料调查",
                    "sub_type": "待指派",
                    "applicant": rd,
                    "ce": nt_name,
                    "status": "open",
                    "urgent": "一般",
                    "dept": rd_dept,
                    "dept_id": rd_dept_id,
                    "model": model,
                    "model_id": model_id,
                    "deltapn": i.deltapn,
                    "des": i.des,
                    "mfgname": i.mfgname,
                    "mfgpn": i.mfgpn,
                }
                task_id = db.insert("ce.task", task)
                sql = "update ce.survey set task_id=%s \
                        where model_id=%s and deltapn = %s"
                params = [task_id, model_id, i.deltapn]
                cu.execute(sql, params)
        else:
            task = {
                "type": "材料调查",
                "applicant": rd,
                "ce": ce,
                "status": "open",
                "urgent": "一般",
                "dept": rd_dept,
                "dept_id": rd_dept_id,
                "model": model,
                "model_id": model_id,
            }
            task_id = db.insert("ce.task", task)
            sql = "update ce.survey set task_id=%s where model_id=%s and designno in %s"
            params = [task_id, model_id, dfi["designno"].tolist()]
            cu.execute(sql, params)
    conn.commit()
    conn.close()
    return notice("提交成功"), True


clientside_callback(
    """function (disabled) {
        if(!disabled){return ''};
        const keys = Object.keys(localStorage)
        for (let key of keys) {
            if (String(key).includes('pages-ce-survey-rd-layout')) {
                localStorage.removeItem(key)
            }
        }
        return ''
    }""",
    Output(id("persistence-clear"), "children"),
    Input(id("submit"), "disabled"),
)
