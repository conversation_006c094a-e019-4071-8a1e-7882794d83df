import feffery_antd_components.alias as fac
from dash_extensions.enrich import Input, Output, State, callback

from common import parse_search
from utils import db
from dash.exceptions import PreventUpdate


class ids:
    start = "start"
    end = "end"


# 定义20种清爽风格的颜色
COLORS = [
    "#A8E6CF",  # 薄荷绿
    "#FFD3B6",  # 淡橙色
    "#DCEDC1",  # 嫩绿色
    "#FFB6B9",  # 淡粉色
    "#B6E3E9",  # 天蓝色
    "#FFDFD3",  # 浅珊瑚色
    "#D4E157",  # 柠檬绿
    "#81C784",  # 森林绿
    "#4FC3F7",  # 湖蓝色
    "#FF8A65",  # 橘红色
    "#E1BEE7",  # 淡紫色
    "#C5E1A5",  # 草绿色
    "#80CBC4",  # 青绿色
    "#FFE082",  # 淡黄色
    "#F48FB1",  # 玫瑰色
    "#90CAF9",  # 天际蓝
    "#CE93D8",  # 薰衣草色
    "#80DEEA",  # 碧绿色
    "#BCAAA4",  # 暖灰色
    "#B39DDB",  # 淡靛蓝
]


def layout(user=None, id=None, **args):
    if id:
        res = db.find_one("ssp.prt_dip", {"id": id})
        if res:
            if res["start_time"]:
                start_disabled = True
            else:
                start_disabled = False
            if res["end_time"]:
                end_disabled = True
            else:
                end_disabled = False
            return fac.Flex(
                [
                    fac.Title(f"{res['prtno']}({res['owner']})", level=2),
                    fac.Flex(
                        [
                            fac.Button(
                                "作业开始",
                                # color="primary",
                                # variant="filled",
                                size="large",
                                style={"width": 300, "backgroundColor": "lightgreen"},
                                disabled=start_disabled,
                                motionType="happy-work",
                                shape="round",
                                id=ids.start,
                            ),
                            fac.Button(
                                "作业完成",
                                # color="danger",
                                # variant="filled",
                                size="large",
                                style={"width": 300, "backgroundColor": "orange"},
                                disabled=end_disabled,
                                motionType="happy-work",
                                shape="round",
                                id=ids.end,
                            ),
                        ],
                        justify="center",
                        align="center",
                        gap=200,
                        vertical=True,
                    ),
                ],
                vertical=True,
                align="center",
            )
        else:
            return fac.Empty()
    else:
        sql = "select id,prtno,owner from ssp.prt_dip \
            where (start_time is null or end_time is null)"
        res = db.execute(sql)
        div = fac.Flex(
            [
                fac.Button(
                    f"{i['prtno']}({i['owner']})",
                    # color="danger",
                    # variant="filled",
                    size="large",
                    style={"width": 300, "backgroundColor": COLORS[j]},
                    # disabled=end_disabled,
                    motionType="happy-work",
                    shape="round",
                    href=f"/scan/dip/?id={i['id']}",
                    target="_self",
                )
                for j, i in enumerate(res)
            ],
            vertical=True,
            gap=66,
            wrap=True,
            justify="center",
        )
        return fac.Flex(
            [fac.Title("项目清单", level=2), div],
            vertical=True,
            justify="center",
            align="center",
        )


@callback(
    Output(ids.start, "disabled"),
    Input(ids.start, "nClicks"),
    State("user", "data"),
    State("url", "search"),
)
def dip_start(n, user, search):
    search = parse_search(search)
    idx = search.get("id")
    if not n:
        raise PreventUpdate

    db.update(
        "ssp.prt_dip",
        {
            "id": idx,
            "start_time": datetime.now(),
            # "user": nt_name,
        },
    )
    return True


@callback(
    Output(ids.end, "disabled"),
    Input(ids.end, "nClicks"),
    State("user", "data"),
    State("url", "search"),
)
def dip_end(n, user, search):
    # TODO：原来的扫单，填delay原因
    search = parse_search(search)
    idx = search.get("id")
    if not n:
        raise PreventUpdate
    db.update(
        "ssp.prt_dip",
        {
            "id": idx,
            "end_time": datetime.now(),
        },
    )
    return True
