from dash_extensions.enrich import html
import feffery_utils_components as fuc
from typing import Literal


def notice(
    message: str = "提交成功",
    type: Literal["info", "success", "warning", "error"] = "success",
):
    if type == "error":
        color = "red"
    else:
        color = "green"

    return fuc.FefferyFancyNotification(
        html.Div(message, style={"color": color}),
        # autoClose=2000,
        closable=False,
        position="bottom-right",
        type=type,
        draggable=False,
        pauseOnHover=True
        # closeOnClick=True,
    )
