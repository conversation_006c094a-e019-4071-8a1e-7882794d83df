# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac
import feffery_utils_components.alias as fuc
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    ctx,
    dcc,
    html,
    no_update,
)

from common import (
    ce_cats,
    get_ce_owner,
    get_ssp_user,
    id_factory,
    parse_search,
    df_to_html,
)
from components import notice
from config import UPLOAD_FOLDER_ROOT, pool

# from dbtool import db
from datetime import datetime
from tasks import bg_mail
import pandas as pd
from utils import db

id = id_factory(__name__)


def form_part2(pn: dict):
    return html.Div(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="机种名",
                        description="(Model Number)",
                        size="xs",
                        id=id("model"),
                        value=pn.get("model"),
                    ),
                    dmc.TextInput(
                        label="机种功率",
                        description="(Watt)",
                        size="xs",
                        id=id("watt"),
                        value=pn.get("watt"),
                    ),
                    dmc.TextInput(
                        label="机种应用領域",
                        description="(Application)",
                        size="xs",
                        id=id("application"),
                        value=pn.get("application"),
                    ),
                    dmc.TextInput(
                        label="客户名称",
                        description="(Customer Name)",
                        size="xs",
                        id=id("customer"),
                        value=pn.get("customer"),
                    ),
                    dmc.TextInput(
                        label="项目阶段",
                        description="(Stage Of Project)",
                        size="xs",
                        id=id("stage"),
                        value=pn.get("stage"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
            dmc.Group(
                [
                    dmc.TextInput(
                        label="量产计划",
                        description="(MP Schedule)",
                        size="xs",
                        id=id("mp"),
                        value=pn.get("mp"),
                    ),
                    dmc.TextInput(
                        label="生产地点",
                        description="(Production location)",
                        size="xs",
                        id=id("location"),
                        value=pn.get("location"),
                    ),
                    dmc.TextInput(
                        label="年需求量",
                        description="(Forecast(pcs/Year))",
                        size="xs",
                        id=id("forecast"),
                        value=pn.get("forecast"),
                    ),
                    dmc.NumberInput(
                        label="單機用量",
                        description="(pcs/set)",
                        size="xs",
                        id=id("pcs"),
                        value=pn.get("pcs"),
                    ),
                    dmc.Select(
                        label="代工还是原设计",
                        description="(OEM or ODM)",
                        placeholder="Select one",
                        id=id("oem_odm"),
                        data=[
                            {"value": "oem", "label": "OEM"},
                            {"value": "odm", "label": "ODM"},
                        ],
                        size="xs",
                        value=pn.get("oem_odm"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ]
    )


def form_part3(pn):
    cats = ce_cats()
    return html.Div(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="位置号",
                        size="xs",
                        id=id("designno"),
                        value=pn.get("designno"),
                    ),
                    dmc.Select(
                        label="材料类别1",
                        placeholder="Select one",
                        size="xs",
                        id=id("cat1"),
                        value=pn.get("cat1"),
                        data=cats["cat1"].unique(),
                    ),
                    dmc.Select(
                        label="材料类别2",
                        placeholder="Select one",
                        size="xs",
                        id=id("cat2"),
                        value=pn.get("cat2"),
                        data=cats["cat2"].unique(),
                    ),
                    dmc.Select(
                        label="材料类别3",
                        placeholder="Select one",
                        size="xs",
                        id=id("cat3"),
                        value=pn.get("cat3"),
                        data=cats["cat3"].unique(),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ]
    )


def form_part4(pn):
    return dmc.Stack(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="厂商",
                        size="xs",
                        id=id("mfgname"),
                        value=pn.get("mfgname"),
                    ),
                    dmc.TextInput(
                        label="厂商料号",
                        size="xs",
                        id=id("mfgpn"),
                        value=pn.get("mfgpn"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
            dmc.Group(
                [
                    dmc.TextInput(
                        label="使用位置",
                        size="xs",
                        id=id("use_position"),
                        value=pn.get("use_position"),
                    ),
                    dmc.Select(
                        label="是否有做过该材料的测试",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        id=id("tested"),
                        value=pn.get("tested"),
                    ),
                    dmc.Select(
                        label="是否有做过其他材料的benchmarking",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        id=id("benchmarking"),
                        value=pn.get("benchmarking"),
                    ),
                    dmc.Select(
                        label="是否是客戶指定",
                        placeholder="Select one",
                        size="xs",
                        data=["是", "否"],
                        id=id("customer_specified"),
                        value=pn.get("customer_specified"),
                    ),
                ],
                spacing=0,
                align="end",
                grow=True,
            ),
        ],
        spacing=5,
    )


def other_info(pn):
    return html.Div(
        dmc.Group(
            [
                dmc.TextInput(
                    label="应用电路及功能",
                    size="xs",
                    id=id("application_circuit"),
                    value=pn.get("application_circuit"),
                ),
                dmc.TextInput(
                    label="机种输入电压/电流",
                    size="xs",
                    id=id("input_vi"),
                    value=pn.get("input_vi"),
                ),
                dmc.TextInput(
                    label="机种输出电压/电流",
                    size="xs",
                    id=id("output_vi"),
                    value=pn.get("output_vi"),
                ),
                dmc.TextInput(
                    label="零件的电压/电流",
                    size="xs",
                    id=id("material_vi"),
                    value=pn.get("material_vi"),
                ),
            ],
            spacing=0,
            grow=True,
        ),
        style={"display": "none"},
        id=id("other_info"),
    )


contact_req = """
** 联络单填写要求:**  
**內容:**詳細說明申請原因（原則上未量產材料不能申請臨時料號）  
**簽核人員:**RD leader，RD Manager， 機種IE，機種QA leader，材料CPC，Safety(僅限安規器件)，BU head，CE  
**受文者:**需要被通知到的其他人員  
**特殊情況說明:**如緊急入料工廠端無法或來不及做材料承認,RD需通知VQA進行免承認維護及免確認MSL level,避免造成入料問題
"""

contact_list = html.Div(
    [
        dcc.Markdown(contact_req),
        html.A(
            "联络单填写地址",
            href="https://dgoa.deltaww.com/SmartFormV2/index.html?Type=APPDEV&SubType=2&t=1693375414954",
            target="_blank",
        ),
    ]
)


def layout(user, tid: int = None, **kwargs):
    if not tid:
        raise PreventUpdate

    nt_name = user.get("nt_name").title()
    role_group = user.get("role_group")
    dept_id = user.get("dept_id")
    users = get_ssp_user()

    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True

    auto_attachment_style = {"display": "none"}
    approve_attachment_style = {"display": "none"}
    if dept_id in [4, 5, 22]:
        auto_attachment_style = {"display": "block"}
    if dept_id in [1, 23]:
        approve_attachment_style = {"display": "block"}

    task = {}
    pn = {}

    if tid:
        task = db.find_one("ce.task", {"id": tid})
        pn = db.find_one("ce.pn_temp", {"task_id": task.get("id")})

    status = task.get("status")
    if status == "reject":
        submit_child = "重新提交"
    else:
        submit_child = "提交申请"

    layout = dmc.Container(
        dmc.Stack(
            [
                # fuc.FefferyReload(id=id("reload"), delay=500),
                # fuc.FefferyExecuteJs(id=id("js")),
                fuc.ListenUnload(id=id("unload")),
                dmc.Grid(
                    [
                        dmc.Col(
                            dmc.Select(
                                withAsterisk=True,
                                size="xs",
                                id=id("applicant"),
                                placeholder="申请人(Applicant)",
                                value=nt_name,
                                data=users["nt_name"].tolist(),
                                disabled=applicant_disabled,
                                searchable=True,
                            ),
                            span=2,
                        ),
                        dmc.Col(span=2),
                        dmc.Col(
                            dmc.Text("临时料号申请单", weight=700, id=id("title")),
                            span=4,
                            style={"text-align": "center"},
                        ),
                        dmc.Col(
                            dmc.Group(
                                [
                                    dmc.Button(
                                        "暂存填写内容",
                                        size="xs",
                                        id=id("save"),
                                        variant="light",
                                    )
                                ],
                                position="right",
                            ),
                            span=4,
                            style={"text-align": "right"},
                        ),
                    ],
                    justify="center",
                ),
                dmc.Divider(),
                dmc.Paper(
                    [
                        dmc.Text("机种信息填写", size="xs", weight=700),
                        dmc.Text(
                            "model information information", color="dimmed", size="xs"
                        ),
                        dmc.Divider(),
                        form_part2(pn),
                    ],
                    withBorder=True,
                    shadow="xs",
                    p="xs",
                    style={"background-color": "#f1f5f8"},
                ),
                dmc.Paper(
                    dmc.Stack(
                        [
                            dmc.Text("料号申请信息", size="xs", weight=700),
                            dmc.Text(
                                "deltapn application information",
                                color="dimmed",
                                size="xs",
                            ),
                            dmc.Divider(),
                            form_part3(pn),
                            form_part4(pn),
                            dmc.Group(
                                [
                                    dmc.Select(
                                        label="選用此料的原因",  # 勾选不同，生成TextInput不同。主料和替代料
                                        placeholder="Select one",
                                        size="xs",
                                        data=[
                                            "main source",
                                            "2nd source",
                                            "single source",
                                        ],
                                        id=id("reason"),
                                        value=pn.get("reason"),
                                    ),
                                    dmc.TextInput(
                                        label="主料为哪颗",
                                        size="xs",
                                        id=id("main_source"),
                                        style={"display": "none"},
                                        value=pn.get("main_source"),
                                    ),
                                    dmc.TextInput(
                                        label="替代料为哪颗",
                                        size="xs",
                                        id=id("second_source"),
                                        style={"display": "none"},
                                        value=pn.get("second_source"),
                                    ),
                                ],
                                grow=True,
                                spacing=0,
                            ),
                            other_info(pn),
                            fac.AntdDraggerUpload(
                                apiUrl="/upload/",
                                text="规格书附件",
                                id=id("attachment"),
                                uploadId=f"pn_temp_attachment_{tid}",
                                defaultFileList=[
                                    {
                                        "name": j.name,
                                        "url": f"/upload/pn_temp_attachment_{tid}/{j.name}",
                                        "status": "done",
                                    }
                                    for j in (
                                        UPLOAD_FOLDER_ROOT / f"pn_temp_attachment_{tid}"
                                    ).glob("*")
                                ],
                            ),
                            fac.AntdDraggerUpload(
                                apiUrl="/upload/",
                                text="车规宣告函",
                                id=id("auto_attachment"),
                                style=auto_attachment_style,
                                uploadId=f"pn_temp_auto_attachment_{tid}",
                                defaultFileList=[
                                    {
                                        "name": j.name,
                                        "url": f"/upload/pn_temp_auto_attachment_{tid}/{j.name}",
                                        "status": "done",
                                    }
                                    for j in (
                                        UPLOAD_FOLDER_ROOT
                                        / f"pn_temp_auto_attachment_{tid}"
                                    ).glob("*")
                                ],
                            ),
                            fac.AntdDraggerUpload(
                                apiUrl="/upload/",
                                text="Approve的邮件/文件",
                                id=id("approve_attachment"),
                                style=approve_attachment_style,
                                uploadId=f"pn_temp_approve_attachment_{tid}",
                                defaultFileList=[
                                    {
                                        "name": j.name,
                                        "url": f"/upload/pn_temp_approve_attachment_{tid}/{j.name}",
                                        "status": "done",
                                    }
                                    for j in (
                                        UPLOAD_FOLDER_ROOT
                                        / f"pn_temp_approve_attachment_{tid}"
                                    ).glob("*")
                                ],
                            ),
                            fac.AntdDraggerUpload(
                                apiUrl="/upload/",
                                text="联络单附件",
                                id=id("contact_attachment"),
                                uploadId=f"pn_temp_contact_attachment_{tid}",
                                defaultFileList=[
                                    {
                                        "name": j.name,
                                        "url": f"/upload/pn_temp_contact_attachment_{tid}/{j.name}",
                                        "status": "done",
                                    }
                                    for j in (
                                        UPLOAD_FOLDER_ROOT
                                        / f"pn_temp_contact_attachment_{tid}"
                                    ).glob("*")
                                ],
                            ),
                            contact_list,
                        ],
                        spacing=5,
                    ),
                    withBorder=True,
                    shadow="xs",
                    p="xs",
                    style={"background-color": "#f1f5f8"},
                ),
                dmc.Group(
                    [
                        dmc.Button(submit_child, id=id("submit")),
                        dmc.Button("取消申请", id=id("cancel"), color="red"),
                    ],
                    position="apart",
                ),
                dmc.Divider(),
                # dcc.Location(id=id("url"), refresh=False),
            ]
        )
    )
    return layout


# *------------------callback------------------*

fields = [
    {"en": "model", "cn": "机种名", "req": 1},
    {"en": "watt", "cn": "机种功率", "req": 1},
    {"en": "application", "cn": "机种应用領域", "req": 1},
    {"en": "customer", "cn": "客户名称", "req": 1},
    {"en": "stage", "cn": "项目阶段", "req": 1},
    {"en": "mp", "cn": "量产计划", "req": 1},
    {"en": "location", "cn": "生产地点", "req": 1},
    {"en": "forecast", "cn": "年需求量", "req": 1},
    {"en": "pcs", "cn": "單機用量", "req": 1},
    {"en": "oem_odm", "cn": "代工还是原设计", "req": 1},
    {"en": "designno", "cn": "位置号", "req": 1},
    {"en": "cat1", "cn": "材料类型1", "req": 1},
    {"en": "cat2", "cn": "材料类型2", "req": 1},
    {"en": "cat3", "cn": "材料类型3", "req": 1},
    {"en": "mfgname", "cn": "厂商", "req": 1},
    {"en": "mfgpn", "cn": "厂商料号", "req": 1},
    {"en": "use_position", "cn": "使用位置", "req": 1},
    {"en": "reason", "cn": "選用此料的原因", "req": 1},
    {"en": "tested", "cn": "是否有做过该材料的测试", "req": 1},
    {"en": "benchmarking", "cn": "是否有做过其他材料的benchmarking", "req": 1},
    {"en": "customer_specified", "cn": "是否是客戶指定", "req": 1},
    {"en": "main_source", "cn": "主料为哪颗", "req": 0},
    {"en": "second_source", "cn": "替代料为哪颗", "req": 0},
    {"en": "application_circuit", "cn": "应用电路及功能", "req": 0},
    {"en": "input_vi", "cn": "机种输入电压/电流", "req": 0},
    {"en": "output_vi", "cn": "机种输出电压/电流", "req": 0},
    {"en": "material_vi", "cn": "零件的电压/电流", "req": 0},
]


@callback(
    Output(id("cat1"), "value"),
    Output(id("cat2"), "value"),
    Output(id("cat3"), "value"),
    Output(id("cat2"), "data"),
    Output(id("cat3"), "data"),
    Input(id("cat1"), "value"),
    Input(id("cat2"), "value"),
    Input(id("cat3"), "value"),
)
def cats_value(cat1, cat2, cat3):
    """材料类别下拉框的回调"""
    tid = ctx.triggered_id
    df = ce_cats()
    c1 = df["cat1"] == cat1
    c2 = df["cat2"] == cat2
    cat2_data = df.loc[c1]["cat2"].unique()
    cat3_data = df.loc[c1 & c2]["cat3"].unique()
    if "cat1" in tid:
        return cat1, None, None, cat2_data, cat3_data
    elif "cat2" in tid:
        return no_update, cat2, None, no_update, cat3_data
    elif "cat3" in tid:
        return no_update, no_update, cat3, no_update, no_update
    raise PreventUpdate


@callback(
    Output(id("main_source"), "style"),
    Output(id("second_source"), "style"),
    Input(id("reason"), "value"),
    prevent_initial_call=False,
)
def source_display(value):
    if value == "main source":
        return {"display": "none"}, {"display": "block"}
    elif value == "2nd source":
        return {"display": "block"}, {"display": "none"}
    else:
        return {"display": "none"}, {"display": "none"}


@callback(
    Output(id("other_info"), "style"),
    Input(id("cat1"), "value"),
    prevent_initial_call=False,
)
def other_info_display(value):
    if value in ("Active", "Passive"):
        return {"display": "block"}
    else:
        return {"display": "none"}


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Output(id("save"), "disabled"),
    Output(id("cancel"), "disabled"),
    Input(id("submit"), "n_clicks"),
    Input(id("save"), "n_clicks"),
    State(id("applicant"), "value"),
    State("url", "search"),
    State(id("attachment"), "uploadId"),
    State(id("auto_attachment"), "uploadId"),
    State(id("approve_attachment"), "uploadId"),
    State(id("contact_attachment"), "uploadId"),
    State(id("model"), "value"),
    State(id("watt"), "value"),
    State(id("application"), "value"),
    State(id("customer"), "value"),
    State(id("stage"), "value"),
    State(id("mp"), "value"),
    State(id("location"), "value"),
    State(id("forecast"), "value"),
    State(id("pcs"), "value"),
    State(id("oem_odm"), "value"),
    State(id("designno"), "value"),
    State(id("cat1"), "value"),
    State(id("cat2"), "value"),
    State(id("cat3"), "value"),
    State(id("mfgname"), "value"),
    State(id("mfgpn"), "value"),
    State(id("use_position"), "value"),
    State(id("reason"), "value"),
    State(id("tested"), "value"),
    State(id("benchmarking"), "value"),
    State(id("customer_specified"), "value"),
    State(id("main_source"), "value"),
    State(id("second_source"), "value"),
    State(id("application_circuit"), "value"),
    State(id("input_vi"), "value"),
    State(id("output_vi"), "value"),
    State(id("material_vi"), "value"),
)
def rd_submit(
    submit,
    save,
    applicant,
    url,
    attach0,
    attach1,
    attach2,
    attach3,
    *inputs,
):
    if not submit and not save:
        raise PreventUpdate

    user = db.find_one("ssp.user", {"nt_name": applicant})
    dept_id = user.get("dept_id")
    dept = user.get("dept")
    cid = ctx.triggered_id

    if "submit" in cid:
        if not (UPLOAD_FOLDER_ROOT / attach0).exists():
            return notice("请上传规格书", "warning"), False, False, False

        if dept_id in [4, 5, 22]:
            if not (UPLOAD_FOLDER_ROOT / attach1).exists():
                return notice("请上传车规宣告函", "warning"), False, False, False

        if dept_id in [1, 23]:
            if not (UPLOAD_FOLDER_ROOT / attach2).exists():
                return (
                    notice("请上传Approve的邮件/文件", "warning"),
                    False,
                    False,
                    False,
                )

        if not (UPLOAD_FOLDER_ROOT / attach3).exists():
            return notice("请上传联络单附件", "warning"), False, False, False

        inputs = inputs
        req = ",".join(
            j["cn"] for i, j in enumerate(fields) if not inputs[i] and j["req"]
        )
        if req:
            return notice(f"以下栏位必填: {req}", "warning"), False

    data = {j["en"]: inputs[i] for i, j in enumerate(fields) if inputs[i]}

    url = parse_search(url)
    tid = url.get("tid")

    cc = ["Ying.Gao"]

    ce = get_ce_owner(data)

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if "submit" in cid:
                sql = (
                    "update ce.task set applicant=%s,ce=%s, status=%s,cat1=%s, cat2=%s, \
                    cat3=%s, mfgpn=%s, mfgname=%s,cc=%s,start_date=%s where id=%s"
                )
                params = [
                    applicant,
                    ce,
                    "open",
                    data.get("cat1"),
                    data.get("cat2"),
                    data.get("cat3"),
                    data.get("mfgpn"),
                    data.get("mfgname"),
                    ",".join(cc),
                    datetime.now(),
                    tid,
                ]
                cu.execute(sql, params)
            else:
                sql = "update ce.task set gmt_update=now() where id=%s"
                params = [tid]
                cu.execute(sql, params)

            data = (
                data
                | {"task_id": tid}
                | {"attachment": attach0}
                | {"auto_attachment": attach1}
                | {"approve_attachment": attach2}
                | {"contact_attachment": attach3}
            )
            sql = f"update ce.pn_temp set {','.join(f'{k}=%s' for k in data.keys())} where task_id=%s"
            params = tuple(data.values()) + (tid,)
            cu.execute(sql, params)
        conn.commit()

    # *---------邮件通知开始-----------*
    if "submit" in cid:
        sub_type = "临时料号"
        to = [applicant, ce] + cc
        to = ";".join(f"{i}@deltaww.com" for i in to)
        subject = f"【料号申请】{sub_type}"
        df = pd.DataFrame(
            [
                {
                    "type": sub_type,
                    "dept": dept,
                    "applicant": applicant,
                    "deltapn": "",
                    "mfgname": data.get("mfgname"),
                    "mfgpn": data.get("mfgpn"),
                    "start_date": datetime.now().date(),
                    "ce": ce,
                }
            ]
        )
        columns = [
            "type",
            "dept",
            "applicant",
            "deltapn",
            "mfgname",
            "mfgpn",
            "start_date",
            "ce",
        ]
        df = df.reindex(columns=columns)
        content = df_to_html(
            df,
            f"您的{sub_type}已提交,{ce}将会处理,请知悉！",
            href="http://sup.deltaww.com/info/rd?page=ongoing",
            link_text="工作进展可至个人中心查询",
        )
        bg_mail(to, subject, content)
    # *---------邮件通知结束-----------*

    return notice(), True, True, True


@callback(
    Output("global-notice", "children"),
    Output(id("save"), "disabled"),
    Output(id("submit"), "disabled"),
    Output(id("cancel"), "disabled"),
    Input(id("cancel"), "n_clicks"),
    State("url", "search"),
)
def cancel_task(n_clicks, url):
    if not n_clicks:
        raise PreventUpdate
    url = parse_search(url)
    tid = url.get("tid")

    db.update("ce.task", {"status": "cancel", "id": tid})
    return notice("取消成功"), True, True, True


# @callback(
#     Output(id("url"), "search"),
#     Input(id("url"), "search"),
#     State("user", "data"),
# )
# def redirect(search, user):
#     if search:
#         raise PreventUpdate
#     nt_name = user.get("nt_name")
#     dept = user.get("dept")
#     dept_id = user.get("dept_id")
#     cond = {
#         "applicant": nt_name,
#         "status": "temp",
#         "type": "料号申请",
#         "sub_type": "临时料号",
#         "dept_id": dept_id,
#         "dept": dept,
#     }
#     task = db.find_one("ce.task", cond)
#     if task:
#         task_id = task.get("id")
#     else:
#         task_id = db.insert("ce.task", cond)
#         db.insert("ce.pn_temp", {"task_id": task_id})
#     return f"?tid={task_id}"


# @callback(
#     Input(id("unload"), "unloaded"),
#     State(id("url"), "search"),
# )
# def unload_page(unloaded, url):
#     if not unloaded:
#         raise PreventUpdate

#     url = parse_search(url)
#     id = url.get("tid")
#     task = db.find_one("ce.task", {"id": id})
#     status = task.get("status")

#     if status == "temp":
#         if not task.get("gmt_update"):
#             db.delete("ce.task", {"id": id})
