import dash_bootstrap_components as dbc
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash, html, no_update
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator
from db.ssp_ext import Mag, Mag_stock_in, Mag_stock_list, Mag_stock_no, Mag_stock_out
from pony.orm import commit, db_session, desc, select
from tasks import bg_label_print
import feffery_antd_components as fac
import dash_mantine_components as dmc
from common import read_sql

dash.register_page(__name__, title="磁组")
ns = Namespace("myNamespace", "tabulator")

downloadButtonType = {
    #  btn-outline-primary position-absolute bottom-0 start-5 my-3
    "css": "btn btn-sm btn-success",
    "text": "下载",
    "type": "xlsx",
}

clearFilterButtonType = {
    # position-absolute  bottom-0  my-3 right2
    "css": "btn btn-sm btn-warning",
    "text": "重置筛选",
}

tab1_content = html.Div(
    [
        html.Br(),
        DashTabulator(
            id="mag-stock-in-table",
            theme="tabulator_site",
            options={"selectable": 1, "index": "deltapn"},
            data=[{"id": 0}],
        ),
        dmc.Group(
            [
                dbc.Button("提交", id="mag-submit"),
                dbc.Button(className="fa fa-plus", id="mag-add-row", color="warning"),
            ],
            position="apart",
        ),
        dbc.Alert(id="mag-alert", is_open=False, duration=5000),
    ]
)

tab2_content = html.Div(
    [
        html.Br(),
        dbc.Button("提交修改", id="submit-action", size="sm", class_name="float-start"),
        DashTabulator(
            id="mag-query-table",
            theme="tabulator_site",
            downloadButtonType=downloadButtonType,
            clearFilterButtonType=clearFilterButtonType,
            options={"selectable": 1, "index": "deltapn", "height": "400px"},
            columns=[
                {
                    "title": "操作",
                    "field": "action",
                    "editor": "select",
                    "editorParams": ["更新", "删除"],
                },
                {
                    "title": "料号",
                    "field": "deltapn",
                    "editor": "input",
                    "headerFilter": "input",
                },
                {
                    "title": "描述",
                    "field": "des",
                    "editor": "input",
                    "headerFilter": "input",
                },
                {
                    "title": "厂商",
                    "field": "mfgname",
                    "editor": "input",
                    "headerFilter": "input",
                },
                {
                    "title": "厂商料号",
                    "field": "mfgpn",
                    "editor": "input",
                    "headerFilter": "input",
                },
                {
                    "title": "库位号",
                    "field": "stock_no",
                    "editor": "input",
                    "headerFilter": "input",
                },
                {
                    "title": "数量",
                    "field": "qty",
                    "editor": "input",
                    "headerFilter": "input",
                },
            ],
        ),
        # dbc.Button("提交", id="mag-submit",block=True),
        # dbc.Alert(id='mag-alert',is_open=False,duration=5000),
    ]
)

layout = dbc.Container(
    [
        dbc.Tabs(
            [
                dbc.Tab(tab1_content, label="入库作业", tab_id="tab-1"),
                dbc.Tab(tab2_content, label="库存信息", tab_id="tab-2"),
                dbc.Tab(id="tab3-content", label="出库记录", tab_id="tab-3"),
                dbc.Tab(id="tab4-content", label="入库记录", tab_id="tab-4"),
                dbc.Tab(id="tab5-content", label="项目记录", tab_id="tab-5"),
            ],
            id="tabs",
            active_tab="tab-1",
            style={"color": "#16a085"},
        ),
        html.Div(id="notice"),
    ],
    fluid=True,
    # className="ml-3 pr-5",
)


# * ---------回调函数----------------
@callback(
    [
        Output("mag-stock-in-table", "data"),
    ],
    [
        Input("mag-add-row", "n_clicks"),
    ],
    [
        State("mag-stock-in-table", "data"),
    ],
)
def pcb_table_add_row(n, data):
    """mag table插入行"""
    if n is None:
        raise PreventUpdate

    max_id = max(i["id"] for i in data)
    data.append({"id": max_id + 1})
    return data


@callback(
    Output("mag-stock-in-table", "data"),
    Input("mag-stock-in-table", "cellEdited"),  # cellEdited
    State("mag-stock-in-table", "data"),
)
@db_session
def mag_table_cell_edited(ce, data):
    if not (ce) or ce["column"] != "deltapn" or not (ce["value"]):
        raise PreventUpdate

    deltapn = ce["value"]
    row_id = ce["row"]["id"]
    stock_no = select(
        i for i in Mag_stock_no if i.mag_stock_list.deltapn == deltapn
    ).first()

    if stock_no:
        for i, j in enumerate(data):
            if j["id"] == row_id:
                j.update(
                    {
                        "stock_no": stock_no.stock_no,
                        "stock_id": stock_no.mag_stock_list.id,
                    }
                )
                data[i] = j
                return data
    else:
        for i, j in enumerate(data):
            if j["id"] == row_id:
                j.update({"stock_no": None, "stock_id": None})
                data[i] = j
                return data


@callback(
    [
        Output("mag-alert", "children"),
        Output("mag-alert", "is_open"),
        Output("mag-alert", "color"),
    ],
    [
        Input("mag-submit", "n_clicks"),
    ],
    [
        State("mag-stock-in-table", "data"),
        State("user", "data"),
    ],
)
@db_session
def mag_stock_in_submit(n, data, user):
    """提交入库"""
    df = pd.DataFrame(data)
    df = df.reindex(columns=["deltapn", "qty", "stock_no", "stock_id", "version"])
    df1 = df[["deltapn", "qty", "stock_no", "version"]].fillna("")
    if (df1 == "").any().any():
        return "料号,数量,库位号,版本号不能为空", True, "danger"

    nt_name = user.get("nt_name")
    dfx = df.loc[df["stock_id"].notna()]
    dfy = df.loc[df["stock_id"].isna()]

    if not dfx.empty:
        dfx["stock_id"] = dfx["stock_id"].astype(int)
        for i in dfx.itertuples():
            Mag_stock_in(
                mag_stock_list=i.stock_id, qty=i.qty, owner=nt_name, version=i.version
            )

    if not dfy.empty:
        for i in dfy.itertuples():
            sl = Mag_stock_list(deltapn=i.deltapn, qty=0)
            commit()
            Mag_stock_in(
                mag_stock_list=sl.id, qty=i.qty, owner=nt_name, version=i.version
            )
            Mag_stock_no(mag_stock_list=sl.id, stock_no=i.stock_no)

    df["checkcode"] = df["deltapn"]
    df["new_stock_no"] = df["stock_no"]
    df["limituse"] = df["version"]
    df["label_template"] = "stock_in"
    df["des"] = ""
    df["mfgname"] = ""
    df["mfgpn"] = ""
    df["type"] = "mag"
    df["id"] = df["deltapn"] + "{" + df["qty"].astype(str) + "{Pur"
    bg_label_print(df.to_json(orient="records"))

    return "入库提交成功", True, "success"


@callback(
    Output("mag-stock-in-table", "data"),
    Input("mag-alert", "color"),
)
@db_session
def clear_table_data(color):
    """提交入库"""
    if color == "success":
        return [{"id": 0}]
    else:
        raise PreventUpdate


@callback(
    Output("tabs", "active_tab"),
    Output("notice", "children"),
    Input("submit-action", "n_clicks"),
    State("mag-query-table", "data"),
)
@db_session
def submit_action(n, data):
    """提交操作"""
    if not n:
        raise PreventUpdate
    df = pd.DataFrame(data)
    if "action" not in df.columns:
        raise PreventUpdate

    dfu = df.loc[df["action"] == "更新"]
    dfd = df.loc[df["action"] == "删除"]

    success = fac.AntdNotification(
        message="提交成功",
        type="success",
        placement="bottomRight",
    )

    error = fac.AntdNotification(
        message="库位号不在系统中",
        type="error",
        placement="bottomRight",
    )
    if not dfu.empty:
        for i in dfu.itertuples():
            msn = Mag_stock_no.get(mag_stock_list=i.id)
            if msn.stock_no != i.stock_no:
                x = select(x for x in Mag_stock_no if x.stock_no == i.stock_no)[:]
                if not x:
                    return no_update, error
                else:
                    msn.stock_no = i.stock_no

    if not dfd.empty:
        for i in dfd.itertuples():
            Mag_stock_list.get(id=i.id).delete()

    return "tab-2", success


@callback(
    [
        Output("mag-query-table", "data"),
        Output("mag-stock-in-table", "columns"),
        Output("tab3-content", "children"),
        Output("tab4-content", "children"),
        Output("tab5-content", "children"),
    ],
    Input("tabs", "active_tab"),
    prevent_initial_call=False,
)
@db_session
def active_tab(at):
    """tab切换"""
    if at == "tab-1":  # * 初始化箱号库位号菜单
        stock_no_list = sorted({i.stock_no for i in Mag_stock_no.select()[:]})
        columns = [
            {"title": "料号", "field": "deltapn", "editor": "input"},
            {
                "title": "入库数量",
                "field": "qty",
                "editor": "input",
                "validator": ["min:0", "max:50000", "integer"],
            },
            {
                "title": "库位号",
                "field": "stock_no",
                "editor": "autocomplete",
                "editorParams": {"values": stock_no_list, "showListOnEmpty": True},
            },
            {"title": "版本", "field": "version", "editor": "input"},
            {"title": "stock_id", "field": "stock_id", "visible": False},
            {"title": "id", "field": "id", "visible": False},
        ]
        return no_update, columns, no_update, no_update, no_update

    elif at == "tab-2":
        mag = Mag.select()[:]
        data = [i.to_dict() for i in mag]
        return data, no_update, no_update, no_update, no_update

    elif at == "tab-3":
        mag = Mag_stock_out.select().order_by(desc(Mag_stock_out.id))[:]
        if mag:
            data = [
                {
                    "owner": i.owner,
                    "date": i.gmt_create,
                    "deltapn": i.mag_stock_list.deltapn,
                    "qty": i.qty,
                }
                for i in mag
            ]
        else:
            data = [{"owner": "", "date": "", "deltapn": "", "qty": ""}]
        columns = [{"title": i, "field": i, "headerFilter": "input"} for i in data[0]]
        for i in columns:
            if i["field"] == "date":
                i["headerFilter"] = ns("dateEditor")
                i["headerFilterFunc"] = ">="
                i["formatter"] = "datetime"
                i["formatterParams"] = {"outputFormat": "YYYY/MM/DD"}
        tab3 = DashTabulator(
            theme="tabulator_site",
            downloadButtonType=downloadButtonType,
            clearFilterButtonType=clearFilterButtonType,
            data=data,
            columns=columns,
            options={"selectable": 1, "height": "400px"},
        )
        tab3 = html.Div([html.Br(), tab3])
        return no_update, no_update, tab3, no_update, no_update

    elif at == "tab-4":
        mag = Mag_stock_in.select().order_by(desc(Mag_stock_in.id))[:]
        if mag:
            data = [
                {
                    "owner": i.owner,
                    "date": i.gmt_create,
                    "deltapn": i.mag_stock_list.deltapn,
                    "qty": i.qty,
                    "version": i.version,
                }
                for i in mag
            ]
        else:
            data = [{"owner": "", "date": "", "deltapn": "", "qty": "", "version": ""}]

        columns = [
            {
                "title": i,
                "field": i,
                "headerFilter": "input",
            }
            for i in data[0]
        ]
        for i in columns:
            if i["field"] == "date":
                i["headerFilter"] = ns("dateEditor")
                i["headerFilterFunc"] = ">="
                i["formatter"] = "datetime"
                i["formatterParams"] = {"outputFormat": "YYYY/MM/DD"}
        tab4 = DashTabulator(
            theme="tabulator_site",
            downloadButtonType=downloadButtonType,
            clearFilterButtonType=clearFilterButtonType,
            data=data,
            columns=columns,
            options={"selectable": 1, "height": "400px"},
        )
        tab4 = html.Div([html.Br(), tab4])
        return no_update, no_update, no_update, tab4, no_update

    elif at == "tab-5":
        sql = "select prtno,stockoutdate as date,deltapn,qty,dept,lable as label \
        from ssp.stockout where mag_stock_list is not null order by date desc"
        df = read_sql(sql)

        columns = [
            {
                "title": i,
                "field": i,
                "headerFilter": "input",
            }
            for i in df.columns
        ]
        for i in columns:
            if i["field"] == "date":
                i["headerFilter"] = ns("dateEditor")
                i["headerFilterFunc"] = ">="
                i["formatter"] = "datetime"
                i["formatterParams"] = {"outputFormat": "YYYY/MM/DD"}

        tab5 = DashTabulator(
            theme="tabulator_site",
            downloadButtonType=downloadButtonType,
            clearFilterButtonType=clearFilterButtonType,
            data=df.to_dict(orient="records"),
            columns=columns,
            options={"selectable": 1, "height": "400px"},
        )
        tab5 = html.Div([html.Br(), tab5])

        return no_update, no_update, no_update, no_update, tab5
    else:
        raise PreventUpdate
