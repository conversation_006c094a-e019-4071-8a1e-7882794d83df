import time
from datetime import datetime, timedelta
from pathlib import Path

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import dash_tabulator as dt
import dash_uploader as du
import feffery_antd_components.alias as fac
import feffery_utils_components.alias as fuc
import numpy as np
import pandas as pd
from chinese_calendar import get_holidays, is_holiday, get_workdays
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    MATCH,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    callback_context,
    clientside_callback,
    dash,
    dash_table,
    dcc,
    html,
    no_update,
)
from dash_extensions.javascript import Namespace
from dash_iconify import DashIconify
from pony.orm import db_session

from common import df_to_html, id_factory, read_sql, df_update, get_nt_name
from components import file_browser, notice
from config import SSP_DIR, UPLOAD_FOLDER_ROOT, pool
from db.ssp import User
from db.ssp_spec import Due_day, Duty, Ecn_sm, Modification, Task, db
from tasks import bg_mail
import dash_ag_grid as dag

id = id_factory(__name__)
pd.set_option("display.max_columns", None)

dash.register_page(__name__, title="规格")
ns = Namespace("myNamespace", "tabulator")
col_dict = {
    "doc_type": "文件类型",
    "model": "机种名称",
    "status": "状态",
    "input_date": "创建日期",
    "request_date": "需求日期",
    "remark": "备注",
}

status_dict = {
    "accepted": "规格已接收",
    "submitted_ee": "电子审核",
    "submitted_me": "机构审核",
    "modify_ee": "文件更新",
    "modify_me": "文件更新",
    "open": "尚未受理",
    "close": "流程结束",
    "cancel": "任务取消",
}
# *--------------导航栏-----------------------------------------------
sidebar = fac.Menu(
    menuItems=[
        {
            "component": "Item",
            "props": {
                "key": "home",
                "title": "规格",
                "icon": "fc-home",
                "href": "/spec",
            },
        },
        # {"component": "Divider"},
        {
            "component": "Item",
            "props": {
                "key": "task",
                "title": "新任务",
                "icon": "fc-idea",
                "href": "/spec?page=task",
            },
        },
        {
            "component": "Item",
            "props": {
                "key": "processing",
                "title": "在途",
                "icon": "fc-process",
                "href": "/spec?page=processing",
            },
        },
        {
            "component": "SubMenu",
            "props": {"key": "add", "title": "新增", "icon": "fc-plus"},
            "children": [
                {
                    "component": "Item",
                    "props": {
                        "key": "add-spec",
                        "title": "规格",
                        "href": "/spec?page=add-spec",
                    },
                },
                {
                    "component": "Item",
                    "props": {
                        "key": "add-cost",
                        "title": "估价",
                        "href": "/spec?page=add-cost",
                    },
                },
            ],
        },
        {
            "component": "SubMenu",
            "props": {
                "key": "admin",
                "title": "管理",
                "icon": "fc-services",
            },
            "children": [
                {
                    "component": "Item",
                    "props": {
                        "key": "add-upload",
                        "title": "规格文件",
                        "href": "/spec?page=add-upload",
                    },
                },
                {
                    "component": "Item",
                    "props": {
                        "key": "add-index",
                        "title": "时效统计",
                        "href": "/spec?page=add-index",
                    },
                },
                {
                    "component": "Item",
                    "props": {
                        "key": "due-day",
                        "title": "绩效标准",
                        "href": "/spec?page=due-day",
                    },
                },
                {
                    "component": "Item",
                    "props": {
                        "key": "duty",
                        "title": "人员职责",
                        "href": "/spec?page=duty",
                    },
                },
            ],
        },
        {
            "component": "Item",
            "props": {
                "key": "query",
                "title": "查询",
                "icon": "fc-search",
                "href": "/spec?page=query",
            },
        },
        {
            "component": "Item",
            "props": {
                "key": "doc",
                "title": "文档",
                "icon": "fc-document",
                "href": "/spec?page=doc",
            },
        },
    ],
    id=id("sidebar"),
    mode="inline",
    inlineIndent=15,
    style={
        "height": "88vh",
        "backgroundColor": "rgba(240,240,240,1)",
        "font-size": "15px",
    },
    menuItemKeyToTitle={
        "home": fac.Text(
            "规格",
            strong=True,
            style={"color": "rgb(0, 159, 232)", "font-size": "16px"},
        ),
    },
)

sp_nav = dbc.Nav(
    [
        dbc.DropdownMenu(
            [
                dbc.DropdownMenuItem("spec", href="?page=add-spec"),
                dbc.DropdownMenuItem("cost", href="?page=add-cost"),
                dbc.DropdownMenuItem("upload", href="?page=add-upload"),
            ],
            label="新增",
            nav=True,
            in_navbar=True,
        ),
        dbc.NavItem([dbc.NavLink("新任务", href="?page=task")]),
        dbc.DropdownMenu(
            [
                dbc.DropdownMenuItem("spec", href="?page=process-spec"),
                # dbc.DropdownMenuItem("cost", href="?page=process-cost"),
                dbc.DropdownMenuItem("ssp", href="?page=processing"),
            ],
            label="处理中",
            nav=True,
            in_navbar=True,
        ),
        dbc.NavItem([dbc.NavLink("查询", href="?page=query")]),
        dbc.NavItem([dbc.NavLink("时效", href="?page=index")]),
        dbc.NavItem([dbc.NavLink("文档", href="?page=doc")]),
    ],
    pills=True,
    id="sp-nav",
    className="mx-3",
)
# --------------新增界面-spec-----------------------------------------------

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
    "font-family": "Helvetica",
    "textAlign": "left",
}

page_add_spec = dmc.Stack(
    [
        dash_table.DataTable(
            data=[{}],
            columns=[
                # {"name": "id", "id": "id"},
                {"name": "New_Source", "id": "new_source"},
                {"name": "Status", "id": "status", "presentation": "dropdown"},
                {
                    "name": "Applicant",
                    "id": "owner",
                },
                {"name": "MODEL(PN)", "id": "model"},
                {"name": "DEPT", "id": "dept", "editable": False},
                {"name": "DOC_TYPE", "id": "doc_type", "presentation": "dropdown"},
                {"name": "ECN_NO", "id": "ecn_no"},
                {"name": "Qty", "id": "qty"},
                {"name": "Running_Date", "id": "running_date"},
                {"name": "REMARK", "id": "remark"},
                {"name": "dept_id", "id": "dept_id"},
            ],
            editable=True,
            row_deletable=True,
            is_focused=True,
            tooltip_header={
                "owner": "请注意正确填写NT账号，否则无法显示DEPT栏",
                "running_date": "参照格式：22-2-12",
            },
            hidden_columns=["id", "status", "new_source", "dept_id"],
            id=id("tab1_table"),
            style_header=style_header,
            dropdown={
                "doc_type": {
                    "options": [
                        {"label": "SM", "value": "SM"},
                        {"label": "ECN", "value": "ECN"},
                        {"label": "MODIFY ISSUE", "value": "MODIFY ISSUE"},
                        {"label": "NEW ISSUE", "value": "NEW ISSUE"},
                        {"label": "GRP CHANGE", "value": "GRP CHANGE"},
                        {"label": "DFMS", "value": "DFMS"},
                        {"label": "CM", "value": "CM"},
                        {"label": "DFCS", "value": "DFCS"},
                        {"label": "SBOM", "value": "SBOM"},
                        {"label": "CBOM", "value": "CBOM"},
                    ],
                    "clearable": False,
                },
            },
            style_cell_conditional=[
                {"if": {"column_id": "doc_type"}, "width": "120px"},
            ],
            style_cell={
                "whiteSpace": "normal",
                "height": "auto",
                "textAlign": "left",
                "font-family": "Helvetica",
                "font-size": "10px",
            },
            css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
            # style_table={"height": "400px", "overflowY": "auto"},
        ),
        dmc.Group(
            [
                dbc.Button("AddRow", id="tab1_add_row", color="success", size="sm"),
                dbc.Button("提交", color="primary", id=id("tab1_submit"), size="sm"),
            ],
            position="apart",
        ),
    ],
    spacing=0,
)
# --------------新任务------------------------------------
page_task = dmc.Stack(
    [
        dbc.Button(
            "接收",
            color="success",
            id="sp-task-accept",
            size="sm",
            className="col-1",
        ),
        # fuc.Style(
        #     rawStyle="""
        #     .tabulator-col {
        #         height: 50px !important;
        #         }"""
        # ),
        dbc.Spinner(
            dt.DashTabulator(
                id="sp-task",
                theme="tabulator_site",
                options={
                    "height": "80vh",
                    "layout": "fitData",
                    # "responsiveLayout": "hide",
                },
                columns=[
                    {
                        "formatter": "rowSelection",
                        "titleFormatter": "rowSelection",
                        "hozAlign": "center",
                        "headerSort": False,
                        "headerHozAlign": "center",
                        "width": "1px",
                    },
                    {"title": "状态", "field": "status"},
                    {"title": "文件类别", "field": "doc_type"},
                    {"title": "机种名称", "field": "model"},
                    {"title": "电子", "field": "ee"},
                    {"title": "机构", "field": "me"},
                    {"title": "部门名称", "field": "dept"},
                    {"title": "创建人", "field": "owner"},
                    {"title": "客户", "field": "customer"},
                    {"title": "是否需要替代料", "field": "second_source"},
                    {"title": "其他要求", "field": "remark"},
                    {
                        "title": "创建日期",
                        "field": "input_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {
                        "title": "需求日期",
                        "field": "request_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    # {"title": 'task_id', "field": 'task_id'},
                    # {"title": 'id', "field": 'id'},
                    {"title": "id", "field": "id", "visible": False},
                ],
                cellEditing=True,
            ),
            color="primary",
        ),
    ],
    spacing=5,
)
# --------------新增界面-cost-----------------------------------------------
page_add_cost = dmc.Stack(
    [
        dash_table.DataTable(
            data=[{}],
            columns=[
                {"name": "New_Source", "id": "new_source"},
                {"name": "Status", "id": "status", "presentation": "dropdown"},
                {"name": "Applicant", "id": "owner"},
                {"name": "MODEL(PN)", "id": "model"},
                {"name": "DEPT", "id": "dept"},
                {"name": "DOC_TYPE", "id": "doc_type", "presentation": "dropdown"},
                {"name": "ECN_NO", "id": "ecn_no"},
                {"name": "Qty", "id": "qty"},
                {"name": "Running_Date", "id": "running_date"},
                {"name": "REMARK", "id": "remark"},
            ],
            editable=True,
            row_deletable=True,
            is_focused=True,
            hidden_columns=["status", "new_source"],
            id=id("tab2_table"),
            style_header=style_header,
            dropdown={
                "doc_type": {
                    "options": [
                        {"label": "PART COST", "value": "PART COST"},
                        {"label": "COST QUERY", "value": "COST QUERY"},
                        {"label": "BOM COST", "value": "BOM COST"},
                    ],
                    "clearable": False,
                },
            },
            style_cell_conditional=[
                {"if": {"column_id": "doc_type"}, "width": "120px"},
            ],
            style_cell={
                "whiteSpace": "normal",
                "height": "auto",
                "textAlign": "left",
                "font-family": "Helvetica",
                "font-size": "10px",
            },
            css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
        ),
        dmc.Group(
            [
                dbc.Button("AddRow", id=id("tab2_add_row"), color="success", size="sm"),
                dbc.Button("提交", color="primary", size="sm", id=id("tab2_submit")),
            ],
            position="apart",
        ),
    ],
    spacing=0,
)


# ----------------新增界面-upload-----------------------
page_add_upload = html.Div(
    dmc.Stack(
        [
            dmc.RadioGroup(
                [
                    dmc.Radio("PSL", value="PSL"),
                    dmc.Radio("PN", value="PN"),
                    dmc.Radio("BOM UPLOAD", value="BOM UPLOAD"),
                ],
                id=id("upload_type"),
                label="选择附件类型",
                size="sm",
            ),
            fac.DraggerUpload(
                apiUrl="/upload/",
                text="上传附件文件",
                id=id("upload_attachment"),
                lastUploadTaskRecord={},
                showUploadList=False,
            ),
            html.Div(id=id("upload_preview")),
            dmc.Button("提交", id=id("upload_submit")),
        ]
    )
)

# ----------------仪表盘指标index界面-----------------------

page_index = html.Div(
    [
        fac.DatePicker(
            id=id("month"),
            picker="month",
            placeholder="选择月份",
            format="YYYY-MM",
        ),
        dash_table.DataTable(
            data=[{}],
            columns=[
                {"name": "spec", "id": "spec"},
                {"name": "work_days", "id": "work_days"},
                {"name": "qty_delay", "id": "qty_delay"},
                {"name": "qty_on_schedule", "id": "qty_on_schedule"},
                {"name": "qty_total", "id": "qty_total"},
                {"name": "ratio", "id": "ratio"},
            ],
            editable=True,
            row_deletable=True,
            is_focused=True,
            # hidden_columns=["status", "new_source"],
            id=id("index_table"),
            style_header=style_header,
            # style_cell_conditional=[
            #     {"if": {"column_id": "doc_type"}, "width": "120px"},
            # ],
            style_cell={
                "whiteSpace": "normal",
                "height": "auto",
                "textAlign": "left",
                "font-family": "Helvetica",
                "font-size": "10px",
            },
            css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
        ),
        dmc.Group(
            [
                dbc.Button(
                    "AddRow", id=id("index_add_row"), color="success", size="sm"
                ),
                dbc.Button("提交", color="primary", id=id("index_submit")),
            ],
            position="apart",
        ),
    ]
)

# ----------------RD处理中-----------------------
sp_form_model = dbc.Col(
    [
        dbc.Label("机种名称", html_for="sp-form-model", width=1),
        dbc.Col(
            dbc.Input(id="sp-form-model", placeholder="修改机种名称"),
            width=5,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-model-form",
)
sp_form_ee = dbc.Col(
    [
        dbc.Label("EE", html_for="sp-form-ee", width=1),
        dbc.Col(
            dcc.Dropdown(
                style={"width": "100%", "flex": 1, "border-radius": "4px"},
                id="sp-form-ee",
            ),
            width=3,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-ee-form",
)
sp_form_me = dbc.Col(
    [
        dbc.Label("ME", html_for="sp-form-me", width=1),
        dbc.Col(
            dcc.Dropdown(
                style={"width": "100%", "flex": 1, "border-radius": "4px"},
                id="sp-form-me",
            ),
            width=3,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-me-form",
)
sp_form_spec = dbc.Col(
    [
        dbc.Label(
            "SPEC", html_for="sp-form-spec", width=1, className="required-fields"
        ),
        dbc.Col(
            dcc.Dropdown(
                style={"width": "100%", "flex": 1, "border-radius": "4px"},
                id="sp-form-spec",
            ),
            width=3,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-spec-form",
)
sp_form_date = dbc.Col(
    [
        dbc.Label("需求日期", html_for="sp-form-date", width=1),
        dbc.Col(
            dcc.DatePickerSingle(
                id="sp-form-date",
                display_format="YYYY-M-D",
                clearable=True,
                className="date-picker",
            ),
            width=3,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-date-form",
)
sp_ee_remark_1 = dbc.Col(
    [
        dbc.Label("意见", html_for="sp-form-remark-1", width=1),
        dbc.Col(
            dbc.Textarea(id="sp-form-remark-1", value="", placeholder="input remark"),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-remark-form-1",
)
sp_ee_remark_2 = dbc.Col(
    [
        dbc.Label(
            "意见", html_for="sp-form-remark-2", width=1, className="required-fields"
        ),
        dbc.Col(
            dbc.Textarea(id="sp-form-remark-2", placeholder="input remark"),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-remark-form-2",
)
sp_ee_remark_3 = dbc.Col(
    [
        dbc.Label("意见", html_for="sp-form-remark-3", width=1),
        dbc.Col(
            dbc.Input(id="sp-form-remark-3", placeholder="input remark"),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-remark-form-3",
)

sp_ee_upload = dbc.Col(
    [
        dbc.Label(
            "上传附件", html_for="sp-form-upload", width=1, className="required-fields"
        ),
        # dbc.Label("上传附件", html_for="sp-form-upload", width=1),
        dbc.Col(
            du.Upload(
                id="sp-form-upload",
                text="请上传附件",
                # filetypes=["xls", "xlsx", "doc", "docx", "xlsm", "txt", "html"],
                default_style={"border-color": "#ccc"},
            ),
            width=11,
            className="pr-5",
        ),
    ],
    style={"display": "flex"},
    id="sp-ee-upload-form",
)
sp_ee_modal_1 = dbc.Modal(
    [
        dbc.ModalHeader(id="sp-ee-modal-header-1"),
        dbc.ModalBody(id="sp-ee-modal-body-1"),
        dbc.ModalFooter(
            dbc.Button("确定", id="sp-ee-modal-close-1", className="ml-auto", href="#")
        ),
    ],
    id="sp-ee-modal-window-1",
    size="sm",
    centered=True,
)
sp_ee_modal_2 = dbc.Modal(
    [
        dbc.ModalHeader(id="sp-ee-modal-header-2"),
        dbc.ModalBody(id="sp-ee-modal-body-2"),
        dbc.ModalFooter(
            dbc.Button("确定", id="sp-ee-modal-close-2", className="ml-auto", href="#")
        ),
    ],
    id="sp-ee-modal-window-2",
    size="sm",
    centered=True,
)
sp_ee_modal_3 = dbc.Modal(
    [
        dbc.ModalHeader(id="sp-ee-modal-header-3"),
        dbc.ModalBody(id="sp-ee-modal-body-3"),
        dbc.ModalFooter(
            dbc.Button("确定", id="sp-ee-modal-close-3", className="ml-auto", href="#")
        ),
    ],
    id="sp-ee-modal-window-3",
    size="sm",
    centered=True,
)
sp_ee_action = dbc.Col(
    [
        dbc.Label("Action", html_for="sp-ee-action", width=1),
        dbc.Col(
            dbc.RadioItems(
                id="sp-ee-action",
                options=[
                    {"label": "核准", "value": 1},
                    {"label": "退单", "value": 2},
                    {"label": "转单", "value": 3},
                ],
                value=1,
            ),
            width=10,
        ),
    ],
)
sp_ee_submit_1 = dbc.Col(
    [
        dbc.Col(
            dbc.Button("提交", color="primary", id="sp-ee-submit-1", disabled=False),
            width={"size": 1, "offset": 11},
        )
    ],
)
sp_ee_submit_2 = dbc.Col(
    [
        dbc.Col(
            dbc.Button("提交", color="primary", id="sp-ee-submit-2", disabled=False),
            width={"size": 1, "offset": 11},
        )
    ],
)
sp_ee_submit_3 = dbc.Col(
    [
        dbc.Col(
            dbc.Button("提交", color="primary", id="sp-ee-submit-3", disabled=False),
            width={"size": 1, "offset": 11},
        )
    ],
)
sp_ee_collapse_btn = dbc.Button(
    "+",
    color="primary",
    id="sp-eecol-btn",
    size="sm",
    outline=True,
    className="mb-3",
    style={"font-family": "SimSun"},
)

sp_ee_collapse = dbc.Collapse(
    [sp_form_model, sp_form_ee, sp_form_me, sp_form_date],
    id="sp-eecol-body",
    is_open=False,
)
page_processing_edit = html.Div(
    [
        # html.Hr(className='spe-hr mt-4'),
        html.Div(
            [
                html.Div(
                    [
                        html.Table(
                            html.Tbody(
                                [
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("机种名称："),
                                                            html.Span(id="sp-ee-model"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=2,
                                                style={"width": "40%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("Status："),
                                                            html.Span(
                                                                id="sp-ee-status"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("文件类别："),
                                                            html.Span(id="sp-ee-doc"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("客户："),
                                                            html.Span(
                                                                id="sp-ee-custom"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                                style={"width": "20%"},
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("部门："),
                                                            html.Span(id="sp-ee-dept"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("创建人："),
                                                            html.Span(id="sp-ee-owner"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("EE："),
                                                            html.Span(id="sp-ee-ee"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("ME："),
                                                            html.Span(id="sp-ee-me"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("SPEC："),
                                                            html.Span(id="sp-ee-spec"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("需求日期："),
                                                            html.Span(id="sp-ee-date"),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span(
                                                                "是否需要替代料："
                                                            ),
                                                            html.Span(
                                                                id="sp-ee-second"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=1,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("Remark："),
                                                            html.Span(
                                                                id="sp-ee-remark"
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=5,
                                            ),
                                        ]
                                    ),
                                    html.Tr(
                                        [
                                            html.Td(
                                                [
                                                    html.Div(
                                                        [
                                                            html.Span("附件下载："),
                                                            html.A(
                                                                id="sp-ee-attach",
                                                                className="a-style",
                                                            ),
                                                        ]
                                                    )
                                                ],
                                                colSpan=5,
                                            )
                                        ]
                                    ),
                                ]
                            ),
                            className="spe-table",
                        )
                    ]
                )
            ],
            style={"background-color": "white", "border-radius": "4px"},
            className="py-2",
        ),
        dcc.Download(id="sp-ee-download"),
        # html.Hr(className="spe-hr-b"),
        html.Div(
            [
                dbc.Form([sp_ee_action], className="ml-4"),
                dbc.Form(className="ml-4", id="sp-form"),
            ],
            style={"background-color": "white", "border-radius": "4px"},
            className="mt-3 py-3",
        ),
        # dbc.Row(dbc.Col(dbc.Button('提交', color="primary",id="sp-ee-submit",className='ml-4',disabled=False),width={'size':1,'offset':11}),form=True)
    ],
)

page_processing_ssp = html.Div(
    [
        dbc.Spinner(
            dt.DashTabulator(
                id="sp-task-solve",
                theme="tabulator_site",
                options={"height": "70vh", "layout": "fitData"},
                columns=[
                    {"title": "状态", "field": "status"},
                    {"title": "文件类别", "field": "doc_type"},
                    {
                        "title": "机种名称",
                        "field": "model_url",
                        "formatter": "link",
                        "formatterParams": {"labelField": "model"},
                    },
                    {"title": "电子", "field": "ee"},
                    {"title": "机构", "field": "me"},
                    {"title": "部门名称", "field": "dept"},
                    {"title": "创建人", "field": "owner"},
                    {"title": "客户", "field": "customer"},
                    {"title": "是否需要替代料", "field": "second_source"},
                    # {"title": "规格", "field": "spec"},
                    {"title": "其他要求", "field": "remark"},
                    {
                        "title": "创建日期",
                        "field": "input_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {
                        "title": "需求日期",
                        "field": "request_date",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {"title": "id", "field": "id", "visible": False},
                ],
                data=[{}],
                cellEditing=True,
            ),
            color="primary",
        ),
    ]
)


# ----------处理中界面-spec----------------------------
page_process_spec = dmc.Stack(
    [
        dbc.Spinner(
            dt.DashTabulator(
                id=id("tab3_process_table"),
                theme="tabulator_site",
                options={
                    "height": "70vh",
                    "selectable": True,
                    # "layout": "fitColumns",
                    # "width": "30%",
                    # "clipboard": True,
                    # "clipboardCopyStyled": True,
                    # "clipboardCopySelector": "selected",
                    # "clipboardCopyRowRange": "selected",
                    # "clipboardPasteAction": "update",
                    # "rowFormatter": ns("rowFormatter4"),
                    # "pagination": "local",
                    # "paginationSize": 10,
                    # "paginationSizeSelector": True,
                    # "movableColumns": True,
                },
                columns=[
                    # {
                    #     "formatter": "rowSelection",
                    #     "titleFormatter": "rowSelection",
                    #     "hozAlign": "center",
                    #     "headerSort": False,
                    #     "clipboard": False,
                    # },
                    {"title": "id", "field": "id", "visible": False},
                    {
                        "title": "Status",
                        "field": "status",
                        "editor": "select",
                        "editorParams": ns("actionValue3"),
                        "headerFilter": "input",
                    },
                    {
                        "title": "DOC_TYPE",
                        "field": "doc_type",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "MODEL(PN)",
                        "field": "model",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "DEPT",
                        "field": "dept",
                        "headerFilter": "input",
                    },
                    {
                        "title": "Applicant",
                        "field": "owner",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "ECN_NO",
                        "field": "ecn_no",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "Qty",
                        "field": "qty",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "Running_Date",
                        "field": "running_date",
                        "headerFilter": "input",
                        "editor": "input",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                    },
                    {
                        "title": "REMARK",
                        "field": "remark",
                        "headerFilter": "input",
                        "editor": "input",
                    },
                    {
                        "title": "Input_Date",
                        "field": "input_date",
                        "headerFilter": "input",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                    },
                    {
                        "title": "Release_Date",
                        "field": "release_date",
                        "headerFilter": "input",
                        "formatter": "datetime",
                        "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                    },
                    {
                        "title": "SPEC",
                        "field": "spec",
                        "headerFilter": "input",
                    },
                ],
            )
        ),
        # dag.AgGrid(
        #     id=id("tab3_process_table"),
        #     className="ag-theme-quartz",
        #     columnDefs=[
        #         {"headerName": "id", "field": "id", "hide": True},
        #         {
        #             "headerName": "Status",
        #             "field": "status",
        #             "editable": True,
        #             "cellEditor": {"function": "DMC_Select"},
        #             "cellEditorParams": {
        #                 "options": ["release", "canceled", "follow_up"],
        #                 "clearable": True,
        #                 "shadow": "xl",
        #             },
        #             "cellEditorPopup": True,
        #             "singleClickEdit": True,
        #         },
        #         {"headerName": "DOC_TYPE", "field": "doc_type"},
        #         {"headerName": "MODEL(PN)", "field": "model"},
        #         {"headerName": "DEPT", "field": "dept"},
        #         {"headerName": "Applicant", "field": "owner"},
        #         {"headerName": "ECN_NO", "field": "ecn_no"},
        #         {"headerName": "Qty", "field": "qty"},
        #         {"headerName": "Running_Date", "field": "running_date"},
        #         {"headerName": "REMARK", "field": "remark"},
        #         {"headerName": "Input_Date", "field": "input_date"},
        #         {"headerName": "Release_Date", "field": "release_date"},
        #         {"headerName": "SPEC", "field": "spec"},
        #     ],
        #     columnSize="responsiveSizeToFit",
        #     rowData=[],
        #     defaultColDef={
        #         "resizable": True,
        #         "sortable": True,
        #         "filter": True,
        #         "wrapHeaderText": True,
        #         "autoHeaderHeight": True,
        #     },
        #     dashGridOptions={
        #         "rowSelection": "single",
        #         "stopEditingWhenCellsLoseFocus": True,
        #         "singleClickEdit": True,
        #         # "rowHeight": 35,
        #         "enableCellTextSelection": True,
        #         "ensureDomOrder": True,
        #         "suppressRowClickSelection": True,
        #     },
        #     getRowId="params.data.id",
        #     style={"height": 400},
        # ),
        dmc.Group(dbc.Button("提交", color="primary", id=id("tab3_submit"))),
    ],
    spacing=0,
)


def page_processing():
    page_processing = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("SSP", value="1"),
                    dmc.Tab("SPEC", value="2"),
                ]
            ),
            dmc.Space(h=5),
            dmc.TabsPanel(page_processing_ssp, value="1"),
            dmc.TabsPanel(page_process_spec, value="2"),
        ],
        # color="red",
        value="1",
        id=id("ongoing-tabs"),
    )
    return page_processing


# ---------------SPEC查询界面---------------------
def page_query():
    return html.Div(
        [
            dmc.Grid(
                [
                    dmc.Col(
                        dmc.Grid(
                            [
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-status", placeholder="Status"
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-dept", placeholder="Dept"
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-doctype", placeholder="DocType"
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-model", placeholder="Model"
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-spec", placeholder="SPEC"
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.DatePickerSingle(
                                        display_format="YYYY-M-D",
                                        clearable=True,
                                        # className="spe-query-date-picker-single",
                                        id="sp-query-date-single",
                                        placeholder="ReleaseDate",
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-sot",
                                        placeholder="SubmitOnTime",
                                        options=["Y", "N"],
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-rot",
                                        placeholder="ReleaseOnTime",
                                        options=["Y", "N"],
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.Dropdown(
                                        id="sp-query-fot",
                                        placeholder="FollowUpOnTime",
                                        options=["Y", "N"],
                                    ),
                                    span=2,
                                ),
                                dmc.Col(
                                    dcc.DatePickerRange(
                                        start_date_placeholder_text="InputDate Start",
                                        end_date_placeholder_text="InputDate End",
                                        calendar_orientation="vertical",
                                        clearable=True,
                                        # className="spe-query-date-picker-multi",
                                        id="sp-query-date-multi",
                                        display_format="YYYY-M-D",
                                    ),
                                    span=5,
                                ),
                            ],
                            gutter=5,
                        ),
                        span=11,
                    ),
                    dmc.Col(
                        dbc.Button(
                            "查询",
                            color="primary",
                            id="sp-query-btn",
                            className="h-100 w-100",
                        ),
                        span=1,
                    ),
                ],
                style={"width": "90vw"},
                gutter=5,
                # columns=24,
            ),
            # dbc.Row(
            #     [
            #         dbc.Col(
            #             [
            #                 dbc.Row(
            #                     [
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-status",
            #                                 placeholder="Select Status",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                             ),
            #                             width=2,
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-dept",
            #                                 placeholder="Select Dept",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                             ),
            #                             width=2,
            #                         ),
            #                         dbc.Col(
            #                             dcc.DatePickerRange(
            #                                 start_date_placeholder_text="InputDate Start",
            #                                 end_date_placeholder_text="InputDate End",
            #                                 calendar_orientation="vertical",
            #                                 clearable=True,
            #                                 className="spe-query-date-picker-multi",
            #                                 id="sp-query-date-multi",
            #                                 display_format="YYYY-M-D",
            #                             ),
            #                             width=4,
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-doctype",
            #                                 placeholder="Select DocType",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                             ),
            #                             width=2,
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-model",
            #                                 placeholder="Select Model",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                             ),
            #                             width=2,
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-spec",
            #                                 placeholder="Select SPEC",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                             ),
            #                             width=2,
            #                             className="my-3",
            #                         ),
            #                         dbc.Col(
            #                             dcc.DatePickerSingle(
            #                                 display_format="YYYY-M-D",
            #                                 clearable=True,
            #                                 className="spe-query-date-picker-single",
            #                                 id="sp-query-date-single",
            #                                 placeholder="ReleaseDate",
            #                             ),
            #                             className="my-3",
            #                             width=2,
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-sot",
            #                                 placeholder="SubmitOnTime",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                                 options=["Y", "N"],
            #                             ),
            #                             width=2,
            #                             className="my-3",
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-rot",
            #                                 placeholder="ReleaseOnTime",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                                 options=["Y", "N"],
            #                             ),
            #                             width=2,
            #                             className="my-3",
            #                         ),
            #                         dbc.Col(
            #                             dcc.Dropdown(
            #                                 id="sp-query-fot",  # 新增
            #                                 placeholder="FollowUpOnTime",
            #                                 style={
            #                                     "width": "100%",
            #                                     "flex": 1,
            #                                     "border-style": "none",
            #                                 },
            #                                 options=["Y", "N"],
            #                             ),
            #                             width=2,
            #                             className="my-3",
            #                         ),
            #                     ]
            #                 ),
            #             ]
            #         ),
            #         dbc.Col(
            #             dbc.Button(
            #                 "查询",
            #                 color="primary",
            #                 id="sp-query-btn",
            #                 style={"height": "83%", "width": "100%"},
            #             ),
            #             width=1,
            #         ),
            #     ]
            # ),
            dcc.Store(id="sp-query-state"),
            dbc.Modal(
                [
                    dbc.ModalHeader(
                        [
                            # DCBU_DCBU
                            dbc.Badge(
                                id="sp-query-modal-dept",
                                color="primary",
                                className="mr-2",
                                style={"font-size": "22px"},
                            ),
                            dbc.Badge(
                                id="sp-query-modal-doctype",
                                color="light",
                                style={"font-size": "22px"},
                            ),
                        ]
                    ),
                    dbc.ModalBody(id="sp-query-modal-body"),
                    dbc.ModalFooter(
                        dbc.Button(
                            "Close",
                            id="sp-query-modal-close",
                            className="ml-auto",
                            n_clicks=0,
                            color="primary",
                        )
                    ),
                ],
                id="sp-query-modal",
                scrollable=True,
                size="lg",
                is_open=False,
            ),
            dbc.Spinner(
                dt.DashTabulator(
                    id="sp-query-result",
                    theme="tabulator_site",
                    options={
                        "height": "67vh",
                        "layout": "fitData",
                        # "responsiveLayout": "hide",
                        # "persistence": {"columns": ["visible"]},
                    },
                    downloadButtonType={
                        "css": "btn btn-sm btn-outline-primary",
                        "text": "Export",
                        "type": "xlsx",
                    },
                    clearFilterButtonType={
                        "css": "btn btn-sm btn-outline-dark",
                        "text": "Clear Filters",
                    },
                    columns=[
                        {
                            "title": "History",
                            "field": "history",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "状态",
                            "field": "status",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "当前处理人",
                            "field": "current",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "申请人",
                            "field": "owner",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "规格",
                            "field": "spec",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "文件类型",
                            "field": "doc_type",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "机种名称",
                            "field": "model",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "变更单号",
                            "field": "ecn_no",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "RunningDate",
                            "field": "running_date",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "电子",
                            "field": "ee",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "机构",
                            "field": "me",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "部门名称",
                            "field": "dept",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "Input_date",
                            "field": "input_date",
                            "headerMenu": ns("headerMenu"),
                            "formatter": "datetime",
                            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                            "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                        },
                        {
                            "title": "Request_date",
                            "field": "request_date",
                            "headerMenu": ns("headerMenu"),
                            "formatter": "datetime",
                            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                            "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                            # "visible": False,
                        },
                        {
                            "title": "Submit_date",
                            "field": "submit_date",
                            "headerMenu": ns("headerMenu"),
                            "formatter": "datetime",
                            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                            "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                        },
                        {
                            "title": "Release_date",
                            "field": "release_date",
                            "headerMenu": ns("headerMenu"),
                            "formatter": "datetime",
                            "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                        },
                        {
                            "title": "Followup_date",
                            "field": "followup_date",
                            "headerMenu": ns("headerMenu"),
                            "formatter": "datetime",
                            # "formatterParams": {"outputFormat": "YYYY-MM-DD"},
                            "formatterParams": {"outputFormat": "YYYY-MM-DD HH:mm:ss"},
                        },
                        {
                            "title": "std_release_lt",
                            "field": "std_release_lt",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "ActLT",
                            "field": "act_release_lt",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "Submit_ontime",
                            "field": "submit_ontime",
                            "headerMenu": ns("headerMenu"),
                            # "visible": False,
                        },
                        {
                            "title": "Release_ontime",
                            "field": "release_ontime",
                            "headerMenu": ns("headerMenu"),
                            # "visible": False,
                        },
                        {
                            "title": "followup_lt",
                            "field": "followup_lt",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "ACT FollowUpLT(Day)",
                            "field": "act_followup_lt",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "Followup_ontime",
                            "field": "followup_ontime",
                            "headerMenu": ns("headerMenu"),
                            # "visible": False,
                        },
                        {
                            "title": "ECN_QTY",
                            "field": "ecn_qty",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "PSL_QTY",
                            "field": "psl_qty",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "work_time_minute",
                            "field": "work_time_minute",
                            "headerMenu": ns("headerMenu"),
                        },
                        {
                            "title": "id",
                            "field": "id",
                            # "headerMenu": ns("headerMenu"),
                            "visible": False,
                        },
                    ],
                    # data=[{}],
                ),
                color="primary",
            ),
        ]
    )


page_doc = file_browser(SSP_DIR / "program" / "DOC" / "spec", __name__)


def page_due_day():
    sql = "select * from ssp_spec.due_day"
    df = read_sql(sql)
    table = dag.AgGrid(
        id=id("due_day_table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "id", "headerName": "id", "hide": True},
            {"field": "action", "headerName": "action", "editable": True, "hide": True},
            {"field": "dept", "headerName": "dept"},
            {"field": "doc_type", "headerName": "doc_type"},
            {
                "field": "due_day",
                "headerName": "due_day",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
            {
                "field": "followup_lt",
                "headerName": "followup_lt",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
            {
                "field": "ecn_qty",
                "headerName": "ecn_qty",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
            {
                "field": "psl_qty",
                "headerName": "psl_qty",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
            {
                "field": "work_time_minute",
                "headerName": "work_time_minute",
                "editable": True,
                "cellEditor": "agNumberCellEditor",
            },
        ],
        columnSize="responsiveSizeToFit",
        rowData=df.to_dict("records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            "suppressRowClickSelection": True,
        },
        getRowId="params.data.id",
        style={"height": 400},
    )
    return dmc.Stack(
        [
            table,
            dmc.Button("提交", id=id("due_day_submit")),
        ]
    )


def page_duty():
    sql = "select * from ssp_spec.duty"
    df = read_sql(sql)
    table = dag.AgGrid(
        id=id("duty_table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "id", "headerName": "id"},
            {"field": "action", "headerName": "action", "editable": True, "hide": True},
            {"field": "dept", "headerName": "dept"},
            {
                "field": "owner",
                "headerName": "owner",
                "editable": True,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": df["owner"].unique().tolist(),
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
            },
        ],
        columnSize="responsiveSizeToFit",
        rowData=df.to_dict("records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            # "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
            "suppressRowClickSelection": True,
        },
        getRowId="params.data.id",
        style={"height": 400},
    )
    return dmc.Stack(
        [
            table,
            dmc.Button("提交", id=id("duty_submit")),
        ]
    )


def dash_board_card(title, count=0):
    return dmc.Card(
        children=[
            html.Div(
                [
                    html.U(
                        title,
                        style={
                            "text-decoration-color": "#1295b9",
                            "text-decoration-thickness": "0.15rem",
                            "text-underline-offset": "8px",
                            "font-size": "12x",
                        },
                    ),
                ],
                style={
                    "margin-top": "-12px",
                },
            ),
            dmc.Space(h=20),
            fac.Statistic(
                value=fuc.CountUp(
                    end=count,
                    duration=3,
                    style={"font-size": "40px", "font-weight": 700},
                ),
                # title="数量",
                valueStyle={
                    "color": "#1295b9",
                    "font-size": "20px",
                },
                # prefix=fac.AntdIcon(icon="antd-caret-right"),
                style={
                    "font-size": "100px",
                    "text-align": "center",
                    "justify-content": "center",
                    "width": "150px",
                    "height": "150px",
                },
            ),
        ],
        withBorder=True,
        shadow="md",
        style={"height": "150px"},
    )


def home_page():
    nt_name = get_nt_name()
    doc_type = ["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE"]
    status = ["process", "release", "open", "accepted", "modify_ee", "modify_me"]
    sql = "select input_date,std_release_lt,release_date,followup_lt,\
        now() as now,status from ssp_spec.task \
        where spec=%s and status in %s and doc_type in %s"
    params = [nt_name, status, doc_type]
    df = read_sql(sql, params=params)
    count1, count2, count3, count4, count5, count6 = 0, 0, 0, 0, 0, 0
    if not df.empty:
        df1 = df.loc[df["status"] == "process"]
        if not df1.empty:
            count1 = df1.shape[0]

            df1["work_days"] = df1.apply(
                lambda x: len(get_workdays(x["input_date"], x["now"])), axis=1
            )
            df1 = df1.loc[df1["work_days"] > df1["std_release_lt"]]
            if not df1.empty:
                count2 = df1.shape[0]

        df1 = df.loc[df["status"] == "release"]
        if not df1.empty:
            count3 = df1.shape[0]

            df1["work_days"] = df1.apply(
                lambda x: len(get_workdays(x["release_date"], x["now"])), axis=1
            )
            df1 = df1.loc[df1["work_days"] > df1["followup_lt"]]
            if not df1.empty:
                count4 = df1.shape[0]

        df1 = df.loc[df["status"] == "open"]
        if not df1.empty:
            count5 = df1.shape[0]
        df1 = df.loc[df["status"].isin(["accepted", "modify_ee", "modify_me"])]
        if not df1.empty:
            count6 = df1.shape[0]

    return dmc.Stack(
        [
            html.Div(
                [
                    fac.Divider("ECN", innerTextOrientation="left"),
                    dmc.Group(
                        [
                            dash_board_card("待生效", count1),
                            dash_board_card("逾期(未生效)", count2),
                            dash_board_card("待检查", count3),
                            dash_board_card("逾期(未检查)", count4),
                        ],
                        position="apart",
                    ),
                ],
                style={"width": "fit-content"},
            ),
            html.Div(
                [
                    fac.Divider("SSP", innerTextOrientation="left"),
                    dmc.Group(
                        [
                            dash_board_card("尚未受理", count5),
                            dash_board_card("待处理", count6),
                        ],
                        position="apart",
                    ),
                ],
                style={"width": "fit-content"},
            ),
        ]
    )


def layout(**query):
    page_dict = {
        "add-spec": page_add_spec,
        "add-cost": page_add_cost,
        "add-upload": page_add_upload,
        "add-index": page_index,
        "task": page_task,
        "processing": page_processing(),
        "processing2": page_processing_edit,
        "query": page_query(),
        "doc": page_doc,
        "home": home_page(),
        "due-day": page_due_day(),
        "duty": page_duty(),
    }
    page = query.get("page", "home")
    content = page_dict.get(page)
    grid = dmc.Grid(
        [
            dmc.Col(sidebar, span=10),
            dmc.Col(content, span=90),
        ],
        columns=100,
        # style={"width": "100vw"},
        gutter=5,
        className="mx-auto",
    )
    return grid


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="nav_click"),
    Output("sp-nav", "style"),
    [Input("sp-nav", "id")],
    prevent_initial_call=False,
)


# ------工作日计算---------------------
def get_workday_offset(begin_date, end_date):
    date = begin_date
    count = 0
    while date <= end_date:
        if is_holiday(date):
            count += 1
        date += timedelta(days=1)
    offset = (end_date - begin_date).days - count
    return offset


# @callback(
#     Output(id("sidebar"), "currentKey"),
#     Input("url", "search"),
# )
# def sidebar_current_key(search):
#     """设置sidebar当前页面"""
#     key = search.split("=")[-1]
#     return key


@callback(
    Output(id("tab1_table"), "data"),
    Input("tab1_add_row", "n_clicks"),
    State(id("tab1_table"), "data"),
)
def tab1_table_add_row(n_clicks, data):
    """插入行"""
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


@callback(
    Output(id("index_table"), "data"),
    Input(id("index_add_row"), "n_clicks"),
    State(id("index_table"), "data"),
)
def index_table_add_row(n_clicks, data):
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


# @callback(
#     Output(id("tab1_table"), "dropdown_conditional"),
#     Input(id("tab1_table"), "id"),
#     State(id("tab1_table"), "columns"),
#     prevent_initial_call=False,
# )
# def initial_tab1_table_columns(id, columns):
#     """初始化表格，部门下拉菜单和owner（RD）下拉菜单"""
#     sql = "select id,concat_ws('_',dept_group,dept_name) as dept from dept"  # 建立一个dept的列，内容来自dept表,内容为dept_group,dept_name连接的结果
#     depts = read_sql(sql)

#     sql = "select nt_name,dept, dept_id from user where termdate is null"
#     users = read_sql(sql)

#     users["dept_id"] = users["dept_id"].astype(str)

#     dc_dept = [
#         {
#             "if": {"column_id": "dept"},
#             "options": [{"label": i, "value": i} for i in depts["dept"]],
#         },
#     ]

#     dc_rd = [
#         {
#             "if": {
#                 "column_id": "owner",
#                 "filter_query": f"{{dept}} eq {f'{x.dept}'}",
#             },
#             "options": [
#                 {"label": i, "value": i}
#                 for i in users.query(f'dept_id=="{x.id}"').nt_name.tolist()
#             ],
#         }
#         for x in depts.itertuples()
#     ]

#     dropdown_conditional = dc_dept + dc_rd
#     return dropdown_conditional


@callback(
    Output(id("tab1_table"), "data"),
    # Output("global-notice", "children"),
    Input(id("tab1_table"), "data_timestamp"),
    State(id("tab1_table"), "data"),
    State(id("tab1_table"), "active_cell"),
    State(id("tab1_table"), "columns"),
)
def tab1_spec_table_completion_information(new_data, data, ac, columns):
    """根据owner匹配dept"""
    if not (new_data and ac):
        raise PreventUpdate

    column_id = ac.get("column_id")
    if column_id not in (
        "owner"
    ):  # 只有在active栏位（owner栏位）输入内容，才可以运行函数
        raise PreventUpdate

    df = pd.DataFrame(data)

    df = df.reindex(columns=[i["id"] for i in columns])
    df = df.fillna("")
    df["owner"] = df["owner"].astype(str).str.strip()
    df["owner"] = df["owner"].str.lower()  # 将df和df1的owner都转为小写，方便匹配数据
    df = df.replace({"": None}).replace({np.nan: None})
    df = df.reset_index()

    if not df.empty:
        params = df["owner"].dropna().tolist()
        sql = "select distinct nt_name as owner,dept as dept1,dept_id as dept_id1 \
          from ssp.user where nt_name in %s"  # 数据库的dept和df的重复，进行重命名
        df1 = read_sql(sql, params=[params])

        df1["owner"] = df1["owner"].str.lower()
        df = df.merge(df1, on="owner", how="left")  # 以owner为关键字合并df和df1的数据
        df["dept"] = df["dept1"]
        df["dept_id"] = df["dept_id1"]

    return df.to_dict(orient="records")  # 输出内容到网页上


@callback(
    Output("global-notice", "children"),  # app中layout写的全局notice
    Output(id("tab1_table"), "data"),  # 提交成功 or 失败 页面数据清空与否
    Input(id("tab1_submit"), "n_clicks"),
    State(id("tab1_table"), "columns"),
    State(id("tab1_table"), "data"),
    State("user", "data"),  # 用于获取NT账号
)
def tab1_spec_table_submit(n_clicks, columns, data, user):
    """必填栏位,初始化,提交到数据库"""
    if not n_clicks:
        raise PreventUpdate

    now = datetime.now()
    df = pd.DataFrame(data)
    if df.empty:
        return notice("表格内容不能空", "error"), no_update
    columns = [i["id"] for i in columns]  # 空值df会自动过滤，所以需要重新读入columns
    df = df.reindex(columns=columns)

    spec = user.get("nt_name").lower()  # 获取NT账号
    df["spec"] = spec
    df["new_source"] = "Y"
    df["status"] = "open"  # df创建一列，并初始化
    df["input_date"] = now
    df["release_date"] = None
    df["release_ontime"] = None
    df["act_release_lt"] = None

    df = df.fillna("")  # 将NAN转换为""
    c1 = df["model"] == ""
    if c1.any():
        return notice("MODEL不能空", "error"), no_update
    c1 = df["owner"] == ""
    if c1.any():
        return notice("APPLICANT不能空", "error"), no_update
    c1 = df["dept"] == ""
    if c1.any():
        return notice("DEPT不能空", "error"), no_update
    c1 = df["doc_type"] == ""
    if c1.any():
        return notice("DOC_TYPE不能空", "error"), no_update
    c1 = df["doc_type"].isin(["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE", "GRP CHANGE"])
    c2 = df["ecn_no"] == ""
    if (c1 & c2).any():
        return notice("ECN_NO不能空", "error"), no_update
    c1 = df["doc_type"].isin(["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE", "COST QUERY"])
    df["status"] = np.where(c1, "process", df["status"])

    # *新增即release
    # *DOC TYPE=CM,CBOM,SBOM,DMFS,DFCS,GRP CHANGE,PART QUERY,BOM COST
    c1 = df["doc_type"].isin(["GRP CHANGE", "CM", "DFMS", "DFCS", "SBOM", "CBOM"])
    df["status"] = np.where(c1, "release", df["status"])
    df["release_date"] = np.where(c1, now, df["release_date"])

    # c1 = df["doc_type"].isin(["CBOM", "SBOM", "DFMS", "DFCS"])
    df["release_ontime"] = np.where(c1, "Y", df["release_ontime"])
    df["act_release_lt"] = np.where(c1, 1, df["act_release_lt"])

    sql1 = "select dept_id,doc_type,due_day,psl_qty,ecn_qty,followup_lt,\
            work_time_minute from ssp_spec.due_day"
    df_due = read_sql(sql1)
    df = df.merge(df_due, on=["dept_id", "doc_type"], how="left")
    df["std_release_lt"] = df["due_day"]

    c1 = df["dept_id"].astype(int) == 21  # DES_CDBU
    c2 = df["model"].str.lower().str.startswith(("ecd"))
    c3 = df["doc_type"].isin(["CBOM", "SBOM"])
    df["std_release_lt"] = np.where(c1 & c2 & c3, 10, df["std_release_lt"])
    df["psl_qty"] = np.where(c1 & c2 & c3, 3, df["psl_qty"])
    df["work_time_minute"] = np.where(c1 & c2 & c3, 180, df["work_time_minute"])

    c2 = df["model"].str.lower().str.startswith(("ecdl"))
    df["std_release_lt"] = np.where(c1 & c2 & c3, 3, df["std_release_lt"])
    df["psl_qty"] = np.where(c1 & c2 & c3, 1, df["psl_qty"])
    df["work_time_minute"] = np.where(c1 & c2 & c3, 60, df["work_time_minute"])

    # *SM/ECN,ecn_qty,work_time_minute为空
    # c1 = df["doc_type"].isin(["SM", "ECN"])
    # df["ecn_qty"] = np.where(c1, None, df["ecn_qty"])
    # df["work_time_minute"] = np.where(c1, None, df["work_time_minute"])

    conn = pool.connection()  # 连接数据库
    cu = conn.cursor()  # 建立游标

    sql2 = "insert into ssp_spec.task(new_source,status,model,spec,owner,dept,\
        dept_id,doc_type,ecn_no,qty,input_date,running_date,remark,\
        std_release_lt,followup_lt,ecn_qty,psl_qty,work_time_minute,\
            release_ontime,release_date,act_release_lt) \
        value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"

    columns = [
        "new_source",
        "status",
        "model",
        "spec",
        "owner",
        "dept",
        "dept_id",
        "doc_type",
        "ecn_no",
        "qty",
        "input_date",
        "running_date",
        "remark",
        "std_release_lt",
        "followup_lt",
        "ecn_qty",
        "psl_qty",
        "work_time_minute",
        "release_ontime",
        "release_date",
        "act_release_lt",
    ]
    # print(df.columns)
    # print(df.columns.isin(columns))

    df = df.reindex(columns=columns)
    # reindex 后没有的列会新生成，空字段为nan，需要转换为None
    df = df.replace({"": None, np.nan: None})
    params = df.values.tolist()
    cu.executemany(sql2, params)
    conn.commit()
    cu.close()
    conn.close()
    return notice("提交成功", "success"), [{}]  # 提交成功，网页数据清空


# -----新增-cost函数------------------
@callback(
    Output(id("tab2_table"), "data"),
    Input(id("tab2_add_row"), "n_clicks"),
    State(id("tab2_table"), "data"),
)
def tab2_table_add_row(n_clicks, data):
    """create_meeting_table插入行"""
    if n_clicks:
        data.append({})
        return data
    else:
        raise PreventUpdate


# @callback(
#     Output(id("tab2_table"), "dropdown_conditional"),
#     Input(id("tab2_table"), "id"),
#     State(id("tab2_table"), "columns"),
#     prevent_initial_call=False,
# )
# def initial_tab2_table_columns(id, columns):
#     """初始化表格，部门下拉菜单和owner（RD）下拉菜单"""
#     sql = "select id,concat_ws('_',dept_group,dept_name) as dept from dept"  # 建立一个dept的列，内容来自dept表,内容为dept_group,dept_name连接的结果
#     depts = read_sql(sql)

#     sql = "select nt_name,dept, dept_id from user where termdate is null"
#     users = read_sql(sql)

#     users["dept_id"] = users["dept_id"].astype(str)

#     dc_dept = [
#         {
#             "if": {"column_id": "dept"},
#             "options": [{"label": i, "value": i} for i in depts["dept"]],
#         },
#     ]

#     dc_rd = [
#         {
#             "if": {
#                 "column_id": "owner",
#                 "filter_query": f"{{dept}} eq {f'{x.dept}'}",
#             },
#             "options": [
#                 {"label": i, "value": i}
#                 for i in users.query(f'dept_id=="{x.id}"').nt_name.tolist()
#             ],
#         }
#         for x in depts.itertuples()
#     ]

#     dropdown_conditional = dc_dept + dc_rd
#     return dropdown_conditional


@callback(
    Output(id("tab2_table"), "data"),
    # Output("global-notice", "children"),
    Input(id("tab2_table"), "data_timestamp"),
    State(id("tab2_table"), "data"),
    State(id("tab2_table"), "active_cell"),
    State(id("tab2_table"), "columns"),
)
def tab2_cost_table_completion_information(new_data, data, ac, columns):
    """根据owner匹配dept"""
    if not (new_data and ac):
        raise PreventUpdate

    column_id = ac.get("column_id")
    if column_id not in (
        "owner"
    ):  # 只有在active栏位（owner栏位）输入内容，才可以运行函数
        raise PreventUpdate

    df = pd.DataFrame(data)

    df = df.reindex(columns=[i["id"] for i in columns])
    df = df.fillna("")
    df["owner"] = df["owner"].astype(str).str.strip()
    df["owner"] = df["owner"].str.lower()  # 将df和df1的owner都转为小写，方便匹配数据
    df = df.replace({"": None}).replace({np.nan: None})
    df = df.reset_index()

    if not df.empty:
        params = df["owner"].dropna().tolist()
        sql = "select distinct nt_name as owner,dept as dept1,dept_id as dept_id1 \
          from ssp.user where nt_name in %s"  # 数据库的dept和df的重复，进行重命名
        df1 = read_sql(sql, params=[params])

        df1["owner"] = df1["owner"].str.lower()
        df = df.merge(df1, on="owner", how="left")  # 以owner为关键字合并df和df1的数据
        df["dept"] = df["dept1"]
        df["dept_id"] = df["dept_id1"]

    return df.to_dict(orient="records")  # 输出内容到网页上


@callback(
    Output("global-notice", "children"),  # app中layout写的全局notice
    Output(id("tab2_table"), "data"),
    Input(id("tab2_submit"), "n_clicks"),
    State(id("tab2_table"), "columns"),
    State(id("tab2_table"), "data"),
    State("user", "data"),
)
def tab2_cost_table_submit(n_clicks, columns, data, user):
    """必填栏位,初始化,提交到数据库"""
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    now = datetime.now()

    if df.empty:
        return notice("表格内容不能空", "error")
    columns = [i["id"] for i in columns]  # 空值df会自动过滤，所以需要重新读入columns
    df = df.reindex(columns=columns)
    df = df.fillna("")  # 将NAN转换为""
    spec = user.get("nt_name").title()
    df["spec"] = spec
    df["new_source"] = "Y"
    df["status"] = "process"
    df["input_date"] = now
    df["release_date"] = None
    df["release_ontime"] = None
    df["act_release_lt"] = None

    if (df["model"] == "").any():
        return notice("MODEL不能空", "error"), no_update

    if (df["owner"] == "").any():
        return notice("APPLICANT不能空", "error"), no_update

    if (df["dept"] == "").any():
        return notice("DEPT不能空", "error"), no_update

    if (df["doc_type"] == "").any():
        return notice("DOC_TYPE不能空", "error"), no_update

    c1 = df["doc_type"].isin(["PART COST", "BOM COST"])
    df["status"] = np.where(c1, "release", df["status"])
    df["release_date"] = np.where(c1, now, df["release_date"])
    df["release_ontime"] = np.where(c1, "Y", df["release_ontime"])
    df["act_release_lt"] = np.where(c1, 1, df["act_release_lt"])

    df["running_date"] = pd.to_datetime(df["running_date"], errors="coerce")

    c1 = df["qty"] == ""
    df["qty"] = np.where(c1, 1, df["qty"])

    conn = pool.connection()
    cu = conn.cursor()
    sql1 = "select * from ssp_spec.due_day"

    df2 = read_sql(sql1)
    df2 = df2.rename(columns={"due_day": "std_release_lt"})

    df = df.merge(df2, on=["dept", "doc_type"], how="left")

    # c1 = df["doc_type"] == "COST QUERY"
    # df["psl_qty"] = np.where(c1, None, df["psl_qty"])
    # df["work_time_minute"] = np.where(c1, None, df["work_time_minute"])

    c1 = df["doc_type"] == "PART COST"
    df["qty"] = df["qty"].astype(float)
    df["psl_qty"] = np.where(c1, df["psl_qty"] * df["qty"], df["psl_qty"])
    df["work_time_minute"] = np.where(
        c1, df["work_time_minute"] * df["qty"], df["work_time_minute"]
    )

    sql2 = "insert into ssp_spec.task(new_source,status,model,spec,owner,dept,\
        dept_id,doc_type,ecn_no,qty,input_date,running_date,remark,\
        std_release_lt,followup_lt,ecn_qty,psl_qty,work_time_minute,\
            release_date,release_ontime,act_release_lt) \
        value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    df = df.reindex(
        columns=[
            "new_source",
            "status",
            "model",
            "spec",
            "owner",
            "dept",
            "dept_id",
            "doc_type",
            "ecn_no",
            "qty",
            "input_date",
            "running_date",
            "remark",
            "std_release_lt",
            "followup_lt",
            "ecn_qty",
            "psl_qty",
            "work_time_minute",
            "release_date",
            "release_ontime",
            "act_release_lt",
        ]
    )
    df = df.replace({"": None, np.nan: None})
    params = df.values.tolist()
    cu.executemany(sql2, params)
    conn.commit()
    cu.close()
    conn.close()
    return notice("提交成功", "success"), [{}]


# -----新增-upload函数------------------
@callback(
    Output(id("upload_preview"), "children"),  # 附件的数据读入到页面的表格中,只显示一行
    Input(id("upload_attachment"), "lastUploadTaskRecord"),
)
def upload_preview(upload_attachment):
    attach = f'{upload_attachment.get("taskId")}'
    file_name = f'{upload_attachment.get("fileName")}'
    children = fuc.ExcelPreview(
        src=f"http://sup.deltaww.com/upload/{attach}/{file_name}",
        style={"height": "200px"},
    )
    return children


@callback(
    Output("global-notice", "children"),
    Output(id("upload_submit"), "disabled"),
    Input(id("upload_submit"), "n_clicks"),
    State(id("upload_type"), "value"),
    State(id("upload_attachment"), "lastUploadTaskRecord"),
)
def upload_submit(n_clicks, file_type, upload_attachment):
    if not n_clicks:
        raise PreventUpdate
    if not file_type:
        return notice("请选择文件类型", "error"), False
    if not upload_attachment:
        return notice("请上传附件", "error"), False

    attach = f'{upload_attachment.get("taskId")}'  # 获取上传附件的ID（自动生成）
    file_name = f'{upload_attachment.get("fileName")}'  # 获取上传附件的文件名
    df = pd.read_excel(
        UPLOAD_FOLDER_ROOT / attach / file_name, dtype=str, keep_default_na=False
    )
    df = df.rename(
        columns={
            "BOM maintain by": "spec",
            "Material": "model",
            "CREATE DATE": "input_date",
            "BU": "dept",
            "Created By": "spec",
            "Object Type Indicator": "doc_type",
            "Name": "model",
            "Change Number": "ecn_no",
            "Created On": "input_date",
        }
    )
    df["input_date"] = pd.to_datetime(df["input_date"], errors="coerce")
    if df["input_date"].isna().any():
        return notice("CREATE DATE不能空,且须日期格式", "error"), no_update
    df["input_date"] = df["input_date"].dt.strftime("%Y-%m-%d %H:%M:%S")

    if file_type == "PN":
        df["doc_type"] = "PN"

    sql = "select distinct doc_type from ssp_spec.due_day"
    dfc = read_sql(sql)
    c1 = df["doc_type"].isin(dfc["doc_type"])
    if not c1.all():
        return notice("文件类型不能空,且与数据库一致", "error"), no_update

    sql = "select concat_ws('_',dept_group,dept_name) as dept from ssp.dept"
    dfc = read_sql(sql)
    c1 = df["dept"].isin(dfc["dept"])
    if not c1.all():
        return notice("部门不能空,且与数据库一致", "error"), no_update

    sql = "select lower(owner) as spec from ssp_spec.duty"
    dfc = read_sql(sql)
    c1 = df["spec"].str.lower().isin(dfc["spec"])
    if not c1.all():
        return notice("规格人员不能空,且与数据库一致", "error"), no_update

    if (df["owner"] == "").any():
        return notice("owner不能空", "error"), no_update

    if (df["model"] == "").any():
        return notice("Name不能空", "error"), no_update

    df["status"] = "release"
    df["new_source"] = "Y"
    df["release_ontime"] = "Y"
    df["act_release_lt"] = 1
    df["release_date"] = df["input_date"]

    sql = "select * from ssp_spec.due_day"
    df2 = read_sql(sql)
    df2 = df2.rename(columns={"due_day": "std_release_lt"})
    df = df.merge(df2, on=["dept", "doc_type"], how="left")

    if file_type == "PSL":
        sql2 = "insert into ssp_spec.task(new_source,status,owner,dept,dept_id,\
            spec,model,doc_type,ecn_no,input_date,release_date,\
            std_release_lt,ecn_qty,psl_qty,work_time_minute,release_ontime,act_release_lt) \
            value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
        df = df.reindex(
            columns=[
                "new_source",
                "status",
                "owner",
                "dept",
                "dept_id",
                "spec",
                "model",
                "doc_type",
                "ecn_no",
                "input_date",
                "release_date",
                "std_release_lt",
                "ecn_qty",
                "psl_qty",
                "work_time_minute",
                "release_ontime",
                "act_release_lt",
            ]
        )  # 重新索引，只保留需要传入数据库的列
        df = df.replace({"": None, np.nan: None})
        params = df.values.tolist()  # df全部列转为此参数，随后传入数据库
        conn = pool.connection()
        cu = conn.cursor()
        cu.executemany(sql2, params)
        conn.commit()
        cu.close()
        conn.close()

    elif file_type == "PN":
        sql2 = "insert into ssp_spec.task(new_source,status,owner,\
            dept,dept_id,spec,model,doc_type,input_date,release_date,\
            std_release_lt,ecn_qty,psl_qty,work_time_minute,release_ontime,act_release_lt) \
            value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
        df = df.reindex(
            columns=[
                "new_source",
                "status",
                "owner",
                "dept",
                "dept_id",
                "spec",
                "model",
                "doc_type",
                "input_date",
                "release_date",
                "std_release_lt",
                "ecn_qty",
                "psl_qty",
                "work_time_minute",
                "release_ontime",
                "act_release_lt",
            ]
        )
        df = df.replace({"": None, np.nan: None})
        params = df.values.tolist()

        conn = pool.connection()
        cu = conn.cursor()
        cu.executemany(sql2, params)
        conn.commit()
        cu.close()
        conn.close()

    elif file_type == "BOM UPLOAD":
        sql2 = "insert into ssp_spec.task(new_source,status,owner,dept,dept_id,\
            spec,model,doc_type,input_date,release_date,\
            std_release_lt,ecn_qty,psl_qty,work_time_minute,release_ontime,act_release_lt) \
            value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"

        df = df.reindex(
            columns=[
                "new_source",
                "status",
                "owner",
                "dept",
                "dept_id",
                "spec",
                "model",
                "doc_type",
                "input_date",
                "release_date",
                "std_release_lt",
                "ecn_qty",
                "psl_qty",
                "work_time_minute",
                "release_ontime",
                "act_release_lt",
            ]
        )
        df = df.replace({"": None, np.nan: None})
        params = df.values.tolist()

        conn = pool.connection()
        cu = conn.cursor()
        cu.executemany(sql2, params)
        conn.commit()
        cu.close()
        conn.close()
    return notice("提交成功", "success"), True


# -----处理中-spec函数------------------
@callback(
    Output(id("tab3_process_table"), "data"),
    Input(id("ongoing-tabs"), "value"),
    State("user", "data"),
    # prevent_initial_call=False,
)
def tab3_process_table_data(value, user):
    """初始加载数据"""
    if value != "2":
        raise PreventUpdate

    nt_name = user.get("nt_name")
    sql = "SELECT * FROM ssp_spec.task \
        WHERE spec=%s and doc_type in %s and status in %s"
    params = [
        nt_name,
        ["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE", "COST QUERY"],
        ["process", "release"],
    ]
    df = read_sql(sql, params=params)
    c1 = df["doc_type"] == "COST QUERY"
    c2 = df["status"] == "release"
    df = df.loc[~(c1 & c2)]
    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    # Output(id("tab3_process_table"), "data"),
    Output(id("ongoing-tabs"), "value"),
    Input(id("tab3_submit"), "n_clicks"),
    State(id("tab3_process_table"), "multiRowsClicked"),
    State(id("tab3_process_table"), "data"),
)
def tab3_spec_process_submit(n_clicks, data, all_data):
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需更新的记录", "error"), no_update

    now = datetime.now()

    df = pd.DataFrame(data)
    # dff = pd.DataFrame(all_data)

    df1 = df.loc[df["status"] == "process"]
    df2 = df.loc[df["status"] == "release"]
    df3 = df.loc[df["status"] == "follow_up"]
    df4 = df.loc[df["status"] == "canceled"]

    conn = pool.connection()
    cu = conn.cursor()

    if not df1.empty:
        df1 = df1.replace({"": None}).replace({np.nan: None})
        sql = "update ssp_spec.task set model=%s,owner=%s,dept=%s,doc_type=%s,\
            ecn_no=%s,qty=%s,running_date=%s,remark=%s where id=%s"
        params = df1[
            [
                "model",
                "owner",
                "dept",
                "doc_type",
                "ecn_no",
                "qty",
                "running_date",
                "remark",
                "id",
            ]
        ].values.tolist()
        cu.executemany(sql, params)
        conn.commit()

    if not df2.empty:
        # release
        df2["act_release_lt"] = 1
        df2["std_release_lt"] = (
            df2["std_release_lt"].replace("", 0).fillna(0).astype(int)
        )
        c1 = df2["doc_type"].isin(
            ["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE", "COST QUERY"]
        )
        df2.loc[c1, "release_date"] = now

        df2["input_date"] = pd.to_datetime(df2["input_date"], errors="coerce")
        df2["release_date"] = pd.to_datetime(df2["release_date"], errors="coerce")

        df2["act_release_lt"] = df2["release_date"] - df2["input_date"]

        df2["holiday"] = df2.apply(
            lambda x: get_holidays(
                x["input_date"],
                x["release_date"]
                if x["release_date"] is not pd.NaT
                else x["input_date"],
            ),
            axis=1,
        )
        df2["holiday"] = df2["holiday"].apply(lambda x: pd.offsets.Day(len(x)))
        df2["act_release_lt"] = df2["act_release_lt"] - df2["holiday"]
        df2["act_release_lt"] = (
            df2["act_release_lt"].apply(lambda x: x.ceil("D")).dt.days
        )

        c1 = df2["act_release_lt"] <= df2["std_release_lt"]
        c2 = df2["act_release_lt"] > df2["std_release_lt"]
        df2.loc[c1, "release_ontime"] = "Y"
        df2.loc[c2, "release_ontime"] = "N"

        df2 = df2.replace({"": None}).replace({np.nan: None})
        sql = "update ssp_spec.task set\
              status=%s,model=%s,owner=%s,dept=%s,doc_type=%s,\
              ecn_no=%s,qty=%s,running_date=%s,release_date=%s,\
              remark=%s,act_release_lt=%s,release_ontime=%s \
              where id=%s"
        params = df2[
            [
                "status",
                "model",
                "owner",
                "dept",
                "doc_type",
                "ecn_no",
                "qty",
                "running_date",
                "release_date",
                "remark",
                "act_release_lt",
                "release_ontime",
                "id",
            ]
        ].values.tolist()
        cu.executemany(sql, params)
        conn.commit()
        # dff["release_date"] = np.where(
        #     dff["id"].isin(df2["id"]), now, dff["release_date"]
        # )

    if not df3.empty:
        # followup
        df3["followup_date"] = None
        df3["act_release_lt"] = 1
        df3["std_release_lt"] = (
            df3["std_release_lt"].replace("", 0).fillna(0).astype(int)
        )
        df3["followup_lt"] = df3["followup_lt"].replace("", 0).fillna(0).astype(int)

        c1 = df3["doc_type"].isin(
            ["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE", "COST QUERY"]
        )
        df3["followup_date"] = np.where(c1, now, df3["followup_date"])

        c1 = df3["release_date"].isna()
        df3["release_date"] = np.where(c1, now, df3["release_date"])

        df3["release_date"] = pd.to_datetime(df3["release_date"], errors="coerce")
        df3["input_date"] = pd.to_datetime(df3["input_date"], errors="coerce")
        df3["followup_date"] = pd.to_datetime(df3["followup_date"], errors="coerce")

        # 计算 act_release_lt 去掉节假日
        df3["act_release_lt"] = df3["release_date"] - df3["input_date"]
        df3["holiday"] = df3.apply(
            lambda x: get_holidays(
                x["input_date"],
                x["release_date"]
                if x["release_date"] is not pd.NaT
                else x["input_date"],
            ),
            axis=1,
        )
        df3["holiday"] = df3["holiday"].apply(lambda x: pd.offsets.Day(len(x)))
        df3["act_release_lt"] = df3["act_release_lt"] - df3["holiday"]
        df3["act_release_lt"] = (
            df3["act_release_lt"].apply(lambda x: x.ceil("D")).dt.days
        )

        # 计算 act_followup_lt 去掉节假日
        df3["act_followup_lt"] = df3["followup_date"] - df3["release_date"]
        df3["holiday"] = df3.apply(
            lambda x: get_holidays(
                x["release_date"],
                x["followup_date"] if not not pd.NaT else x["release_date"],
            ),
            axis=1,
        )
        df3["holiday"] = df3["holiday"].apply(lambda x: pd.offsets.Day(len(x)))
        df3["act_followup_lt"] = df3["act_followup_lt"] - df3["holiday"]
        df3["act_followup_lt"] = (
            df3["act_followup_lt"].apply(lambda x: x.ceil("D")).dt.days
        )

        # *act_followup_lt最小值为1
        df3["act_followup_lt"] = np.where(
            df3["act_followup_lt"] == 0, 1, df3["act_followup_lt"]
        )

        c1 = df3["doc_type"].isin(["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE"])
        c2 = df3["act_release_lt"] <= df3["std_release_lt"]
        c3 = df3["act_release_lt"] > df3["std_release_lt"]
        df3.loc[c1 & c2, "release_ontime"] = "Y"
        df3.loc[c1 & c3, "release_ontime"] = "N"

        c1 = df3["doc_type"].isin(["SM", "ECN", "MODIFY ISSUE", "NEW ISSUE"])

        c2 = df3["act_followup_lt"] <= df3["followup_lt"]
        c3 = df3["act_followup_lt"] > df3["followup_lt"]
        df3.loc[c1 & c2, "followup_ontime"] = "Y"
        df3.loc[c1 & c3, "followup_ontime"] = "N"

        df3 = df3.replace({"": None}).replace({np.nan: None})
        sql = "update ssp_spec.task set\
              status=%s,model=%s,owner=%s,dept=%s,doc_type=%s,\
              ecn_no=%s,qty=%s,running_date=%s,release_date=%s,followup_date=%s,\
              remark=%s,act_release_lt=%s,act_followup_lt=%s,followup_ontime=%s,release_ontime=%s \
              where id=%s"
        params = df3[
            [
                "status",
                "model",
                "owner",
                "dept",
                "doc_type",
                "ecn_no",
                "qty",
                "running_date",
                "release_date",
                "followup_date",
                "remark",
                "act_release_lt",
                "act_followup_lt",
                "followup_ontime",
                "release_ontime",
                "id",
            ]
        ].values.tolist()
        cu.executemany(sql, params)
        conn.commit()
        # dff = dff.loc[~dff["id"].isin(df3["id"])]

    if not df4.empty:
        # *cancel
        # *状态CANCEL，需清空release_date，act_release_lt，release_ontime，ECN_QTY,work_time_minute\
        # *act_followup_lt,followup_ontime都应该为空白
        df4 = df4.replace({"": None}).replace({np.nan: None})
        df4["release_date"] = None
        df4["act_release_lt"] = None
        df4["release_ontime"] = None
        df4["ecn_qty"] = None
        df4["work_time_minute"] = None
        df4["act_followup_lt"] = None
        df4["followup_ontime"] = None
        df4["psl_qty"] = None
        sql = "update ssp_spec.task set\
              status=%s,model=%s,owner=%s,dept=%s,doc_type=%s,ecn_no=%s,qty=%s,running_date=%s,\
              remark=%s,release_date=%s,act_release_lt=%s,release_ontime=%s,\
            ecn_qty=%s,work_time_minute=%s,act_followup_lt=%s,followup_ontime=%s,psl_qty=%s \
            where id=%s"
        params = df4[
            [
                "status",
                "model",
                "owner",
                "dept",
                "doc_type",
                "ecn_no",
                "qty",
                "running_date",
                "remark",
                "release_date",
                "act_release_lt",
                "release_ontime",
                "ecn_qty",
                "work_time_minute",
                "act_followup_lt",
                "followup_ontime",
                "psl_qty",
                "id",
            ]
        ].values.tolist()
        cu.executemany(sql, params)
        conn.commit()
        # dff = dff.loc[~dff["id"].isin(df4["id"])]

    cu.close()
    conn.close()

    #! 只去掉选中行其中的cancel和followup
    return notice("更新成功", "success"), "2"


@callback(
    Output(id("tab3_process_table"), "data"),
    Input(id("tab3_process_table"), "cellEdited"),
    State(id("tab3_process_table"), "data"),
)
def tab3_nt_name_update_dept(new_data, data):
    if new_data is None:
        raise PreventUpdate
    if new_data.get("column") != "owner":
        raise PreventUpdate

    conn = pool.connection()
    cu = conn.cursor()
    params = [new_data["row"]["owner"]]
    sql = "select dept from ssp.user where nt_name=%s"
    cu.execute(sql, params)
    res = cu.fetchone()
    cu.close()
    conn.close()
    if not res:
        raise PreventUpdate

    dept = res.get("dept")

    id = new_data["row"]["id"]
    df = pd.DataFrame(data)
    df["dept"] = np.where(df["id"] == id, dept, df["dept"])

    return df.to_dict(orient="records")


# -----时效index提交------------------
@callback(
    Output("global-notice", "children"),  # app中layout写的全局notice
    Output(id("index_table"), "data"),
    Input(id("index_submit"), "n_clicks"),
    State(id("index_table"), "columns"),
    State(id("index_table"), "data"),
    State(id("month"), "value"),
)
def index_table_submit(n_clicks, columns, data, month):
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if df.empty:
        return notice("表格内容不能空", "error"), no_update
    columns = [i["id"] for i in columns]
    df = df.reindex(columns=columns)
    df["work_days"] = df["work_days"].astype(float)
    df["qty_total"] = df["qty_total"].astype(float)
    df["month"] = pd.to_datetime(month, errors="coerce")
    df["spec"] = df["spec"].str.strip().str.title()

    conn = pool.connection()
    cu = conn.cursor()

    sql1 = "insert into ssp_spec.index(spec,work_days,qty_delay,\
        qty_on_schedule,qty_total,ratio,month) value(%s,%s,%s,%s,%s,%s,%s)"

    columns = [
        "spec",
        "work_days",
        "qty_delay",
        "qty_on_schedule",
        "qty_total",
        "ratio",
        "month",
    ]

    df = df.reindex(columns=columns)
    df = df.replace({"": None, np.nan: None})
    params = df.values.tolist()
    cu.executemany(sql1, params)
    conn.commit()
    cu.close()
    conn.close()
    return notice("提交成功", "success"), [{}]


# ---------自动校验EE姓名
# sp-ecn-ee
@callback(
    Output("sp-ee", "options"), [Input("user", "data")], prevent_initial_call=False
)
@db_session
def get_sp_ee_options(data):
    doc_type = "ECN"
    due_day = Due_day.select(lambda d: d.doc_type == doc_type)[:]
    dept_id = [i.dept_id for i in due_day]
    dept_id = list(set(dept_id))
    role_group = ["ee", "me"]
    user = User.select(lambda u: u.dept_id in dept_id and u.role_group in role_group)[:]
    nt_name = [i.nt_name.lower() for i in user]
    source = [{"label": i, "value": i} for i in nt_name]
    return source


@callback(
    Output("sp-dept", "value"), [Input("sp-ee", "value")], prevent_initial_call=False
)
@db_session
def default_runningchange(ee):
    if ee:
        user = User.get(nt_name=ee)
        return user.dept
    else:
        return ""


@callback(
    [
        Output("sp-proj", "value"),
        Output("sp-ee", "value"),
        Output("sp-type", "value"),
        Output("sp-projno", "value"),
        Output("sp-qty", "value"),
        Output("sp-urgent", "value"),
        Output("sp-rundate", "date"),
        Output("sp-modal", "is_open"),
        Output("sp-modal-header", "children"),
        Output("sp-modal-body", "children"),
        Output("sp-modal-close", "color"),
    ],
    [Input("sp-submit", "n_clicks"), Input("sp-modal-close", "n_clicks")],
    [
        State("sp-proj", "value"),
        State("sp-ee", "value"),
        State("sp-type", "value"),
        State("sp-projno", "value"),
        State("sp-qty", "value"),
        State("sp-urgent", "value"),
        State("user", "data"),
        State("sp-dept", "value"),
        State("sp-rundate", "date"),
        State("sp-modal", "is_open"),
    ],
    prevent_initial_call=False,
)
@db_session
def submit_clear_sp_input(
    n, close, proj, ee, sm, projno, qty, urgent, user, dept, date, is_open
):
    if n or close:
        if all([proj, ee, sm, projno]):
            spec = user.get("nt_name")
            today = datetime.now()
            dept_id = User.get(nt_name=ee).dept_id
            conn = pool.connection()
            cu = conn.cursor()
            data = {
                "status": "accepted",
                "owner": spec,
                "model": proj,
                "ee": ee,
                "dept": dept,
                "doctype": sm,
                "ecnno": projno,
                "qty": qty,
                "input_date": today,
                "urgent": urgent,
                "running_date": date,
                "dept_id": dept_id,
            }
            df1 = pd.DataFrame(data, index=[0])
            fields = ",".join(df1.columns)
            params1 = df1.values.tolist()
            ph = ",".join(["%s"] * df1.columns.size)
            sql1 = f"insert into ssp_spec.ecn_sm ({fields}) values({ph})"
            cu.executemany(sql1, params1)
            conn.commit()
            cu.close()
            conn.close()
            return (
                "",
                "",
                "",
                "",
                "",
                "",
                None,
                not is_open,
                "Success!",
                "提交成功！",
                "success",
            )
        else:
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                not is_open,
                "Error!",
                "提交失败，请将必填项补充完整！",
                "danger",
            )
    else:
        raise PreventUpdate


# --------------------新任务----------------------------
def get_value(df, key, i):
    return df.loc[(df.id.isin([i])), key].tolist()[0]


@db_session
def update_task(spec):
    dept_id = Duty.select(lambda d: d.owner == spec)[:]
    if not dept_id:
        return [{}]
    dept_id = [i.dept_id for i in dept_id]
    # sql = 'select t.* from ssp_spec.task t left join ssp_spec.modification m \
    #     on t.id=m.task_id where m.task_id is null and dept_id in %s'
    sql = "select m.id,m.status,t.owner,m.model,m.ee,m.me,t.customer,t.doc_type,\
        t.input_date,m.request_date,t.second_source,m.remark,m.attachment,t.dept,\
        m.task_id,t.other_address,\
        t.upload_address from ssp_spec.modification m left join ssp_spec.task t\
        on m.task_id=t.id where dept_id in %s"
    params = [dept_id]
    df = read_sql(sql, params=params)

    df.sort_values(["task_id", "id"], ascending=[1, 0], inplace=True)
    df = df.groupby(["task_id"]).head(1)
    df = df[df["status"] == "open"]

    df["request_date"] = pd.to_datetime(
        df["request_date"], errors="coerce"
    ).dt.strftime("%Y-%m-%d")
    df["input_date"] = pd.to_datetime(df["input_date"], errors="coerce").dt.strftime(
        "%Y-%m-%d"
    )
    df["filename"] = df["attachment"].apply(lambda x: x.rsplit("/", 1)[1] if x else "")
    df["remark"] = (
        df["remark"]
        + ";上传地址("
        + df["upload_address"]
        + ","
        + df["other_address"]
        + ")"
    )
    df = df.to_dict("records")
    return df


@callback(
    Output("sp-task", "data"),
    Input("sp-task-accept", "n_clicks"),
    State("sp-task", "multiRowsClicked"),
    State("user", "data"),
    prevent_initial_call=False,
)
@db_session
def new_task(accpet, rows, user):
    spec = user.get("nt_name").lower()
    if rows:
        dfr = pd.DataFrame(rows)
        dfr_status = dfr["status"].values
        dfr["modified_date"] = datetime.now()
        dfr["user"] = spec
        # task_id = dfr['task_id'].tolist()
        ctx = callback_context
        id = ctx.triggered[0]["prop_id"].split(".")[0]
        if id == "sp-task-accept" and (dfr_status == "open").all():
            dfx = dfr[
                [
                    "task_id",
                    "status",
                    "user",
                    "modified_date",
                    "remark",
                    "attachment",
                    "model",
                    "ee",
                    "me",
                    "request_date",
                ]
            ]
            dfx["status"] = "accepted"
            dfx["spec"] = dfx["user"]
            dfx["current"] = dfx["user"]
            # dic = dfx.to_dict()
            con = pool.connection()
            cu = con.cursor()
            fields = ",".join(dfx.columns)
            params = dfx.values.tolist()
            ph = ",".join(["%s"] * dfx.columns.size)
            sql = f"insert into ssp_spec.modification ({fields}) values({ph})"
            cu.executemany(sql, params)
            # params1 = [(spec , i) for i in task_id]
            # sql1 = 'update ssp_spec.task set spec=%s where id=%s'
            # cu.executemany(sql1,params1)
            con.commit()
            cu.close()
            con.close()
            # db.insert('Modification',**dic)
            return update_task(spec)
    else:
        return update_task(spec)


# ---------------ECN&SM处理中-------------------
# 比较实际天数与实际交期
def is_ontime(original, dueday):
    data = []
    for index, item in enumerate(original):
        data.append("Y") if item < dueday[index] else data.append("N")
    return data


@callback(
    Output("sp-processing", "columns"),
    Input("user", "data"),
    State("sp-processing", "columns"),
    prevent_initial_call=False,
)
def sp_processing_columns(user, columns):
    title = [
        "",
        "Status",
        "是否急件",
        "机种名称",
        "EE",
        "部门",
        "文件类别",
        "单号/项目号",
        "Qty",
        "RunningDate",
        "InputDate",
        "ReleaseDate",
        "备注",
        "id",
        "dept_id",
    ]
    field = [
        "",
        "status",
        "urgent",
        "model",
        "ee",
        "dept",
        "doctype",
        "ecnno",
        "qty",
        "running_date",
        "input_date",
        "release_date",
        "remark",
        "id",
        "dept_id",
    ]
    columns = [{"title": i, "field": j} for i, j in zip(title, field)]
    editor = {
        "urgent": "select",
        "ee": "input",
        "doctype": "select",
        "model": "input",
        "ecnno": "input",
        "qty": "number",
        "running_date": ns("dateEditor"),
        "remark": "textarea",
    }
    for i in columns:
        if i["field"] in [
            "urgent",
            "ee",
            "doctype",
            "model",
            "ecnno",
            "qty",
            "running_date",
        ]:
            i["editor"] = editor.get(i["field"])
        if i["field"] == "urgent":
            i["editorParams"] = ["Y", "N"]
        elif i["field"] == "doctype":
            i["editorParams"] = ["SM", "ECN"]
        elif i["field"] in ["id", "dept_id"]:
            i["visible"] = False
    columns[0].update(
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
            "headerHozAlign": "center",
            "width": "1px",
        }
    )
    return columns


def is_valid_date(str):
    try:
        time.strptime(str, "%Y-%m-%d")
        return True
    except:
        return False


@callback(
    Output("sp-processing", "data"),
    Output("sp-modal-page3", "is_open"),
    Output("sp-page3-header", "children"),
    Output("sp-page3-body", "children"),
    Output("sp-page3-close", "color"),
    Input("sp-release", "n_clicks"),
    Input("sp-reject", "n_clicks"),
    Input("sp-followup", "n_clicks"),
    Input("sp-save", "n_clicks"),
    Input("sp-page3-close", "n_clicks"),
    State("sp-processing", "multiRowsClicked"),
    State("sp-processing", "data"),
    State("user", "data"),
    State("sp-modal-page3", "is_open"),
    prevent_initial_call=False,
)
@db_session
def sp_sup_table1_data(
    n_release, n_reject, n_followup, n_save, close, rows, data, user, is_open
):
    if data:
        dff = pd.DataFrame(data)
    else:
        raise PreventUpdate
    today = datetime.now()
    nt_name = user.get("nt_name")
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if rows:
        dfr = pd.DataFrame(rows)
        dfr_status = dfr["status"].values
        dfr_id = dfr["id"].tolist()
        if id == "sp-release":
            if (dfr_status == "").all():
                input_date = Ecn_sm.select(lambda e: e.id in dfr_id)[:]
                actlt = []
                for i in input_date:
                    days = get_workday_offset(i.input_date, today)
                    actlt.append(days)
                rel_deptid = [i.dept_id for i in input_date]  # 都改成dept_id
                rel_doctype = [i.doctype for i in input_date]
                due_day = Due_day.select(
                    lambda d: d.doc_type in rel_doctype and d.dept_id in rel_deptid
                )[:]
                due_day = [i.due_day for i in due_day]
                rel_ontime = is_ontime(actlt, due_day)
                status = "Submitted"
                con = pool.connection()
                cu = con.cursor()
                params1 = [
                    (status, today, actlt[index], rel_ontime[index], item)
                    for index, item in enumerate(dfr_id)
                ]
                sql1 = "update ssp_spec.ecn_sm set status=(%s),release_date=(%s),actlt=(%s),release_ontime=(%s) where id=(%s)"
                cu.executemany(sql1, params1)
                con.commit()
                cu.close()
                con.close()
            else:
                return (
                    no_update,
                    not is_open,
                    "Error!",
                    "变更状态失败，请选择状态为空的行!",
                    "danger",
                )
        elif id == "sp-reject":
            if (dfr_status == "").all():
                status = "Cancel"
                con = pool.connection()
                cu = con.cursor()
                params1 = [(status, i) for i in dfr_id]
                sql1 = "update ssp_spec.ecn_sm set status=(%s) where id=(%s)"
                cu.executemany(sql1, params1)
                con.commit()
                cu.close()
                con.close()
            else:
                return (
                    no_update,
                    not is_open,
                    "Error!",
                    "变更状态失败，请选择状态为空的行！",
                    "danger",
                )
        elif id == "sp-followup":
            list_qty = dfr.loc[:, "qty"].tolist()  # dfr->选中行的qty不能为空
            if (dfr_status == "Release").all() and "" not in list_qty:
                release_date = Ecn_sm.select(lambda e: e.id in dfr_id)[:]
                followup_lt = []
                for i in release_date:
                    days = get_workday_offset(i.release_date, today)
                    followup_lt.append(days)
                fol_ontime = is_ontime(followup_lt, [10] * len(followup_lt))
                status = "Close"
                con = pool.connection()
                cu = con.cursor()
                params1 = [
                    (status, today, followup_lt[index], fol_ontime[index], item)
                    for index, item in enumerate(dfr_id)
                ]
                sql1 = "update ssp_spec.ecn_sm set status=(%s),followup_date=(%s),followup_lt=(%s),followup_ontime=(%s) where id=(%s)"
                cu.executemany(sql1, params1)
                con.commit()
                cu.close()
                con.close()
            else:
                if "" in list_qty:
                    return (
                        no_update,
                        not is_open,
                        "Error!",
                        "变更状态失败，Qty不能为空！",
                        "danger",
                    )
                else:
                    return (
                        no_update,
                        not is_open,
                        "Error!",
                        "变更状态失败，请选择状态为Release的行！",
                        "danger",
                    )
        else:
            status = ""
    if id == "sp-save":
        if ~dff[["model", "ee", "dept", "doctype", "ecnno"]].isin([None]).any().any():
            dff_urgent = dff.loc[:, "urgent"].tolist()
            dff_model = dff.loc[:, "model"].tolist()
            dff["ee"] = dff["ee"].str.lower()
            dff_ee = dff.loc[:, "ee"].tolist()
            dff_dept = dff.loc[:, "dept"].tolist()
            dff_type = dff.loc[:, "doctype"].tolist()
            dff_no = dff.loc[:, "ecnno"].tolist()
            dff_qty = dff.loc[:, "qty"].tolist()
            dff_run = dff.loc[:, "running_date"].tolist()
            dff_remark = dff.loc[:, "remark"].tolist()
            dff_deptid = dff.loc[:, "dept_id"].tolist()
            dff_id = dff.loc[:, "id"].tolist()
            con_date = True
            for date in dff_run:
                if not is_valid_date(date) and date:
                    con_date = False
                    return (
                        no_update,
                        not is_open,
                        "Error!",
                        "保存失败，请填写正确的时间格式！",
                        "danger",
                    )
                else:
                    con_date = True
            con_name = True
            sql = "select id,ee from ssp_spec.ecn_sm \
                where status in (%s,%s) and owner=%s"
            params = ["Accepted", "Submitted", nt_name]
            df_old = read_sql(sql, params=params)
            df_old = df_old.sort_values(by="id")
            old_name = df_old.loc[:, "ee"].tolist()
            df_new = dff.loc[:, ["id", "ee"]]
            df_new = df_new.sort_values(by="id")
            new_name = df_new.loc[:, "ee"].tolist()
            df_diff = [x for x in new_name if x not in old_name]
            if len(df_diff) != 0:
                db_user = User.select(lambda u: u.nt_name in df_diff)[:]
                db_nt_name = [i.nt_name.lower() for i in db_user]
                if not len(db_nt_name):
                    con_name = False
                    return (
                        no_update,
                        not is_open,
                        "Error!",
                        "保存失败，请输入正确EE姓名！",
                        "danger",
                    )
                else:
                    db_dept = [i.dept for i in db_user]
                    db_dept_id = [i.dept_id for i in db_user]
                    for index, item in enumerate(db_nt_name):
                        dff.loc[dff.ee.isin([item]), "ee"] = db_nt_name[index]
                        dff.loc[dff.ee.isin([item]), "dept"] = db_dept[index]
                        dff.loc[dff.ee.isin([item]), "dept_id"] = db_dept_id[index]
                    dff_ee = dff.loc[:, "ee"].tolist()
                    dff_dept = dff.loc[:, "dept"].tolist()
                    dff_deptid = dff.loc[:, "dept_id"].tolist()
                    con_name = True
            # -------------------------------------------------------
            if con_date and con_name:
                con = pool.connection()
                cu = con.cursor()
                params = [
                    (
                        dff_urgent[i],
                        dff_model[i],
                        dff_ee[i],
                        dff_dept[i],
                        dff_type[i],
                        dff_no[i],
                        dff_qty[i],
                        dff_run[i],
                        dff_remark[i],
                        dff_deptid[i],
                        dff_id[i],
                    )
                    for i in range(0, len(dff_id))
                ]
                sql = "update ssp_spec.ecn_sm set urgent=(%s),model=(%s),ee=(%s),dept=(%s),\
                doctype=(%s),ecnno=(%s),qty=(%s),running_date=(%s),remark=(%s),dept_id=(%s) where id=(%s)"
                cu.executemany(sql, params)
                con.commit()
                cu.close()
                con.close()
        else:
            return (
                no_update,
                not is_open,
                "Error!",
                "保存失败，必填项不能为空！",
                "danger",
            )
    sql = "select * from ssp_spec.ecn_sm where status in (%s,%s) and owner=%s"
    params = ["Accepted", "Submitted", nt_name]
    df = read_sql(sql, params=params)
    df = df.loc[
        :,
        (
            "id",
            "status",
            "urgent",
            "model",
            "ee",
            "dept",
            "doctype",
            "ecnno",
            "qty",
            "running_date",
            "remark",
            "input_date",
            "release_date",
            "dept_id",
        ),
    ]
    # df=df.rename({'urgent': '是否急件','Model': '机种名称','Dept': '部门','DocType':'文件类别','ECNNo':'单号/项目号','Remark':'备注'},axis=1)
    # df['编辑'] = 'edit'
    df.loc[df["status"] == "Accepted", "status"] = ""
    df.loc[df["status"] == "Submitted", "status"] = "Release"
    df["running_date"] = pd.to_datetime(
        df["running_date"], errors="coerce"
    ).dt.strftime("%Y-%m-%d")
    df["input_date"] = pd.to_datetime(df["input_date"], errors="coerce").dt.strftime(
        "%Y-%m-%d"
    )
    df["release_date"] = pd.to_datetime(
        df["release_date"], errors="coerce"
    ).dt.strftime("%Y-%m-%d")
    # df['FollowupDate'] = pd.to_datetime(df['FollowupDate'], errors='coerce').dt.strftime('%Y-%m-%d %H:%M:%S')
    df = df.reindex(
        columns=[
            "status",
            "urgent",
            "model",
            "ee",
            "dept",
            "doctype",
            "ecnno",
            "qty",
            "running_date",
            "input_date",
            "release_date",
            "remark",
            "id",
            "dept_id",
        ]
    )
    df = df.to_dict("records")
    if n_release or n_reject or n_followup or n_save or close:
        if close:
            return df, not is_open, "Success!", "变更状态成功！", "success"
        else:
            return df, not is_open, "Success!", "变更状态成功！", "success"
    else:
        return df, False, "", "", ""


# ---------------ECN&SM 查询-------------------------------------
@callback(
    Output("sp-ecn-query", "columns"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def get_query_columns(user):
    title = [
        "是否急件",
        "机种名称",
        "工程师",
        "部门",
        "文件类别",
        "单号/项目号",
        "Qty",
        "Running change",
        "备注",
        "InputDate",
        "ReleaseDate",
        "FollowupDate",
        "Owner",
        "ECN实际交期",
        "是否准时完成",
        "Follow Up实际交期",
        "是否准时跟进",
        "id",
        "dept_id",
    ]
    field = [
        "urgent",
        "model",
        "ee",
        "dept",
        "doctype",
        "ecnno",
        "qty",
        "running_date",
        "remark",
        "input_date",
        "release_date",
        "followup_date",
        "owner",
        "actlt",
        "release_ontime",
        "followup_lt",
        "followup_ontime",
        "id",
        "dept_id",
    ]
    columns = [{"title": i, "field": j} for i, j in zip(title, field)]
    for col in columns:
        if col["field"] == "running_date":
            col["editor"] = ns("dateEditor")
        elif col["field"] == "remark":
            col["editor"] = "textarea"
        if col["field"] not in ["id", "dept_id"]:
            col["headerMenu"] = ns("headerMenu")
        else:
            col["visible"] = False
    return columns


@callback(
    Output("sp-ecn-query", "data"),
    Output("sp-modal-page4", "is_open"),
    Output("sp-page4-header", "children"),
    Output("sp-page4-body", "children"),
    Output("sp-page4-close", "color"),
    Input("sp-ecn-query-btn", "n_clicks"),
    Input("sp-ecn-query-save", "n_clicks"),
    Input("sp-page4-close", "n_clicks"),
    State("sp-ecn-query", "data"),
    State("sp-modal-page4", "is_open"),
    State("sp-ecn-query-model", "value"),
    State("sp-ecn-inputdate", "start_date"),
    State("sp-ecn-inputdate", "end_date"),
    State("sp-ecn-releasedate", "start_date"),
    State("sp-ecn-releasedate", "end_date"),
    prevent_initial_call=False,
)
@db_session
def search_query_data(
    n1,
    n2,
    close,
    table_data,
    is_open,
    model,
    input_start=None,
    input_end=None,
    relea_start=None,
    relea_end=None,
):
    ctx = callback_context
    n_clicks = ctx.triggered[0]["prop_id"].split(".")[0]
    if n_clicks == "sp-ecn-query-btn":
        min_datetime = datetime(1900, 1, 1, 0, 0, 0)
        max_datetime = datetime(2200, 1, 1, 0, 0, 0)
        model = "" if model is None else model
        input_start = (
            min_datetime
            if input_start is None
            else datetime.strptime(input_start, "%Y-%m-%d")
        )
        input_end = (
            max_datetime
            if input_end is None
            else datetime.strptime(input_end, "%Y-%m-%d") + timedelta(days=1)
        )
        relea_start = (
            min_datetime
            if relea_start is None
            else datetime.strptime(relea_start, "%Y-%m-%d")
        )
        relea_end = (
            max_datetime
            if relea_end is None
            else datetime.strptime(relea_end, "%Y-%m-%d") + timedelta(days=1)
        )
        db_ecnsm = Ecn_sm.select(
            lambda e: model in e.model
            and e.input_date > input_start
            and e.input_date < input_end
            and e.release_date > relea_start
            and e.release_date < relea_end
        )[:]
        data = [i.to_dict() for i in db_ecnsm]
        df = pd.DataFrame(data)
        for i in ["input_date", "running_date", "release_date", "followup_date"]:
            df[i] = pd.to_datetime(df[i], errors="coerce").dt.strftime("%Y-%m-%d")
        return df.to_dict("records"), False, "", "", ""
    elif n_clicks == "sp-ecn-query-save":
        if table_data:
            df = pd.DataFrame(table_data)
            if "Invalid date" in df["running_date"].values:
                return (
                    no_update,
                    not is_open,
                    "Error!",
                    "时间不能为Invalid date！",
                    "danger",
                )
            else:
                df_running_date = df.loc[:, "running_date"].tolist()
                df_remark = df.loc[:, "remark"].tolist()
                df_id = df.loc[:, "id"].tolist()
                con = pool.connection()
                cu = con.cursor()
                params = [
                    (df_running_date[i], df_remark[i], df_id[i])
                    for i in range(0, len(df_id))
                ]
                sql = "update ssp_spec.ecn_sm set running_date=(%s),remark=(%s) where id=(%s)"
                cu.executemany(sql, params)
                con.commit()
                cu.close()
                con.close()
                return table_data, not is_open, "Success!", "变更成功！", "success"
    elif n_clicks == "sp-page4-close":
        return no_update, False, "", "", ""
    else:
        raise PreventUpdate


# -----------------------RD处理中---------------------------------


@callback(
    Output("sp-task-solve", "data"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def get_sp_task_solve_data(user):
    spec = user.get("nt_name").lower()
    sql = "select m.spec,m.modified_date,m.status,t.remark,m.attachment,t.input_date,\
        t.owner,t.upload_address,t.other_address,\
        m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source,m.task_id,m.id\
    from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id \
        where m.spec=%s and m.status in %s \
            and m.id in (select max(id) from ssp_spec.modification group by task_id)"
    params = [spec, ["accepted", "modify_ee", "modify_me"]]
    df = read_sql(sql, params=params)
    # df.sort_values(["task_id", "id"], ascending=False, inplace=True)
    # df = df.groupby(["task_id"]).head(1)
    # df = df[df["spec"] == spec]
    # df = df.loc[df.status.isin(["accepted", "modify_ee", "modify_me"])]
    if df.empty:
        return []  # df.to_dict("records")
    df["remark"] = (
        df["remark"]
        + ";上传地址("
        + df["upload_address"]
        + ","
        + df["other_address"]
        + ")"
    )
    df["request_date"] = pd.to_datetime(
        df["request_date"], errors="coerce"
    ).dt.strftime("%Y-%m-%d")
    df["input_date"] = pd.to_datetime(df["input_date"], errors="coerce").dt.strftime(
        "%Y-%m-%d"
    )
    f = lambda x: f"/spec?page=processing2#{x['task_id']}"
    df["model_url"] = df.apply(f, axis=1)
    df = repalce(df)
    df = df.to_dict("records")
    return df


@callback(
    Output("sp-ee-model", "children"),
    Output("sp-ee-status", "children"),
    Output("sp-ee-doc", "children"),
    Output("sp-ee-custom", "children"),
    Output("sp-ee-dept", "children"),
    Output("sp-ee-owner", "children"),
    Output("sp-ee-ee", "children"),
    Output("sp-ee-me", "children"),
    Output("sp-ee-spec", "children"),
    Output("sp-ee-date", "children"),
    Output("sp-ee-second", "children"),
    Output("sp-ee-remark", "children"),
    Output("sp-ee-attach", "children"),
    Input("url", "hash"),
    State("user", "data"),
    prevent_initial_call=False,
)
def spe_ee_edit(urlhash, user):
    # role = user.get('role_group').lower()
    if urlhash:
        id = urlhash.split("#")[1]
        sql = "select m.spec,m.modified_date,m.status,m.remark,m.attachment,t.id,\
        t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source\
        from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id where m.task_id=%s \
        order by m.id desc limit 1"
        params = [id]
        df = read_sql(sql, params=params)
        df = df.loc[df["status"].isin(["accepted", "modify_ee", "modify_me"])]
        if df.empty:
            raise PreventUpdate

        # df = df.sort_values(by=["modified_date"], ascending=False)
        # df = df.drop_duplicates(["id"])
        # df = df.loc[
        #     df.status.isin(["accepted", "modify_ee", "modify_me"]),
        #     (
        #         "status",
        #         "doc_type",
        #         "model",
        #         "customer",
        #         "owner",
        #         "ee",
        #         "me",
        #         "spec",
        #         "request_date",
        #         "second_source",
        #         "remark",
        #         "attachment",
        #         "dept",
        #     ),
        # ]
        df["request_date"] = pd.to_datetime(
            df["request_date"], errors="coerce"
        ).dt.strftime("%Y-%m-%d")
        df["attachment"] = df["attachment"].apply(
            lambda x: x.rsplit("/", 1)[1] if x else ""
        )
        df = df.reindex(
            columns=[
                "model",
                "status",
                "doc_type",
                "customer",
                "dept",
                "owner",
                "ee",
                "me",
                "spec",
                "request_date",
                "second_source",
                "remark",
                "attachment",
            ]
        )
        df = repalce(df)
        tuples = tuple(sum(df.loc[0:].values.tolist(), []))
        return tuples
    else:
        raise PreventUpdate


# ---------------下载附件--------------------------
@callback(
    Output("sp-ee-download", "data"),
    [Input("sp-ee-attach", "n_clicks")],
    [State("url", "hash")],
    prevent_initial_call=False,
)
def get_sp_download_data(n, hash):
    # 应该抓取
    id = hash.split("#")[1]
    if n:
        sql = "select attachment,modified_date from ssp_spec.modification where task_id=%s"
        params = [id]
        df = read_sql(sql, params=params)
        df = df.sort_values(by=["modified_date"], ascending=False)
        df.index = range(len(df))
        url = df.loc[0, "attachment"]
        return dcc.send_file(url)


@callback(
    Output("sp-form", "children"),
    [Input("sp-ee-action", "value")],
    prevent_initial_call=False,
)
def get_sp_form_children(value):
    if value == 1:
        return [
            sp_ee_collapse_btn,
            sp_ee_collapse,
            sp_ee_remark_1,
            sp_ee_upload,
            sp_ee_submit_1,
            sp_ee_modal_1,
        ]
    elif value == 2:
        return [sp_ee_remark_2, sp_ee_submit_2, sp_ee_modal_2]
    elif value == 3:
        return [sp_form_spec, sp_ee_remark_3, sp_ee_submit_3, sp_ee_modal_3]


@callback(
    Output("sp-form-ee", "options"),
    Output("sp-form-me", "options"),
    Input("sp-eecol-btn", "n_clicks"),
    Input("sp-ee-owner", "children"),
    Input("sp-ee-action", "value"),
)
def get_sp_form_ee_options(n_clicks, owner, value):
    if value == 1:
        return sp_rd_name("EE", owner), sp_rd_name("ME", owner)
    else:
        raise PreventUpdate


@callback(
    Output("sp-eecol-body", "is_open"),
    [Input("sp-eecol-btn", "n_clicks")],
    [State("sp-eecol-body", "is_open")],
)
def sp_ee_open(n, is_open):
    if n:
        return not is_open
    return is_open


@callback(Output("sp-eecol-btn", "children"), [Input("sp-eecol-body", "is_open")])
def sp_ee_rotation(is_open):
    if is_open:
        return "-"
    else:
        return "+"


# -----------------核准------------------------------
# 返回非空值
def if_empty(before, after):
    if pd.isnull(before):
        before = None
    if not after:
        return before
    else:
        return after


@callback(
    Output("sp-form-model", "value"),
    Output("sp-form-ee", "value"),
    Output("sp-form-me", "value"),
    Output("sp-form-date", "date"),
    Output("sp-form-remark-1", "value"),
    Output("sp-form-upload", "fileNames"),
    Output("sp-ee-modal-window-1", "is_open"),
    Output("sp-ee-modal-header-1", "children"),
    Output("sp-ee-modal-body-1", "children"),
    Output("sp-ee-modal-close-1", "color"),
    Output("sp-ee-modal-close-1", "href"),
    Input("sp-ee-submit-1", "n_clicks"),
    Input("sp-ee-modal-close-1", "n_clicks"),
    State("sp-ee-action", "value"),
    State("sp-form-model", "value"),
    State("sp-form-ee", "value"),
    State("sp-form-me", "value"),
    State("sp-form-date", "date"),
    State("sp-form-remark-1", "value"),
    State("sp-form-upload", "fileNames"),
    State("sp-form-upload", "upload_id"),
    State("url", "hash"),
    State("user", "data"),
    State("sp-ee-modal-window-1", "is_open"),
    State("sp-ee-owner", "children"),
    State("sp-ee-doc", "children"),
    State("sp-ee-spec", "children"),
    State("sp-ee-ee", "children"),
    State("sp-ee-me", "children"),
    State("sp-ee-model", "children"),
)
@db_session
def sp_submitee(
    submit,
    close,
    action,
    model,
    ee,
    me,
    date,
    remark,
    file,
    uid,
    hash,
    user,
    is_open,
    pm,
    doc_type,
    spec,
    ee_before,
    me_before,
    model_before,
):
    nt_name = user.get("nt_name").lower()
    now = datetime.now()
    today = now.date()
    ee = ee or ee_before
    me = me or me_before
    model = model or model_before
    if file:
        filename = UPLOAD_FOLDER_ROOT / uid / file[0]
        filename = str(filename)
        filename = filename.replace("\\", "/")
    else:
        filename = ""
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if submit or close:
        if id == "sp-ee-submit-1" and action == 1:
            if filename:
                task_id = hash.split("#")[1]
                # id = hash.split('#')[1]
                sql = (
                    "select m.user,m.modified_date,m.status,m.remark,m.attachment,t.id,\
                    t.action_process,t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,\
                    m.request_date,t.second_source,t.input_date\
                    from ssp_spec.modification m \
                    left join ssp_spec.task t on m.task_id=t.id where m.task_id=%s \
                    order by m.id desc limit 1"
                )
                params = [task_id]
                df = read_sql(sql, params=params)
                df_mail = df.reindex(
                    columns=[
                        "doc_type",
                        "model",
                        "status",
                        "input_date",
                        "request_date",
                    ]
                )

                action_process = df["action_process"].tolist()[0]
                df = df.sort_values(by=["modified_date"], ascending=False)
                length = df.shape[0]  # 行数
                # df = df.drop_duplicates(["id"])
                df = df.loc[
                    df.status.isin(["accepted", "modify_ee", "modify_me"]),
                    ("model", "ee", "me", "request_date", "id", "status"),
                ]
                df["request_date"] = pd.to_datetime(
                    df["request_date"], errors="coerce"
                ).dt.strftime("%Y-%m-%d")
                df["request_date"] = df["request_date"].where(
                    df["request_date"].notnull(), None
                )
                df.index = range(len(df))  # 重置索引！
                para = {"model": model, "ee": ee, "me": me, "request_date": date}
                tup = []
                for key, value in para.items():
                    tup.append(if_empty(df.loc[0, key], value))
                if "modify_me" in df.values:
                    status = "submitted_me"
                    current = tup[2]
                    mail_status = "机构审核"
                else:
                    status = "submitted_ee"
                    mail_status = "电子审核"
                    current = tup[1]
                if action_process == "D":
                    status = "close"
                    mail_status = "流程结束"
                del df["status"]
                df_mail["status"] = mail_status
                df_mail = df_mail.rename(columns=col_dict)

                conn = pool.connection()
                cu = conn.cursor()
                params2 = [
                    (
                        task_id,
                        nt_name,
                        nt_name,
                        current,
                        today,
                        status,
                        remark,
                        filename,
                    )
                    + tuple(tup)
                ]
                sql2 = (
                    "insert into ssp_spec.modification (task_id,user,spec,current,modified_date,status,remark,attachment,model,ee,me,request_date)\
                        values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                )
                cu.executemany(sql2, params2)
                conn.commit()
                cu.close()
                conn.close()
                task = Task.get(id=task_id)
                input_date = task.input_date.date()
                dic_update = {}
                if length == 1:
                    submit_date = task.submit_date or now
                    request_date = task.request_date
                    dic_update["submit_date"] = submit_date
                    dic_update["act_submit_lt"] = (today - input_date).days
                    dic_update["std_submit_lt"] = task.std_submit_lt
                    dic_update["submit_ontime"] = (
                        "Y"
                        if submit_date.date()
                        <= (request_date - timedelta(days=1)).date()
                        else "N"
                    )
                # add release_date
                if status == "close":
                    # ===========close邮件start===========
                    to = ";".join(f"{i}@deltaww.com" for i in {spec, ee, me, pm})
                    subject = f"SSP-流程结束({doc_type}-{model})"
                    title = f"Dears,<br>SSP作业平台，{doc_type}-{model}已生效,请至SSP下载文件<br>"
                    href = "http://sup.deltaww.com/spec-ee?page=query"
                    body = df_to_html(df_mail, title=title, href=href)
                    bg_mail(to, subject, body)
                    # ===========close邮件end=============

                    dic_update["release_date"] = today
                    dic_update["act_release_lt"] = (today - input_date).days
                    # request_date 要抓两张表
                    # 查Modification表中最新非空request_date
                    due_day = Due_day.get(
                        dept_id=task.dept_id, doc_type=task.doc_type
                    ).due_day
                    sql = "select id, input_date, request_date from ssp_spec.task \
                        where id=%s and request_date is not null union all\
                        select task_id, modified_date, request_date \
                            from ssp_spec.modification\
                        where task_id=%s  and request_date is not null"
                    params = [task_id, task_id]
                    df = read_sql(sql, params=params)
                    if len(df) > 0:
                        request_date = df.values[-1][2]
                        if today <= request_date:
                            dic_update["release_ontime"] = "Y"
                        else:
                            dic_update["release_ontime"] = "N"
                    else:
                        if dic_update.get("act_release_lt") <= due_day:
                            dic_update["release_ontime"] = "Y"
                        else:
                            dic_update["release_ontime"] = "N"
                Task[task_id].set(**dic_update)
                tup_back = (
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    not is_open,
                    "success",
                    "提交成功！",
                    "success",
                    "/spec?page=processing",
                )
                # * ------邮件--------
                if status.lower() == "submitted_ee":
                    to = ee
                elif status.lower() == "submitted_me":
                    to = me
                else:
                    to = pm

                if status != "close":
                    to = f"{to}@deltaww.com;{nt_name}@deltaww.com"
                    subject = f"SSP-新任务({doc_type}-{model}),请确认签核"
                    title = "Dear Sir,<br>SSP作业平台，收到新任务，请确认并签核<br>"
                    href = f"http://sup.deltaww.com/spec-ee?page=solve2#{task_id}"
                    body = df_to_html(df_mail, title=title, href=href)
                    bg_mail(to, subject, body)

                return tup_back
            else:
                tup_back = (
                    None,
                    None,
                    None,
                    None,
                    None,
                    None,
                    not is_open,
                    "error",
                    "请上传附件！",
                    "danger",
                    "",
                )
                return tup_back
        else:
            tup_back = (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                not is_open,
                no_update,
                no_update,
                no_update,
                "",
            )
            return tup_back
    else:
        raise PreventUpdate


# -----------------退单------------------------------
@callback(
    [
        Output("sp-form-remark-2", "value"),
        Output("sp-ee-modal-window-2", "is_open"),
        Output("sp-ee-modal-header-2", "children"),
        Output("sp-ee-modal-body-2", "children"),
        Output("sp-ee-modal-close-2", "color"),
        Output("sp-ee-modal-close-2", "href"),
    ],
    [Input("sp-ee-submit-2", "n_clicks"), Input("sp-ee-modal-close-2", "n_clicks")],
    [
        State("sp-ee-action", "value"),
        State("sp-form-remark-2", "value"),
        State("url", "hash"),
        State("user", "data"),
        State("sp-ee-modal-window-2", "is_open"),
    ],
    prevent_initial_call=False,
)
def sp_cancel(submit, close, action, remark, hash, user, is_open):
    nt_name = user.get("nt_name").lower()
    today = datetime.now()
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if submit or close:
        if id == "sp-ee-submit-2" and action == 2:
            if remark:
                task_id = hash.split("#")[1]
                id = hash.split("#")[1]
                sql = "select m.user,m.modified_date,m.status,m.remark,m.attachment,t.id,t.input_date,\
                t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source,m.spec\
                from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id where m.task_id=%s"
                params = [id]
                df = read_sql(sql, params=params)

                df = df.sort_values(by=["modified_date"], ascending=False)
                df = df.drop_duplicates(["id"])
                df = df.loc[df.status.isin(["accepted", "modify_ee", "modify_me"])]

                # * ---邮件所需内容----
                model = df.model.iat[0]
                doc_type = df.doc_type.iat[0]
                owner = df.owner.iat[0]
                df_mail = df.reindex(
                    columns=[
                        "doc_type",
                        "model",
                        "status",
                        "input_date",
                        "request_date",
                        "remark",
                    ]
                )
                df_mail["input_date"] = df_mail["input_date"].dt.date
                df_mail["status"] = "cancel"
                df_mail["remark"] = remark
                df_mail = df_mail.rename(columns=col_dict)

                df["request_date"] = pd.to_datetime(
                    df["request_date"], errors="coerce"
                ).dt.strftime("%Y-%m-%d")
                df["request_date"] = df["request_date"].where(
                    df["request_date"].notnull(), None
                )
                df = df.reindex(
                    columns=["model", "ee", "me", "request_date", "attachment", "spec"]
                )
                tup = tuple(sum(df.loc[0:].values.tolist(), []))
                conn = pool.connection()
                cu = conn.cursor()
                params2 = [(task_id, nt_name, today, "cancel", remark) + tup]
                sql2 = (
                    "insert into ssp_spec.modification (task_id,user,modified_date,status,remark,model,ee,me,request_date,attachment,spec)\
                        values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
                )
                cu.executemany(sql2, params2)
                conn.commit()
                cu.close()
                conn.close()
                tup_back = (None, not is_open, "success", "退单成功！", "success", "#")

                # * ---退单邮件---
                to = f"{owner}@deltaww.com;{nt_name}@deltaww.com"
                subject = f"SSP-任务取消({doc_type}-{model})"
                title = f"Dear {owner},<br>{doc_type}-{model}被退回,请知悉.如有问题请联系规格组<br>"
                body = df_to_html(df_mail, title=title)
                bg_mail(to, subject, body)
                return tup_back
            else:
                tup_back = (
                    no_update,
                    not is_open,
                    "danger",
                    "退单失败！",
                    "danger",
                    "",
                )
                return tup_back
        else:
            tup_back = (no_update, not is_open, no_update, no_update, no_update, "")
            return tup_back
    else:
        raise PreventUpdate


# -----------------转单------------------------------
@callback(
    Output("sp-form-spec", "options"),
    [Input("user", "data")],
    prevent_initial_call=False,
)
def get_sp_form_spec_options(user):
    spec = user.get("nt_name").lower()
    sql = "select distinct owner from ssp_spec.duty"
    df = read_sql(sql)
    df["owner"] = df["owner"].str.lower()
    owners = df["owner"].tolist()
    if spec in owners:
        owners.remove(spec)
    source = [{"label": i, "value": i} for i in owners]
    return source


@callback(
    [
        Output("sp-form-spec", "value"),
        Output("sp-form-remark-3", "value"),
        Output("sp-ee-modal-window-3", "is_open"),
        Output("sp-ee-modal-header-3", "children"),
        Output("sp-ee-modal-body-3", "children"),
        Output("sp-ee-modal-close-3", "color"),
        Output("sp-ee-modal-close-3", "href"),
    ],
    [Input("sp-ee-submit-3", "n_clicks"), Input("sp-ee-modal-close-3", "n_clicks")],
    [
        State("sp-ee-action", "value"),
        State("sp-form-spec", "value"),
        State("sp-form-remark-3", "value"),
        State("url", "hash"),
        State("user", "data"),
        State("sp-ee-modal-window-3", "is_open"),
    ],
    prevent_initial_call=False,
)
@db_session
def submit_divert_data(submit, close, action, spec, remark, hash, user, is_open):
    nt_name = user.get("nt_name").lower()
    # today = datetime.now()
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if submit or close:
        if id == "sp-ee-submit-3" and action == 3:
            if spec:
                task_id = hash.split("#")[1]
                # sql = 'select m.user,m.modified_date,m.status,m.remark,m.attachment,t.id,\
                # t.owner,m.model,t.customer,m.ee,m.me,t.dept,t.doc_type,m.request_date,t.second_source\
                # from ssp_spec.modification m left join ssp_spec.task t on m.task_id=t.id where m.task_id=%s'
                sql = "select * from ssp_spec.modification where task_id=%s"
                params = [task_id]
                df = read_sql(sql, params=params)
                df = df.sort_values(by=["modified_date"], ascending=False)
                df = df.drop_duplicates(["task_id"])
                df = df.loc[
                    df.status.isin(["accepted", "modify_ee", "modify_me"]),
                    (
                        "task_id",
                        "status",
                        "remark",
                        "attachment",
                        "model",
                        "ee",
                        "me",
                        "request_date",
                    ),
                ]
                df["request_date"] = pd.to_datetime(
                    df["request_date"], errors="coerce"
                ).dt.strftime("%Y-%m-%d")
                df["request_date"] = df["request_date"].where(
                    df["request_date"].notnull(), None
                )
                df = df.reindex(
                    columns=[
                        "task_id",
                        "status",
                        "remark",
                        "attachment",
                        "model",
                        "ee",
                        "me",
                        "request_date",
                    ]
                )
                df = df.dropna(axis=1)
                dic = df.to_dict("records")[0]
                if dic.get("remark"):
                    dic["remark"] = dic.get("remark") + "|" + remark
                else:
                    dic["remark"] = remark
                # tup = tuple(sum(df.loc[0:].values.tolist(),[]))
                dic["current"] = spec
                dic["user"] = nt_name
                dic["modified_date"] = datetime.now()
                dic["spec"] = spec
                db.insert("Modification", **dic)
                tup_back = (
                    None,
                    None,
                    not is_open,
                    "success",
                    "转单成功",
                    "success",
                    "#",
                )
                return tup_back
            else:
                tup_back = (
                    no_update,
                    no_update,
                    not is_open,
                    "danger",
                    "请选择新处理人",
                    "danger",
                    "",
                )
                return tup_back
        else:
            tup_back = (
                no_update,
                no_update,
                not is_open,
                no_update,
                no_update,
                no_update,
                "",
            )
            return tup_back
    else:
        raise PreventUpdate


# --------------------SPEC->RD 查询------------------------------------


# 合并重叠数据
def merge_rows(df):
    num = df["revise_date"].unique()
    first = num[0]
    dfx = df[df["revise_date"].isin([first])]
    dfx.index = range(len(dfx))
    for i in num:
        dfi = df[df["revise_date"].isin([i])]
        dfi.index = range(len(dfi))
        dfx = dfx.combine_first(dfi)
    return dfx


# 数据预处理
# ! 严重性能问题,数据增加后,性能瓶颈,应改成按条件从数据库载入数据
def data_pre_processing(nt_name):
    sql = "SELECT b.* FROM (\
            SELECT DISTINCT(m.id)tt,m.*,t.owner,t.doc_type,t.dept,t.input_date,\
            t.submit_date,t.release_date,t.submit_ontime,t.release_ontime,t.psl_qty,\
            t.work_time_minute FROM ssp_spec.modification m\
            LEFT JOIN ssp_spec.task t \
            ON m.task_id=t.id \
            ORDER BY m.id DESC\
            ) b GROUP BY b.task_id"  # SELECT DISTINCT 语句用于返回唯一不同的值; left join(左联接) 返回包括左表中的所有记录和右表中联结字段相等的记录
    df1 = read_sql(sql)

    sql2 = "SELECT * from ssp_spec.task WHERE new_source= %s or doc_type in %s"
    params = ["Y", ["TOOLA", "TOOLB"]]
    df2 = read_sql(sql2, params=params)
    df2["task_id"] = df2["id"]
    df = pd.concat([df1, df2])
    # df['revise_date'] = np.where(df.input_date.notnull(),df.input_date,df.modified_date)
    # df = df.sort_values(by=['id','revise_date'],ascending=False)
    # df = df.groupby('id').apply(merge_rows)
    for i in [
        "input_date",
        "modified_date",
        "submit_date",
        "release_date",
        "followup_date",
    ]:
        df[i] = pd.to_datetime(df[i], errors="coerce").dt.strftime("%Y-%m-%d %H-%M-%S")
    return df


# @callback(
#     Output("sp-query-state", "data"),
#     Input("user", "data"),
#     prevent_initial_call=False,
# )
# def query_tabel_state(user):
#     nt_name = user.get("nt_name").lower()
#     df = data_pre_processing(nt_name)
#     repalce(df)

#     df = df.to_dict("records")
#     return df


@callback(
    Output("sp-query-result", "data"),
    Input("sp-query-btn", "n_clicks"),  # 查询按钮
    State("user", "data"),
    State("sp-query-status", "value"),
    State("sp-query-model", "value"),
    State("sp-query-doctype", "value"),
    State("sp-query-spec", "value"),
    State("sp-query-date-multi", "start_date"),
    State("sp-query-date-multi", "end_date"),  # inpitdate
    State("sp-query-date-single", "date"),  # releasedate
    State("sp-query-dept", "value"),
    State("sp-query-sot", "value"),  # SubmitOnTime
    State("sp-query-rot", "value"),  # releaseOnTime
    State("sp-query-fot", "value"),  # 新增
    prevent_initial_call=False,
)
def query_tabel_data(
    n_clicks,
    user,
    status,
    model,
    doc_type,
    spec,
    start,
    end,
    modified_date,
    dept,
    sot,
    rot,
    fot,
):
    if not n_clicks:
        raise PreventUpdate
    if not any(
        (status, model, doc_type, spec, start, end, modified_date, dept, sot, rot, fot)
    ):
        return []

    nt_name = user.get("nt_name").lower()
    df = data_pre_processing(nt_name)
    df["current"] = np.where(df.status == "close", "", df["current"])
    condition = {
        "status": status,
        "model": model,
        "doc_type": doc_type,
        "spec": spec,
        "modified_date": modified_date,
        "dept": dept,
        "submit_ontime": sot,
        "release_ontime": rot,
        "followup_ontime": fot,
    }
    if status == "process":
        df["status"] = np.where(
            df["status"].isin(["process", "accepted", "submitted_ee", "submitted_me"]),
            "process",
            df["status"],
        )
    for key, value in condition.items():
        if value is not None:
            if key == "spec":
                df = df.loc[df[key].str.lower() == value.lower()]
            else:
                df = df.loc[df[key] == value]

    if start is not None:
        if end is not None:
            df = df[(df["input_date"] >= start) & (df["input_date"] <= end)]
        else:
            df = df[(df["input_date"] >= start)]
    else:
        if end is not None:
            df = df[(df["input_date"] <= end)]

    df["history"] = "查看"
    df["status"] = df["status"].apply(lambda x: status_dict.get(x, x))
    df = df.to_dict("records")
    return df


@callback(
    Output("sp-query-modal", "is_open"),
    Output("sp-query-modal-body", "children"),
    Output("sp-query-modal-dept", "children"),
    Output("sp-query-modal-doctype", "children"),
    Input("sp-query-result", "rowClicked"),
    Input("sp-query-modal-close", "n_clicks"),
    Input("sp-query-result", "cellTap"),
    State("sp-query-modal", "is_open"),
    prevent_initial_call=False,
)
@db_session
def toggle_sp_query_modal(cellclick, n, cell, is_open):
    ctx = callback_context
    id = ctx.triggered[0]["prop_id"].split(".")[0]
    if id == "sp-query-result":
        # task_id = cellclick.get('id')
        task_id = cellclick.get("task_id")
        modifi = Modification.select(lambda m: m.task_id == task_id)[:]
        task = Task.get(id=task_id)
        modal_body = history(modifi)
        return not is_open, modal_body, task.dept, task.doc_type
        # return not is_open, create_timeline(task, modifi), task.dept, task.doc_type
    elif id == "sp-query-modal-close":
        return not is_open, no_update, no_update, no_update
    return is_open, no_update, no_update, no_update


@callback(
    Output("sp-query-status", "options"),
    Output("sp-query-model", "options"),
    Output("sp-query-doctype", "options"),
    Output("sp-query-spec", "options"),
    Output("sp-query-dept", "options"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def query_condition(user):
    if not user:
        raise PreventUpdate
    sql = "select distinct status,model,doc_type,spec,dept from ssp_spec.task"
    df = read_sql(sql)
    status = [
        {"label": status_dict.get(i, i), "value": i}
        for i in df["status"].dropna().unique()
    ]
    model = [{"label": i, "value": i} for i in df["model"].dropna().unique()]
    doc_type = [{"label": i, "value": i} for i in df["doc_type"].dropna().unique()]
    spec = [{"label": i, "value": i} for i in df["spec"].dropna().str.title().unique()]
    dept = [{"label": i, "value": i} for i in df["dept"].dropna().unique()]
    return status, model, doc_type, spec, dept


# ---------Timeline-----------------------br
def history(data):
    df = pd.DataFrame(i.to_dict() for i in data)
    # mask = df != df.shift(1)
    # df = df.where(mask)
    # df["current"] = df["current"].fillna("未指派")
    # df["spec2"] = df["spec"].fillna(method="ffill")
    # df["ee2"] = df["ee"].fillna(method="ffill")
    # df["me2"] = df["me"].fillna(method="ffill")
    df = df.fillna("")
    if df.empty:
        return
    df["model2"] = df["model"].where(df["model"] != df["model"].shift(1), "")
    df["ee2"] = df["ee"].where(df["ee"] != df["ee"].shift(1), "")
    df["me2"] = df["me"].where(df["me"] != df["me"].shift(1), "")

    df["remark2"] = df["remark"].where(df["remark"] != df["remark"].shift(1), "")
    df["request_date2"] = df["request_date"].where(
        df["request_date"] != df["request_date"].shift(1), ""
    )
    df["attachment2"] = df["attachment"].where(
        df["attachment"] != df["attachment"].shift(1), ""
    )

    l = []
    colors = {"EE": "green", "PM": "blue", "ME": "yellow", "SPEC": "red"}
    for i in df.itertuples():
        role = {i.ee.lower(): "EE", i.me.lower(): "ME", i.spec.lower(): "SPEC"}
        accordion_item = []
        if i.model2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "机种名称:",
                            size="sm",
                            style={"color": "#FA7600"},
                            weight="bolder",
                        ),
                        dmc.Text(
                            i.model2,
                            size="sm",
                        ),
                    ],
                )
            )
        if i.ee2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "电子工程师:",
                            size="sm",
                            style={"color": "#FA7600"},
                            weight="bolder",
                        ),
                        dmc.Text(
                            i.ee2,
                            size="sm",
                        ),
                    ],
                )
            )
        if i.me2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "机构工程师:",
                            size="sm",
                            style={"color": "#FA7600"},
                            weight="bolder",
                        ),
                        dmc.Text(
                            i.me2,
                            size="sm",
                        ),
                    ],
                )
            )
        if i.remark2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "备注:",
                            size="sm",
                            style={"color": "#FA7600"},
                            weight="bolder",
                        ),
                        dmc.Text(
                            i.remark2,
                            size="sm",
                        ),
                    ],
                )
            )

        if i.request_date2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "需求日期:",
                            size="sm",
                            style={"color": "#FA7600"},
                            # color="blue",
                            weight="bolder",
                        ),
                        dmc.Text(
                            i.request_date2,
                            size="sm",
                        ),
                    ],
                )
            )

        if i.attachment2:
            accordion_item.append(
                dmc.Group(
                    [
                        dmc.Text(
                            "附件:",
                            size="sm",
                            style={"color": "#FA7600"},
                            weight="bolder",
                        ),
                        dbc.Button(
                            Path(i.attachment2).name,
                            id={"type": "history-download", "index": i.Index},
                            size="sm",
                            color="link",
                            download=i.attachment2,
                        ),
                        dcc.Download(
                            id={"index": i.Index, "type": "sp-timeline-download"}
                        ),
                    ],
                    align="center",
                )
            )
        if accordion_item and i.Index:
            accordion_item = dmc.Alert(accordion_item, title="变更:", color="red")

        if i.status.lower() in ("modify_ee", "modify_me", "cancel"):
            color = "red"
        else:
            color = "green"

        item = dmc.TimelineItem(
            dmc.Accordion(
                dmc.AccordionItem(
                    [
                        dmc.AccordionControl(
                            [
                                dmc.Badge(
                                    role.get(i.user.lower(), "PM"),
                                    color=colors.get(role.get(i.user.lower(), "PM")),
                                    variant="filled",
                                ),
                                dmc.Badge(i.user, color="blue", variant="outline"),
                            ]
                        ),
                        dmc.AccordionPanel(accordion_item),
                    ],
                    # label=[
                    #     dmc.Badge(
                    #         role.get(i.user.lower(), "PM"),
                    #         color=colors.get(role.get(i.user.lower(), "PM")),
                    #         variant="filled",
                    #     ),
                    #     dmc.Badge(i.user, color="blue", variant="outline"),
                    # ],
                    value="link",
                ),
                # icon=[DashIconify(icon="ant-design:down-outlined")],
                # iconPosition="right",
            ),
            # bullet=[DashIconify(icon="icon-park-outline:history")],
            bullet=[DashIconify(icon="cil:history")],
            title=i.modified_date,
            color=color,
        )
        l.append(item)
    child = dmc.Timeline(l, active=df["id"].max())
    return child


def create_timeline(task, modifi):
    # itema = [create_timeline_entry(task,task,100)]
    # itemb = [create_timeline_entry(task,m,index) for index,m in enumerate(modifi)]
    # itema.extend(itemb)
    itema = [create_timeline_entry(task, m, index) for index, m in enumerate(modifi)]
    return html.Div(itema, className="timeline")


def create_timeline_entry(task, entry, index):
    # if entry.status == 'open':
    #     mod_date = entry.input_date
    # else:
    #     mod_date = entry.modified_date
    mod_date = entry.modified_date
    return html.Div(
        [
            create_timeline_title(task, mod_date, entry.status),
            create_timeline_body(task, entry, index),
        ],
        className="timeline-entry",
    )


# timeline 时间组件
def create_timeline_title(task, mod_date, status):
    if not isinstance(mod_date, datetime):
        mod_date = datetime.strptime(mod_date, "%Y-%m-%d %H:%M:%S")
    date = mod_date.strftime("%Y-%m-%d")
    time = mod_date.strftime("%H:%M:%S")
    release_ontime = task.release_ontime
    if status.lower() in ("modify_ee", "modify_me", "cancel"):
        style = "timeline-title timeline-red"
    elif status.lower() == "close" and release_ontime == "N":
        style = "timeline-title timeline-yellow"
    else:
        style = "timeline-title timeline-green"
    return html.Div([html.H3(date), html.P(time)], className=style)


# timeline body部分
@db_session
def create_timeline_body(task, data, index):
    # nt_name = data.owner if data.status=='open' else data.user
    nt_name = data.user
    role_group = User.get(nt_name=nt_name).role_group
    role_color = "warning" if role_group == "SPEC" else "info"
    config = {
        "open": "尚未受理",
        "accepted": "规格已接收",
        "submitted_ee": "电子审核",
        "submitted_me": "机构审核",
        "modify_ee": "文件更新",
        "modify_me": "文件新",
        "close": "流程结束",
        "cancel": "任务取消",
    }
    children = [
        dbc.Badge(role_group, color=role_color, className="mx-1"),
        html.Span(nt_name.title(), className="mx-1"),
        # html.Span(
        #     config.get(data.status), className="mx-1", style={"font-weight": "bold"}
        # ),
    ]
    # if data.status.lower() in ["open", "submitted_ee", "submitted_me"]:
    #     children.append(html.Span(data.model, className="mx-1"))
    if data.status.lower() in [
        "open",
        "submitted_ee",
        "modify_ee",
        "modify_me",
        "close",
    ]:
        children.append(create_timeline_collapse_btn(index))
        children.append(create_timeline_collapse(task, data, index))
    return html.Div(children, className="timeline-body")


# timeline 折叠按钮
def create_timeline_collapse_btn(index):
    return dbc.Button(
        "+",
        color="primary",
        id={"index": index, "type": "timeline-btn"},
        outline=True,
        className="ml-3",
        style={
            "font-family": "SimSun",
            "padding-left": "7px",
            "padding-right": "7px",
            "padding-top": "0",
            "padding-bottom": "0",
        },
    )


# 匹配详情信息
def match_timeline_message(dic, config):
    content = []
    for key, val in config.items():
        if dic.get(key):
            dic_v = dic.get(key)
            if type(dic_v).__name__ == "datetime":
                dic_v = dic_v.strftime("%Y-%m-%d")
            li = html.Li(val + " : " + dic_v)
            content.append(li)
    return content


# attachment附件下载
def match_timeline_download(dic, content):
    if dic.get("attachment"):
        attachment = dic.get("attachment")
        id = dic.get("id")
        if dic.get("task_id"):
            task_id = dic.get("task_id")
            index = str(task_id) + "-" + str(id)
        else:
            index = str(id)
        filename = attachment.rsplit("/", 1)[1]
        li = html.Li(
            [
                "附件 : ",
                html.A(
                    [filename],
                    id={"index": index, "type": "sp-timeline-attach"},
                    className="a-style",
                    title=attachment,
                ),
                dcc.Download(id={"index": index, "type": "sp-timeline-download"}),
            ]
        )
        content.append(li)
        return content


@db_session
def create_timeline_collapse(task, data, index):
    # dept_id = task.dept_id
    # doc_type = task.doc_type
    current_status = data.status
    data_dic = data.to_dict()
    task_dic = task.to_dict()
    # doctype = Doctype.get(dept_id=dept_id,doc_type=doc_type)
    # form_id = list(doctype.form_id)
    # apply_form = Apply_form.select(lambda a: a.id in form_id)[:]
    if current_status.lower() == "open":
        config_a = {
            "other_address": "其他上传地址",
            "customer": "客户名称",
            "second_source": "是否需要多报备替代料",
            "upload_address": "上传地址",
        }
        config_b = {
            "model": "机种名称",
            "ee": "电子姓名",
            "me": "机构姓名",
            "request_date": "需要日期",
            "remark": "其他要求",
        }
        contentx = match_timeline_message(task_dic, config_a)
        contenty = match_timeline_message(data_dic, config_b)
        contentz = contenty + contentx
        content = match_timeline_download(data_dic, contentz)
    elif current_status.lower() in ["submitted_ee", "modify_ee", "modify_me"]:
        config_li = {"remark": "意见"}
        contentx = match_timeline_message(data_dic, config_li)
        content = match_timeline_download(data_dic, contentx)
    else:
        content = match_timeline_download(data_dic, [])

    if current_status.lower() in ("modify_ee", "modify_me"):
        config_ul = {
            "model": "机种名称",
            "ee": "电子姓名",
            "me": "机构姓名",
            "request_date": "需求时间",
        }
        arr_ul = match_timeline_message(data_dic, config_ul)
        children = [
            html.Ul(content),
            dbc.Badge("变更", color="danger", className="ml-3"),
            html.Ul(arr_ul),
        ]
    else:
        children = [html.Ul(content)]
    return dbc.Collapse(
        children, id={"index": index, "type": "timeline-collapse"}, is_open=False
    )


# timeline 内部折叠
@callback(
    Output({"type": "timeline-collapse", "index": MATCH}, "is_open"),
    [Input({"type": "timeline-btn", "index": MATCH}, "n_clicks")],
    [State({"type": "timeline-collapse", "index": MATCH}, "is_open")],
)
def sp_ee_open(n, is_open):
    if n:
        return not is_open
    return is_open


@callback(
    Output({"type": "timeline-btn", "index": MATCH}, "children"),
    [Input({"type": "timeline-collapse", "index": MATCH}, "is_open")],
)
def sp_ee_rotation(is_open):
    if is_open:
        return "-"
    else:
        return "+"


# ------timeline中附件下载------
@callback(
    Output({"type": "sp-timeline-download", "index": MATCH}, "data"),
    Input({"type": "history-download", "index": MATCH}, "n_clicks"),
    State({"type": "history-download", "index": MATCH}, "download"),
)
def history_download(n, url):
    return dcc.send_file(url)


# @callback(
#     Output({"type": "sp-timeline-download", "index": MATCH}, "data"),
#     Input({"type": "sp-timeline-attach", "index": MATCH}, "n_clicks"),
#     State({"type": "sp-timeline-attach", "index": MATCH}, "title"),
#     prevent_initial_call=False,
# )
# def sp_download(n, url):
#     if n:
#         ctx = callback_context
#         id = ctx.triggered[0]["prop_id"].split(".")[0]
#         id = json.loads(id)
#         if id.get("type") == "sp-timeline-attach":
#             return dcc.send_file(url)


@callback(
    Output("sp-form-date", "min_date_allowed"),
    Input("sp-form-date", "id"),
    prevent_initial_call=False,
)
def sp_form_min_date(id):
    """需求日期最小时间"""
    return datetime.now()


# --------替换状态----------
def repalce(df):
    df["status"] = df["status"].str.lower()
    config = {
        "accepted": "规格已接收",
        "submitted_ee": "电子审核",
        "submitted_me": "机构审核",
        "modify_ee": "文件更新",
        "modify_me": "文件更新",
        "open": "尚未受理",
        "close": "流程结束",
        "cancel": "任务取消",
    }
    for key, value in config.items():
        df.loc[df["status"] == key, "status"] = value
    return df


# ------------自动校验RD姓名------------
def sp_rd_name(role, owner):
    params = [role, owner]
    sql = "select nt_name from ssp.user where role_group = %s \
        and dept_id=(select dept_id from ssp.user where nt_name=%s) \
            and termdate is null"
    df = read_sql(sql, params=params)
    df["nt_name"] = df["nt_name"].str.lower()
    l = df["nt_name"].tolist()
    source = [{"label": i, "value": i} for i in l]
    return source


# -------------下载文件转pdf-------
def ppt_pdf(path):
    # PPT 转 PDF
    pdf_path = path.replace("ppt", "pdf")  # pdf保存路径 推荐使用绝对路径
    try:
        p = client.CreateObject("PowerPoint.Application")
        ppt = p.Presentations.Open(path)
        ppt.ExportAsFixedFormat(pdf_path, 2, PrintRange=None)
        ppt.Close()
        p.Quit()
    except Exception as e:
        pass


def word_pdf(path):
    # Word转pdf
    pdf_path = path.replace("doc", "pdf")
    w = client.CreateObject("Word.Application")
    doc = w.Documents.Open(path)
    doc.ExportAsFixedFormat(pdf_path, 17)
    doc.Close()
    w.Quit()


def excel_pdf(self, path):
    # Excel转pdf
    pdf_path = path.replace("xls", "pdf")
    xlApp = client.CreateObject("Excel.Application")
    books = xlApp.Workbooks.Open(path)
    books.ExportAsFixedFormat(0, pdf_path)
    xlApp.Quit()


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="update_action"),
    Output(id("due_day_table"), "id"),
    Input(id("due_day_table"), "cellValueChanged"),
    State(id("due_day_table"), "id"),
)


@callback(
    Output("global-notice", "children"),
    Output(id("due_day_submit"), "disabled"),
    Input(id("due_day_submit"), "n_clicks"),
    State(id("due_day_table"), "rowData"),
)
def due_day_submit(n, data):
    if not n:
        raise PreventUpdate
    df = pd.DataFrame(data)
    dfu = df.loc[df["action"] == "updated"]
    if dfu.empty:
        raise PreventUpdate
    dfu.drop(columns=["action"], inplace=True)
    df_update("ssp_spec.due_day", dfu)
    return notice("更新成功"), True


clientside_callback(
    ClientsideFunction(namespace="clientside", function_name="update_action"),
    Output(id("duty_table"), "id"),
    Input(id("duty_table"), "cellValueChanged"),
    State(id("duty_table"), "id"),
)


@callback(
    Output("global-notice", "children"),
    Output(id("duty_submit"), "disabled"),
    Input(id("duty_submit"), "n_clicks"),
    State(id("duty_table"), "rowData"),
)
def duty_submit(n, data):
    if not n:
        raise PreventUpdate
    df = pd.DataFrame(data)
    dfu = df.loc[df["action"] == "updated"]
    if dfu.empty:
        raise PreventUpdate
    dfu.drop(columns=["action"], inplace=True)
    df_update("ssp_spec.duty", dfu)
    return notice("更新成功"), True
