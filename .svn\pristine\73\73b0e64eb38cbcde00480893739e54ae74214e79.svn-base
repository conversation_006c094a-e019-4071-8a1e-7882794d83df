#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用MySQL的LAST_INSERT_ID()函数实现的唯一码生成器
"""

from config import pool
from typing import Optional, Union, Dict, Any
import time
import logging

logger = logging.getLogger(__name__)


class IdGenerator:
    """
    基于MySQL的LAST_INSERT_ID()实现的唯一ID生成器

    原理：利用MySQL的自增特性和LAST_INSERT_ID()函数，
    在表中插入一条记录并获取自增ID，从而保证在高并发环境下ID的唯一性。
    """

    def __init__(self, table_name: str = "id_generator"):
        """
        初始化ID生成器

        Args:
            db_config: 数据库连接配置，包含host, user, password, database等
            table_name: 用于ID生成的表名，默认为'id_generator'
        """
        self.table_name = table_name
        self._ensure_table_exists()

    def _get_connection(self):
        """获取数据库连接"""
        try:
            return pool.connection()
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise

    def _ensure_table_exists(self):
        """确保ID生成器表存在"""
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS `{self.table_name}` (
                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                  `type` varchar(50) NOT NULL COMMENT '业务类型',
                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  KEY `idx_type` (`type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='唯一ID生成器表'
                """)
                conn.commit()
        except Exception as e:
            logger.error(f"创建ID生成器表失败: {e}")
            raise
        finally:
            conn.close()

    def generate_id(
        self,
        type_name: str,
        prefix: str = "",
        suffix: str = "",
        padding: int = 0,
        padding_char: str = "0",
    ) -> str:
        """
        生成唯一ID

        Args:
            type_name: 业务类型名称，用于区分不同业务的ID
            prefix: ID前缀
            suffix: ID后缀
            padding: ID数字部分的填充长度，0表示不填充
            padding_char: 填充字符

        Returns:
            生成的唯一ID字符串
        """
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                # 插入记录并获取自增ID
                cursor.execute(
                    f"INSERT INTO `{self.table_name}` (`type`) VALUES (%s)",
                    (type_name,),
                )
                # 获取LAST_INSERT_ID
                cursor.execute("SELECT LAST_INSERT_ID() as id")
                last_id = cursor.fetchone().get("id")
                conn.commit()

                # 格式化ID
                id_str = str(last_id)
                if padding > 0:
                    id_str = id_str.zfill(padding)
                return f"{prefix}{id_str}{suffix}"
        except Exception as e:
            conn.rollback()
            logger.error(f"生成唯一ID失败: {e}")
            raise
        finally:
            conn.close()

    def generate_timestamp_id(
        self, type_name: str, prefix: str = "", suffix: str = ""
    ) -> str:
        """
        生成带时间戳的唯一ID

        Args:
            type_name: 业务类型名称
            prefix: ID前缀
            suffix: ID后缀

        Returns:
            格式为: {prefix}{yyyyMMddHHmmss}{自增ID}{suffix} 的唯一ID
        """
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        id_value = self.generate_id(type_name)
        return f"{prefix}{timestamp}{id_value}{suffix}"

    def batch_generate_ids(self, type_name: str, count: int, **kwargs) -> list:
        """
        批量生成唯一ID

        Args:
            type_name: 业务类型名称
            count: 需要生成的ID数量
            **kwargs: 传递给generate_id的其他参数

        Returns:
            生成的唯一ID列表
        """
        if count <= 0:
            return []

        # 从kwargs中提取格式化参数
        prefix = kwargs.get("prefix", "")
        suffix = kwargs.get("suffix", "")
        padding = kwargs.get("padding", 0)
        padding_char = kwargs.get("padding_char", "0")

        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                # 构建批量插入SQL
                # 方法1：使用单条SQL插入多行数据
                placeholders = ", ".join(["(%s)"] * count)
                values = [type_name] * count

                insert_sql = (
                    f"INSERT INTO `{self.table_name}` (`type`) VALUES {placeholders}"
                )
                cursor.execute(insert_sql, values)

                # 获取第一个插入的ID
                cursor.execute("SELECT LAST_INSERT_ID() as id")
                first_id = cursor.fetchone().get("id")
                conn.commit()

                # 基于第一个ID计算所有ID
                result = []
                for i in range(count):
                    id_num = first_id + i
                    id_str = str(id_num)
                    if padding > 0:
                        id_str = id_str.zfill(padding)
                    result.append(f"{prefix}{id_str}{suffix}")

                return result
        except Exception as e:
            conn.rollback()
            logger.error(f"批量生成唯一ID失败: {e}")
            # 如果批量操作失败，回退到单个生成（可能较慢但更可靠）
            logger.warning("回退到逐个生成ID模式")
            return [self.generate_id(type_name, **kwargs) for _ in range(count)]
        finally:
            conn.close()


# 简单使用示例
def get_unique_code(
    type_name: str, db_config: Optional[Dict[str, Any]] = None, **kwargs
) -> str:
    """
    获取唯一码的便捷函数

    Args:
        type_name: 业务类型名称
        db_config: 数据库配置，如果为None则使用默认配置
        **kwargs: 其他ID生成参数

    Returns:
        生成的唯一码
    """
    generator = IdGenerator()
    return generator.generate_id(type_name, **kwargs)


# 使用示例
if __name__ == "__main__":
    # 创建ID生成器
    id_gen = IdGenerator()

    # 生成普通唯一ID
    order_id = id_gen.generate_id("order", prefix="ORD", padding=6)
    print(f"订单ID: {order_id}")  # 输出类似: ORD000123

    # 生成带时间戳的唯一ID
    user_id = id_gen.generate_timestamp_id("user", prefix="U")
    print(f"用户ID: {user_id}")  # 输出类似: U20230401123456789

    # 批量生成ID
    batch_ids = id_gen.batch_generate_ids("product", 100, prefix="P", padding=5)
    print(
        f"批量产品ID: {batch_ids}"
    )  # 输出类似: ['P0001', 'P0002', 'P0003', 'P0004', 'P0005']

    # # 使用便捷函数
    # simple_id = get_unique_code("customer", prefix="CUST", padding=5)
    # print(f"客户ID: {simple_id}")  # 输出类似: CUST00123
