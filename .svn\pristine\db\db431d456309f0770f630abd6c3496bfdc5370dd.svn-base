# -*- coding: utf-8 -*-
from io import BytesIO

import dash_ag_grid as dag
import dash_mantine_components as dmc
import numpy as np
import openpyxl
import orjson
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dcc, html
from dash_iconify import DashIconify
from openpyxl.styles import Alignment, Font

from common import id_factory, parse_search, read_sql
from components import create_sidebar, notice, Grid
from config import cfg
import feffery_antd_components.alias as fac
from utils import db

id = id_factory(__name__)


STATUS_DICT = {
    "open": ["open"],
    "ongoing": ["ongoing", "approve"],
    "close": ["close"],
}

STATUS_MAP = {
    "open": "未受理",
    "ongoing": "处理中",
    "approve": "待审核",
    "close": "已结案",
}

sub_type_dict = {
    "全新料号": "new",
    "临时料号": "temp",
    "客户专用": "customer",
    "料号升级": "update",
    "规格书更新": "spec",
    "IC+软体组合件": "sw",
}

menu_items = [
    {
        "key": "0",
        "title": "CE",
        "label": "CE",
        "icon": "material-symbols:home",
        "href": "/ce",
        "page": "home",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
    },
    {
        "key": "1",
        "title": "未处理",
        "label": "未处理",
        "icon": "mdi:account-details",
        "href": "?page=open&status=open",
        "backgroundColor": "wheat",
        "page": "open",
    },
    {
        "key": "2",
        "title": "在途",
        "label": "在途",
        "icon": "mdi:account-edit",
        "href": "?page=ongoing&status=ongoing",
        "backgroundColor": "wheat",
        "page": "ongoing",
    },
    {
        "key": "3",
        "title": "已处理",
        "label": "已处理",
        "icon": "mdi:account-eye",
        "href": "?page=close&status=close",
        "backgroundColor": "wheat",
        "page": "close",
    },
    {
        "title": "新增",
        "label": "新增",
        "icon": "mdi:account-cog",
        "href": "/ce/special",
        "target": "_blank",
        "page": "project",
    },
    {
        "key": "5",
        "title": "管理",
        "label": "管理",
        "icon": "mdi:account-cog",
        # "href": "?page=division",
        # "page": "division",
        "children": [
            {
                "title": "职责表",
                "label": "职责表",
                "icon": "mdi:account-cog",
                "href": "?page=division",
                "page": "division",
            },
            {
                "title": "工作数据",
                "label": "工作数据",
                "icon": "mdi:account-cog",
                "href": "?page=workdata",
                # "target": "_blank",
                "page": "workdata",
            },
        ],
    },
    {
        "key": "6",
        "title": "查询",
        "label": "查询",
        "icon": "material-symbols:search",
        "href": "?page=query",
        "page": "query",
        # "target": "_blank",
    },
    {
        "key": "7",
        "title": "材料调查",
        "label": "材料调查",
        "icon": "ic:baseline-content-paste-search",
        "href": "/ce/survey/rd",
        "target": "_blank",
        "page": "survey",
    },
]


def create_task_table():
    task_table = dag.AgGrid(
        id=id("task-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "id",
                "headerName": "ID",
                "width": 70,
            },
            {"field": "urgent", "headerName": "紧急程度", "width": 90},
            {
                "field": "type",
                "headerName": "作业类型",
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "width": 120,
            },
            {"field": "deltapn", "headerName": "料号", "width": 120},
            {"field": "mfgname", "headerName": "厂商", "width": 90},
            {"field": "mfgpn", "headerName": "厂商料号", "width": 120},
            {"field": "dept", "headerName": "申请部门", "width": 120},
            {"field": "applicant", "headerName": "申请人", "width": 120},
            {"field": "status", "headerName": "状态", "width": 90},
            {
                "field": "ce",
                "headerName": "负责人",
                "editable": True,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.ce,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "width": 110,
            },
            {"field": "start_date", "headerName": "申请日期", "width": 120},
            {"field": "gmt_update", "headerName": "更新日期", "width": 120},
            {"field": "end_date", "headerName": "结束日期", "width": 120},
            {"field": "cat3", "headerName": "材料类别3", "width": 120},
            {
                "field": "ce_comment",
                "headerName": "CE备注",
                "editable": True,
                "width": 120,
            },
        ],
        rowData=[],
        # columnSize="autoSize",
        # rowSelection="single",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowSelection": "multiple",
        },
    )
    task = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab(
                        "待审核",
                        # rightSection=dmc.Badge(
                        #     "6",
                        #     size="xs",
                        #     p=0,
                        #     variant="filled",
                        #     sx={"width": 16, "height": 16, "pointerEvents": "none"},
                        # ),
                        value="1",
                    ),
                    dmc.Tab(
                        "CC",
                        # rightSection=dmc.Badge(
                        #     "8",
                        #     size="xs",
                        #     p=0,
                        #     variant="filled",
                        #     sx={"width": 16, "height": 16, "pointerEvents": "none"},
                        #     color="green",
                        # ),
                        value="2",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dmc.Group(
                [
                    dcc.Clipboard(
                        id=id("clipboard"),
                        style={"display": "inline-block", "color": "orange"},
                        title="复制到粘贴板",
                    ),
                    dmc.ActionIcon(
                        DashIconify(
                            icon="material-symbols:download",
                            width=20,
                        ),
                        id=id("download"),
                        color="blue",
                        # variant="light",
                    ),
                    dmc.ActionIcon(
                        DashIconify(
                            icon="material-symbols:update",
                            width=20,
                        ),
                        id=id("update"),
                        color="green",
                        # variant="light",
                    ),
                ],
                # position="apart",
            ),
            task_table,
        ],
        color="red",
        value="1",
        id=id("open-tabs"),
    )
    return task


def create_work_division():
    work_division_table = dag.AgGrid(
        id=id("work-division-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"headerName": "ID", "field": "id", "hide": True},
            {
                "field": "action",
                "headerName": "ACTION",
                # "cellEditor": "agSelectCellEditor",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["update", "delete", "add"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 130,
                # "cellEditorParams": {"values": ["", "update", "delete", "add"]},
            },
            {"field": "category_1", "headerName": "材料类别1"},
            {"field": "category_2", "headerName": "材料类别2"},
            {"field": "category_3", "headerName": "材料类别3"},
            {"field": "pur_keyword_pn", "headerName": "料号匹配"},
            {"field": "pur_keyword_des", "headerName": "描述匹配"},
            {
                "field": "owner_ce",
                "headerName": "负责人",
                # "cellEditor": "agSelectCellEditor",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.ce,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                # "cellEditor": "agSelectCellEditor",
                # "cellEditorParams": {"values": ces},
            },
        ],
        columnSize="sizeToFit",
        defaultColDef={
            "editable": True,
            "resizable": True,
            "sortable": True,
            "filter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
    )
    work_division = dmc.Stack(
        [
            work_division_table,
            dmc.Button("提交", id=id("work-division-submit-btn")),
        ]
    )
    return work_division


styles = {
    "root": {
        "display": "flex",
        "flexDirection": "row",
        "alignItems": "center",
    },
    "label": {
        "width": 100,
        "fontSize": 13,
    },
    "input": {
        "width": 180,
    },
}


def search_layout():
    sql = "select distinct applicant from ce.task"
    df = read_sql(sql)
    applicant = df["applicant"].str.title().tolist()

    sql = "select distinct dept from ce.task"
    df = read_sql(sql)
    dept = df["dept"].str.upper().tolist()

    sql = "select distinct ce from ce.task"
    df = read_sql(sql)
    ce = df["ce"].str.title().tolist()

    date_obj1 = "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.start_date)"
    date_obj2 = "d3.timeParse('%Y-%m-%dT%H:%M:%S')(params.data.end_date)"
    div = dmc.Stack(
        [
            dmc.SimpleGrid(
                [
                    dmc.Select(
                        data=applicant,
                        searchable=True,
                        size="xs",
                        id=id("applicant"),
                        styles=styles,
                        label="申请人",
                        clearable=True,
                    ),
                    dmc.Select(
                        data=dept,
                        searchable=True,
                        size="xs",
                        id=id("dept"),
                        label="申请部门",
                        styles=styles,
                        clearable=True,
                    ),
                    dmc.Select(
                        data=ce,
                        searchable=True,
                        size="xs",
                        id=id("ce"),
                        label="处理人",
                        styles=styles,
                        clearable=True,
                    ),
                    dmc.MultiSelect(
                        data=[
                            "失效分析",
                            "料号申请",
                            "材料调查",
                            "应用支持",
                            "替代料",
                            "NUDD",
                            "DFX",
                            "BOM review",
                            "部门owner",
                            "新料号申请",
                            "其他",
                            "新厂商导入",
                            "品质稽核",
                            "年度稽核",
                            "材料专案",
                            "材料挑战",
                        ],
                        searchable=True,
                        size="xs",
                        id=id("type"),
                        label="工作类型",
                        styles=styles,
                        clearable=True,
                    ),
                    dmc.Select(
                        data=["Y", "N"],
                        size="xs",
                        id=id("status"),
                        label="是否结案",
                        styles=styles,
                        clearable=True,
                    ),
                    dmc.TextInput(
                        size="xs",
                        id=id("deltapn"),
                        label="材料料号",
                        styles=styles,
                    ),
                    dmc.TextInput(
                        size="xs",
                        id=id("mfgname"),
                        label="厂商",
                        styles=styles,
                    ),
                    dmc.TextInput(
                        size="xs",
                        id=id("mfgpn"),
                        label="厂商料号",
                        styles=styles,
                    ),
                    dmc.DateRangePicker(
                        id=id("start_date"),
                        dropdownType="modal",
                        size="xs",
                        label="申请日期",
                        styles=styles,
                        inputFormat="YYYY-MM-DD",
                        clearable=True,
                    ),
                    # dmc.DateRangePicker(
                    #     id=id("end_date"),
                    #     size="xs",
                    #     dropdownType="modal",
                    #     label="完成日期",
                    #     styles=styles,
                    # ),
                ],
                cols=5,
            ),
            dmc.Group(
                [
                    dmc.Button("查询", id=id("search"), size="xs"),
                    dmc.Button(
                        "下载", id=id("download-btn"), color="yellow", size="xs"
                    ),
                    dmc.Button("清空", id=id("clear"), color="green", size="xs"),
                    dcc.Download(id=id("download-data")),
                ],
                spacing="xl",
                position="apart",
                # grow=True,
            ),
            Grid(
                id=id("search-result"),
                columnDefs=[
                    # {"field": "urgent", "headerName": "紧急程度"},
                    {"field": "id", "headerName": "id", "width": 70, "hide": True},
                    {
                        "field": "type",
                        "headerName": "主类型",
                        "width": 100,
                        "cellRenderer": "markdown",
                        "linkTarget": "_blank",
                    },
                    {"field": "sub_type", "headerName": "次类型", "width": 100},
                    {"field": "deltapn", "headerName": "料号", "width": 130},
                    {"field": "mfgname", "headerName": "厂商", "width": 100},
                    {"field": "mfgpn", "headerName": "厂商料号", "width": 100},
                    {"field": "dept", "headerName": "申请部门", "width": 120},
                    {"field": "applicant", "headerName": "申请人", "width": 110},
                    {"field": "status", "headerName": "状态", "width": 100},
                    {"field": "ce", "headerName": "处理人", "width": 100},
                    {"field": "rd_remark", "headerName": "RD备注", "width": 100},
                    {"field": "ce_comment", "headerName": "CE备注", "width": 100},
                    {"field": "conclusion", "headerName": "结论", "width": 100},
                    {
                        "field": "final_report",
                        "headerName": "Final报告",
                        "width": 100,
                        "cellRenderer": "DownloadLink",
                        # "cellRenderer": "markdown",
                        # "linkTarget": "_blank",
                    },
                    {
                        "field": "burn_mark",
                        "headerName": "BurnMark",
                        "width": 100,
                        "cellRenderer": "DownloadLink",
                        # "cellRenderer": "markdown",
                        # "linkTarget": "_blank",
                    },
                    {
                        "field": "start_date",
                        "headerName": "开始日期",
                        "width": 110,
                        "valueGetter": {"function": date_obj1},
                        "valueFormatter": {
                            "function": "params.value?d3.timeFormat('%y/%m/%d')(params.value):''"
                        },
                    },
                    {
                        "field": "end_date",
                        "headerName": "结束日期",
                        "width": 110,
                        "valueGetter": {"function": date_obj2},
                        "valueFormatter": {
                            "function": "params.value?d3.timeFormat('%y/%m/%d')(params.value):''"
                        },
                    },
                ],
                rowData=[],
                style={"height": 330},
            ),
        ]
    )
    return div


def workdata_layout():
    div = fac.DraggerUpload(
        apiUrl="/upload/",
        text="上传附件",
        id=id("attachment"),
        lastUploadTaskRecord={},
        # uploadId=attachment,
        # defaultFileList=fl5,
    )
    return dmc.Stack(
        [
            fac.Center(fac.Text("工作数据", strong=True)),
            div,
            dmc.Button("提交", id=id("submit")),
        ]
    )


def layout(page=None, **kwargs):
    if page is None:
        content = html.Div("首页")
    elif page == "open":
        content = create_task_table()
    elif page == "ongoing":
        content = create_task_table()
    elif page == "close":
        content = create_task_table()
    elif page == "workdata":
        content = workdata_layout()
    elif page == "division":
        content = create_work_division()
    elif page == "query":
        content = search_layout()
    else:
        content = html.Div("首页")

    sidebar = create_sidebar(page, menu_items)

    appshell = dmc.AppShell(
        content,
        navbar=sidebar,
        style={"position": "fixed"},
    )
    return appshell


@callback(
    Output(id("task-table"), "rowData"),
    Input(id("open-tabs"), "value"),
    State("url", "search"),
    State("url", "pathname"),
    State("user", "data"),
    prevent_initial_call=False,
)
def task_table_data(tabs, search, pathname, user):
    if (pathname != "/ce") or (not search):
        raise PreventUpdate

    qsl = parse_search(search)
    if qsl.get("page") not in ("open", "ongoing", "close"):
        raise PreventUpdate

    status = qsl.get("status")
    nt_name = user.get("nt_name").lower()

    if tabs == "1":
        if nt_name in cfg.ce_admin:
            if status == "open":
                sql = "select * from ce.task where (ce like %s and status=%s) or (status=%s)"
                params = [f"%{nt_name}%", "open", "approve"]
            elif status == "ongoing":
                sql = "SELECT * FROM ce.task where ce like %s and status=%s"
                params = [f"%{nt_name}%", "ongoing"]
            else:
                sql = "select * from ce.task where (ce like %s and status=%s) or (urgent=%s and status=%s)"
                params = [f"%{nt_name}%", "close", "紧急", "close"]
        else:
            sql = "select * from ce.task where ce like %s and status in %s"
            params = [f"%{nt_name}%", STATUS_DICT.get(status)]
    else:
        sql = "select * from ce.task where cc like %s and status in %s"
        params = [f"%{nt_name}%", STATUS_DICT.get(status)]

    df = read_sql(sql, params=params)
    if df.empty:
        raise PreventUpdate

    df["status"] = df["status"].apply(lambda x: STATUS_MAP.get(x))
    c1 = df["type"] == "失效分析"
    df["type"] = np.where(
        c1,
        df.apply(lambda x: f"[失效分析](/ce/fa/ce?task={x['id']})", axis=1),
        df["type"],
    )
    c1 = df["type"] == "料号申请"
    df["path"] = df["sub_type"].apply(lambda x: sub_type_dict.get(x))
    df["type"] = np.where(
        c1,
        df.apply(
            lambda x: f"[料号申请](/ce/pn/{x['path']}/ce?task={x['id']})",
            axis=1,
        ),
        df["type"],
    )
    # c1 = df["type"] == "材料调查"
    # df["type"] = np.where(
    #     c1,
    #     df.apply(lambda x: f"[材料调查](/ce/survey/ce?task={x['id']})", axis=1),
    #     df["type"],
    # )
    c1 = df["type"].isin(
        ["NUDD", "新厂商导入", "品质稽核", "年度稽核", "材料专案", "材料挑战"]
    )
    df["type"] = np.where(
        c1,
        df.apply(lambda x: f"[{x['type']}](/ce/special?tid={x['id']})", axis=1),
        df["type"],
    )
    return df.to_dict("records")


@callback(
    Output("global-notice", "children"),
    Input(id("task-table"), "cellValueChanged"),
)
def update_ce(cell_changed):
    """转单给"""
    if not cell_changed:
        raise PreventUpdate

    cell_changed = cell_changed[0]
    colid = cell_changed.get("colId")
    id = cell_changed.get("data").get("id")

    if colid == "ce":
        ce = cell_changed.get("value")
        if not ce:
            raise PreventUpdate
        db.update("ce.task", {"ce": ce, "id": id})
        return notice(f"转单给{ce}成功")

    elif colid == "ce_comment":
        comment = cell_changed.get("value")
        if not comment:
            raise PreventUpdate
        db.update("ce.task", {"ce_comment": comment, "id": id})
        return notice("CE备注成功")
    else:
        raise PreventUpdate


@callback(
    Output(id("work-division-table"), "rowData"),
    Input(id("work-division-table"), "id"),
    prevent_initial_call=False,
)
def work_division_table_data(id):
    sql = "select id,category_1,category_2,category_3,\
        pur_keyword_pn,pur_keyword_des,owner_ce \
            from ssp_ce.a_mat_catalogue"
    df = read_sql(sql)
    return df.to_dict(orient="records")


@callback(
    Output(id("work-division-table"), "rowData"),
    Input(id("work-division-table"), "cellValueChanged"),
    State(id("work-division-table"), "rowData"),
)
def work_division_table_cell_edited(changed, data):
    if not changed:
        raise PreventUpdate
    changed = changed[0]
    if (changed.get("colId") == "action") and (changed.get("newValue") == "add"):
        idx = changed.get("rowIndex")
        data[idx].update({"action": ""})
        data.insert(idx + 1, {"action": "add"})
        return data
    else:
        raise PreventUpdate


@callback(
    Output("global-notice", "children"),
    Output(id("work-division-table"), "rowData"),
    Input(id("work-division-submit-btn"), "n_clicks"),
    State(id("work-division-table"), "rowData"),
)
def work_division_submit(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)

    add = df.query("action=='add'").drop(["id", "action"], axis=1)
    if not add.empty:
        data = add.to_dict(orient="records")
        for item in data:
            db.insert("ssp_ce.a_mat_catalogue", item)

    update = df.query("action=='update'").drop("action", axis=1)
    if not update.empty:
        data = update.to_dict(orient="records")
        for item in data:
            db.update("ssp_ce.a_mat_catalogue", item)

    delete = df.query("action=='delete'").drop("action", axis=1)
    if not delete.empty:
        data = delete[["id"]].to_dict(orient="records")
        for item in data:
            db.delete("ssp_ce.a_mat_catalogue", item)
    df["action"] = ""
    return notice(), df.to_dict(orient="records")


@callback(
    Output(id("applicant"), "value"),
    Output(id("dept"), "value"),
    Output(id("ce"), "value"),
    Output(id("status"), "value"),
    Output(id("deltapn"), "value"),
    Output(id("mfgname"), "value"),
    Output(id("mfgpn"), "value"),
    Output(id("start_date"), "value"),
    Output(id("type"), "value"),
    Output(id("search-result"), "rowData"),
    Input(id("clear"), "n_clicks"),
)
def clear_search_result(n_clicks, *args):
    if n_clicks is None:
        raise PreventUpdate
    return [None] * 10


@callback(
    Output(id("search-result"), "rowData"),
    Input(id("search"), "n_clicks"),
    State(id("applicant"), "value"),
    State(id("dept"), "value"),
    State(id("ce"), "value"),
    State(id("status"), "value"),
    State(id("deltapn"), "value"),
    State(id("mfgname"), "value"),
    State(id("mfgpn"), "value"),
    State(id("start_date"), "value"),
    State(id("type"), "value"),
)
def search_result(
    n_clicks,
    applicant,
    dept,
    ce,
    status,
    deltapn,
    mfgname,
    mfgpn,
    start_date,
    type,
):
    if n_clicks is None:
        raise PreventUpdate

    where = []
    params = []
    if applicant:
        where.append("applicant=%s")
        params.append(applicant)
    if dept:
        where.append("dept=%s")
        params.append(dept)
    if ce:
        where.append("ce=%s")
        params.append(ce)
    if status:
        if status == "Y":
            where.append("status = %s")
        else:
            where.append("status != %s")
        params.append("close")
    if deltapn:
        where.append("deltapn like %s")
        params.append(f"%{deltapn}%")
    if mfgname:
        where.append("mfgname like %s")
        params.append(f"%{mfgname}%")
    if mfgpn:
        where.append("mfgpn like %s")
        params.append(f"%{mfgpn}%")
    if start_date:
        where.append("start_date between %s and %s")
        params.extend(start_date)
    if type:
        where.append("type in %s")
        params.append(type)

    if where:
        where = " and ".join(where)
        sql = f"select * from ce.task where {where}"
    else:
        sql = "select * from ce.task limit 2000"
    df = read_sql(sql, params=params)
    df["original_type"] = df["type"]

    df1 = df.loc[df["type"] == "失效分析"]
    if not df1.empty:
        sql = "select task_id as id,ce_remark,conclusion,final_report,burn_mark \
            from ce.fa where task_id in %s"
        params = [df1["id"].unique().tolist()]
        dfi = read_sql(sql, params=params)
        df = df.merge(dfi, on="id", how="left")
        df["ce_remark"] = df["ce_remark"].fillna("[]")
        df["ce_remark"] = df["ce_remark"].apply(orjson.loads)
        df["ce_remark"] = df["ce_remark"].apply(
            lambda x: "&char(10)&".join(
                f'"{i.get("time")}({i.get("comment")})"' for i in x if i.get("comment")
            )
        )
        df["ce_comment"] = np.where(
            df["ce_remark"] != "", "=" + df["ce_remark"], df["ce_comment"]
        )
        df.drop(["ce_remark"], axis=1, inplace=True)
        # df["final_report"] = np.where(
        #     df["final_report"] != "",
        #     df["final_report"].apply(lambda x: f"**[点击下载](/upload/{x})**"),
        #     df["final_report"],
        # )
        # df["burn_mark"] = np.where(
        #     df["burn_mark"] != "",
        #     df["burn_mark"].apply(lambda x: f"**[点击下载](/upload/{x})**"),
        #     df["burn_mark"],
        # )
    for i, j in sub_type_dict.items():
        dfi = df.loc[df["sub_type"] == i]
        if not dfi.empty:
            sql = f"select task_id,ce_remark from ce.pn_{j} where task_id in %s"
            params = [dfi["id"].unique().tolist()]
            dfii = read_sql(sql, params=params)
            df = df.merge(dfii, left_on="id", right_on="task_id", how="left")
            df["ce_comment"] = np.where(
                df["ce_remark"].notna(), df["ce_remark"], df["ce_comment"]
            )
            df["qty"] = np.where(df["task_id"].notna(), 1, df["qty"])
            df.drop(["ce_remark", "task_id"], axis=1, inplace=True)

    df["type"] = np.where(
        df["type"] == "材料调查",
        df.apply(lambda x: f"**[材料调查](/ce/survey/ce?task={x['id']})**", axis=1),
        df["type"],
    )
    df["type"] = np.where(
        df["type"] == "失效分析",
        df.apply(lambda x: f"**[失效分析](/ce/fa/ce?task={x['id']})**", axis=1),
        df["type"],
    )
    df["type"] = np.where(
        df["type"] == "料号申请",
        df.apply(
            lambda x: f"**[料号申请](/ce/pn/{sub_type_dict.get(x['sub_type'])}/ce?task={x['id']})**",
            axis=1,
        ),
        df["type"],
    )
    df["type"] = np.where(
        df["type"].isin(
            [
                "NUDD",
                "新厂商导入",
                "品质稽核",
                "年度稽核",
                "材料专案",
                "材料挑战",
            ]
        ),
        df.apply(
            lambda x: f"**[{x['type']}](/ce/special?tid={x['id']})**",
            axis=1,
        ),
        df["type"],
    )

    return df.to_dict(orient="records")


@callback(
    Output(id("download-data"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State(id("search-result"), "rowData"),
)
def download_search_result(n_clicks, data):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    df["type"] = df["original_type"]
    columns = [
        "id",
        "urgent",
        "status",
        "model",
        "type",
        "sub_type",
        "dept",
        "applicant",
        "ce",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "cat1",
        "cat2",
        "cat3",
        "rd_remark",
        "ce_comment",
        "qty",
        "conclusion",
        "final_report",
        "burn_mark",
        "start_date",
        "end_date",
    ]
    df = df.reindex(columns=columns)
    df["final_report"] = df["final_report"].fillna("")
    df["burn_mark"] = df["burn_mark"].fillna("")
    df["final_report"] = np.where(
        df["final_report"] != "",
        "http://sup.deltaww.com/upload/" + df["final_report"],
        df["final_report"],
    )
    df["burn_mark"] = np.where(
        df["burn_mark"] != "",
        "http://sup.deltaww.com/upload/" + df["burn_mark"],
        df["burn_mark"],
    )

    wb = openpyxl.Workbook()
    ws = wb.active

    data = [df.columns.tolist()]
    data += df.values.tolist()
    ws.column_dimensions["Q"].width = 30
    ws.row_dimensions[1].font = Font(bold=True)

    for index, row in enumerate(data, start=1):
        for col_num, value in enumerate(row, start=1):
            ws.cell(row=index, column=col_num).value = value
            if index == 1:
                ws.cell(row=index, column=col_num).font = Font(bold=True)
            if col_num == 20:
                ws.cell(row=index, column=col_num).alignment = Alignment(wrap_text=True)
    bio = BytesIO()
    wb.save(bio)
    bio.seek(0)
    workbook = bio.read()

    return dcc.send_bytes(workbook, "CE查询结果.xlsx")


@callback(
    Output(id("clipboard"), "content"),
    Input(id("clipboard"), "n_clicks"),
    State(id("task-table"), "selectedRows"),
    State(id("task-table"), "columnDefs"),
)
def selected_to_clipboard(n, selected, columns):
    if selected is None:
        raise PreventUpdate
    dff = pd.DataFrame(selected)
    dff = dff.reindex(columns=[i.get("field") for i in columns])
    dff = dff.rename(columns={i.get("field"): i.get("headerName") for i in columns})
    return dff.to_string()


@callback(
    Output(id("task-table"), "exportDataAsCsv"),
    Input(id("download"), "n_clicks"),
)
def export_data_as_csv(n_clicks):
    if n_clicks:
        return True
    return False
