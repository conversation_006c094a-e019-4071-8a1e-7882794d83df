# -*- coding: utf-8 -*-
import os
from datetime import datetime

from pony.orm import Database, Json, Optional, Required

db = Database()
db.bind(
    provider="mysql",
    host=os.getenv("MYSQL_HOST"),
    user=os.getenv("MYSQL_USER"),
    passwd=os.getenv("MYSQL_PASSWORD"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    db=os.getenv("MYSQL_DATABASE"),
)


class User(db.Entity):
    onno = Optional(int)
    name = Required(str)
    nt_name = Required(str)
    area = Required(str)
    dept = Required(str)
    role_group = Required(str)
    email = Required(str)
    ext = Optional(str)
    u_date = Required(datetime)
    termdate = Optional(datetime)
    remark = Optional(str)
    dept_id = Optional(int)


class Project(db.Entity):
    gmt_create = Required(datetime)
    gmt_modified = Required(datetime)
    owner = Required(str)
    project = Required(str)
    board = Required(Json)
    sm_forecast = Optional(Json)
    pm = Required(str)
    ee = Required(str)
    me = Required(str)
    layout = Required(str)
    mag = Required(str)
    dept_id = Optional(int)


class Prt_temp(db.Entity):
    project_id = Optional(int)
    stage = Optional(int)
    # prtno=Required(str)
    proj = Required(str)
    qty = Required(int)
    # smstatus=Required(str)
    dept = Required(str)
    pm = Required(str)
    ee = Required(str)
    me = Required(str)
    mag = Required(str)
    layout = Required(str)
    appdate = Required(datetime)
    fsdate_req = Required(datetime)
    fsqty = Required(int)
    pcbpn = Optional(str)
    pcbstatus = Required(str)
    scheme = Optional(str)
    board = Optional(str)
    approved = Optional(int)
    dept_id = Optional(int)


class Prt(db.Entity):
    project_id = Optional(int)
    stage = Optional(int)
    board = Optional(str)
    fsdate_req = Required(datetime)
    qty = Required(int)
    fsqty = Required(int)
    pcbpn = Optional(str)
    pcbstatus = Required(str)


class Dept(db.Entity):
    dept = Required(str)
    sm_qty_limit = Required(int)
    toolbox_permit = Required(str)
    purchasing_permit = Required(str)
    area = Required(str)
    dept_group = Required(str)
    dept_name = Required(str)
    product_code = Optional(str)
    system_block = Optional(str)
    category = Optional(str)


db.generate_mapping(create_tables=True)
