import os
from datetime import date, datetime
from enum import Enum

import instructor
from openai import OpenAI
from pydantic import BaseModel, Field
import openai


# class Result(BaseModel):
#     result: str = Field(description="结果")
#     thinking: str = Field(description="模型思考过程")


class MaterialInfo1(BaseModel):
    type: str = Field(description="材料类型")
    power: float = Field(description="功率,单位转换成W")
    voltage: float = Field(description="标称电压,单位转换成V")
    min_voltage: float = Field(description="电压下限,单位转换成V")
    max_voltage: float = Field(description="电压上限,单位转换成V")
    mounting: str = Field(description="安装方式(SMD或DIP)")
    package: str = Field(description="封装")
    resistance: float = Field(description="阻值,单位转换成欧姆")


class MaterialInfo(BaseModel):
    deltapn: str = Field()
    summary: str = Field(description="通过描述内容，尽可能详细的解读材料信息")
    interpretation: str = Field(
        description="""
        用空格拆分描述，详尽解释每个拆分单元的具体含义,格式如下:
        1.材料类型：

        2.材料封装：
     
        3.贴片还是插件(SMD/DIP):

        剩余拆分单元按上面格式补充,不要遗漏
        """
    )


def call_llm(prompt):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/Qwen/Qwen3-235B-A22B-FP8"

    # api_key = os.getenv("OPENROUTER_API_KEY")
    # base_url = "https://openrouter.ai/api/v1"
    # model = "google/gemini-2.5-flash"

    # api_key = os.getenv("siliconflow_api_key")
    # base_url = "https://api.siliconflow.cn/v1"
    # model = "Pro/moonshotai/Kimi-K2-Instruct"

    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    res = client.chat.completions.create(
        model=model,
        response_model=list[MaterialInfo],
        messages=[{"role": "user", "content": prompt}],
    )
    res = [i.model_dump() for i in res]
    return res


def call_embedding(input: list):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/jinaai/jina-embeddings-v3"
    client = openai.OpenAI(api_key=api_key, base_url=base_url)
    responses = client.embeddings.create(input=input, model=model)
    result = [data.embedding for data in responses.data]
    return result
