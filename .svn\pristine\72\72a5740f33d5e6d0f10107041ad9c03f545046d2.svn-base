# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import dash_table, dcc

from common import id_factory
from components import CeCancelAIO, CeRejectAIO

# from dbtool import db
from utils import db

id = id_factory(__name__)


def task_information(task, customer, attach):
    dept = db.find_one("ssp.dept", {"id": task.get("dept_id")})
    settings = db.find_one("ce.settings", {"dept_id": task.get("dept_id")})
    return fac.AntdDescriptions(
        [
            fac.AntdDescriptionItem(task.get("dept"), label="部门"),
            fac.AntdDescriptionItem(task.get("applicant"), label="申请人"),
            fac.AntdDescriptionItem(task.get("type"), label="类型"),
            fac.AntdDescriptionItem(task.get("status"), label="状态"),
            fac.AntdDescriptionItem(dept.get("product_code"), label="产品代码"),
            fac.AntdDescriptionItem(settings.get("project_name"), label="项目名称"),
            fac.AntdDescriptionItem(customer, label="客户名称"),
            fac.AntdDescriptionItem(attachment(attach), label="客户需求文件"),
        ],
        labelStyle={"fontWeight": "bold"},
    )


columns = [
    {"name": "台达料号", "id": "deltapn", "presentation": "input"},
    {"name": "描述", "id": "des", "presentation": "input"},
    {"name": "厂商", "id": "mfgname", "presentation": "input"},
    {"name": "厂商型号", "id": "mfgpn", "presentation": "input"},
    {"name": "材料类型1", "id": "cat1", "presentation": "dropdown"},
    {"name": "材料类型2", "id": "cat2", "presentation": "dropdown"},
    {"name": "材料类型3", "id": "cat3", "presentation": "dropdown"},
    # {"name": "部门", "id": "dept", "presentation": "input"},
    # {"name": "产品代码", "id": "product_code", "presentation": "input"},
    # {"name": "项目名称", "id": "project_name", "presentation": "input"},
    # {"name": "申请人", "id": "applicant", "presentation": "input"},
    {"name": "厂商代码", "id": "mfg_code", "presentation": "input"},
    {"name": "新台达料号", "id": "new_deltapn", "presentation": "input"},
    {"name": "新描述", "id": "new_des", "presentation": "input"},
    {"name": "新厂商", "id": "new_mfgname", "presentation": "input"},
    {"name": "新厂商型号", "id": "new_mfgpn", "presentation": "input"},
    {"name": "CE备注", "id": "ce_remark", "presentation": "input"},
]


def table(data):
    table = dash_table.DataTable(
        data=data,
        columns=columns,
        editable=True,
        row_deletable=True,
        is_focused=True,
        id=id("table"),
        style_cell={
            "whiteSpace": "normal",
            "height": "auto",
            "textAlign": "left",
            "font-family": "Helvetica",
            "font-size": "10px",
        },
        css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    )
    return table


title = dmc.Center(dmc.Text("客户专用申请单", weight=700))


def customer(customer):
    return dmc.TextInput(
        label="客户名称(Customer)",
        withAsterisk=True,
        size="xs",
        style={"width": 200},
        value=customer,
    )


def attachment(attachment):
    return dmc.Anchor(
        "客户需求文件",
        href=f"/upload/{attachment}",
        target="_blank",
        variant="link",
        weight=700,
        size=10,
    )


download = dmc.Group(
    [
        dmc.Button(
            "下载",
            variant="subtle",
            color="orange",
            size="xs",
            id=id("download-btn"),
        ),
        dcc.Download(id=id("download")),
    ],
    position="right",
)


def layout(**query):
    task_id = query.get("task")

    task = db.find_one("ce.task", {"id": task_id})
    pn = db.execute("select * from ce.pn_customer where task_id = %s", (task_id,))
    cust = pn[0].get("customer")
    attach = pn[0].get("attachment")
    layout = dmc.Container(
        dmc.Stack(
            [
                download,
                title,
                dmc.Divider(),
                task_information(task, cust, attach),
                # customer(cust),
                table(pn),
                # attachment(attach),
                dmc.Group(
                    [
                        dmc.Button("提交", id=id("submit")),
                        CeRejectAIO(__name__),
                        CeCancelAIO(__name__),
                    ],
                    grow=True,
                ),
            ]
        )
    )
    return layout
