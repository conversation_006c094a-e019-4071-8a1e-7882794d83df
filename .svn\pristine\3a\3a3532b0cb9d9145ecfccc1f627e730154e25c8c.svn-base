function hasClass(elem, cls) {
  cls = cls || '';
  if (cls.replace(/\s/g, '').length == 0) return false;
  return new RegExp(' ' + cls + ' ').test(' ' + elem.className + ' ');
}
function addClass(elem, cls) {
  if (!hasClass(elem, cls)) {
    elem.className += ' ' + cls;
  }
}
function removeClass(elem, cls) {
  if (hasClass(elem, cls)) {
    var newClass = ' ' + elem.className.replace(/[\t\r\n]/g, '') + ' ';
    while (newClass.indexOf(' ' + cls + ' ') >= 0) {
      newClass = newClass.replace(' ' + cls + ' ', ' ');
    }
    elem.className = newClass.replace(/^\s+|\s+$/g, '');
  }
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
  clientside: {
    flex_size: function (value) {
      var jmz = {};
      jmz.GetLength = function (str) {
        return str.replace(/[\u0391-\uFFE5]/g, "aa").length;   //先把中文替换成两个字节的英文，在计算长度
      };
      var l = jmz.GetLength(value);
      if (l > 1)
        if (l < 30)
          return l + 2;
        else
          return 30;
      else
        return 1;
    },
    flex_width: function (id) {
      var tTD;      //用来存储当前更改宽度的Table Cell,避免快速移动鼠标的问题
      var table = document.getElementById(id);
      for (j = 0; j < table.rows[0].cells.length; j++) {
        table.rows[0].cells[j].onmousedown = function () {
          //记录单元格
          tTD = this;
          if (event.offsetX > tTD.offsetWidth - 10) {
            tTD.mouseDown = true;
            tTD.oldX = event.x;
            tTD.oldWidth = tTD.offsetWidth;
          }
          //记录Table宽度
          //table = tTD; while (table.tagName != ‘TABLE') table = table.parentElement;
          //tTD.tableWidth = table.offsetWidth;
        };
        table.rows[0].cells[j].onmouseup = function () {
          //结束宽度调整
          if (tTD == undefined) tTD = this;
          tTD.mouseDown = false;
          tTD.style.cursor = 'default';
        };
        table.rows[0].cells[j].onmousemove = function () {
          //更改鼠标样式
          if (event.offsetX > this.offsetWidth - 10)
            this.style.cursor = 'col-resize';
          else
            this.style.cursor = 'default';
          //取出暂存的Table Cell
          if (tTD == undefined) tTD = this;
          //调整宽度
          if (tTD.mouseDown != null && tTD.mouseDown == true) {
            tTD.style.cursor = 'default';
            if (tTD.oldWidth + (event.x - tTD.oldX) > 0)
              tTD.width = tTD.oldWidth + (event.x - tTD.oldX);
            //调整列宽
            tTD.style.width = tTD.width;
            tTD.style.cursor = 'col-resize';
            //调整该列中的每个Cell
            table = tTD;
            while (table.tagName != 'TABLE') table = table.parentElement;
            for (j = 0; j < table.rows.length; j++) {
              table.rows[j].cells[tTD.cellIndex].width = tTD.width;
            }
            //调整整个表
            //table.width = tTD.tableWidth + (tTD.offsetWidth – tTD.oldWidth);
            //table.style.width = table.width;
          }
        };
      };
      return 'sm';
    },
    input_search: function (value, id, label) {
      var storeId = document.getElementById(id);//获取table的id标识
      var rowsLength = storeId.rows.length;//表格总共有多少行
      var colsLength = storeId.rows[0].cells.length;
      for (var j = 0; j < colsLength; j++) {
        var searchlabel = storeId.rows[0].cells[j].innerHTML;
        console.log(searchlabel);
        console.log(label);
        if (searchlabel.match(label)) {
          var searchCol = j;
        }
        else {
          var searchCol = 0;//要搜索的哪一列，这里是第一列，从0开始数起
        }
      };
      var key = value;//获取输入框的值
      for (var i = 1; i < rowsLength; i++) {//按表的行数进行循环，本例第一行是标题，所以i=1，从第二行开始筛选（从0数起）
        var searchText = storeId.rows[i].cells[searchCol].innerHTML;//取得table行，列的值
        if (searchText.toLowerCase().match(key)) {//用match函数进行筛选，如果input的值，即变量 key的值为空，返回的是ture，
          storeId.rows[i].style.display = '';//显示行操作，
        }
        else {
          storeId.rows[i].style.display = 'none';//隐藏行操作
        }
      };
      return 'Search';
    },
    detect_browser: function (id) {
      var agent = navigator.userAgent.toLowerCase();
      if (agent.indexOf('mobile') > 0 || agent.indexOf('chrome') > 0 && agent.indexOf('edge') < 0) {
        return 'chrome';
      }
      else {
        return 0;
      }
    },
    btn_color: function (value) {
      if (value)
        return 'success';
      else
        return 'secondary';
    },
    mouse_over: function (ida, idb) {
      var obj1 = document.getElementById(ida);
      var obj2 = document.getElementById(idb);
      obj1.onmouseover = function () {
        obj2.style.display = 'block';
      }
      obj1.onmouseout = function () {
        obj2.style.display = 'none';
      }
      return 'green'
    },
    // hasClass:function(elem, cls){
    //     cls = cls || '';
    //     if(cls.replace(/\s/g, '').length == 0) return false;
    //     return new RegExp(' ' + cls + ' ').test(' ' + elem.className + ' ');
    // },
    project_status: function (id) {
      if (document.querySelectorAll(".s-step1").hasClass("active")) {
        document.querySelectorAll(".s-step1>b>b,.s-step1>div").addClass("active");
        document.querySelectorAll(".s-step2>em,.s-step3>em,.s-step4>em,.s-step5>em").hide();
      }
    },
    star_hover: function (id, ida, idb) {
      var obj = document.getElementById(id);
      var obja = document.getElementById(ida);
      var objb = document.getElementById(idb);
      var obj1 = obj.children[0];
      var obj2 = obj.children[1];
      var obj3 = obj.children[2];
      var obj4 = obj.children[3];
      var obj5 = obj.children[4];
      // alert(obj.children[1]);
      obj1.onclick = function () {
        obj1.classList.add("cs");
        obj2.classList.remove("cs");
        obj3.classList.remove("cs");
        obj4.classList.remove("cs");
        obj5.classList.remove("cs");
        obja.innerHTML = "&#9785;";
        // objb.innerText = "非常不满意！！";
      }
      obj1.onmouseover = function () {
        obj1.classList.add("hs");
      }
      obj1.onmouseout = function () {
        obj1.classList.remove("hs");
      }
      obj2.onclick = function () {
        obj1.classList.add("cs");
        obj2.classList.add("cs");
        obj3.classList.remove("cs");
        obj4.classList.remove("cs");
        obj5.classList.remove("cs");
        obja.innerHTML = "&#128577;";
        // objb.textContent = "不满意！";
      }
      obj2.onmouseover = function () {
        obj1.classList.add("hs");
        obj2.classList.add("hs");
      }
      obj2.onmouseout = function () {
        obj1.classList.remove("hs");
        obj2.classList.remove("hs");
      }
      obj3.onclick = function () {
        obj1.classList.add("cs");
        obj2.classList.add("cs");
        obj3.classList.add("cs");
        obj4.classList.remove("cs");
        obj5.classList.remove("cs");
        obja.innerHTML = "&#128528;";
        // objb.textContent = "一般。";
      }
      obj3.onmouseover = function () {
        obj1.classList.add("hs");
        obj2.classList.add("hs");
        obj3.classList.add("hs");
      }
      obj3.onmouseout = function () {
        obj1.classList.remove("hs");
        obj2.classList.remove("hs");
        obj3.classList.remove("hs");
      }
      obj4.onclick = function () {
        obj1.classList.add("cs");
        obj2.classList.add("cs");
        obj3.classList.add("cs");
        obj4.classList.add("cs");
        obj5.classList.remove("cs");
        obja.innerHTML = "&#128578;";
        // objb.textContent = "满意！";
      }
      obj4.onmouseover = function () {
        obj1.classList.add("hs");
        obj2.classList.add("hs");
        obj3.classList.add("hs");
        obj4.classList.add("hs");
      }
      obj4.onmouseout = function () {
        obj1.classList.remove("hs");
        obj2.classList.remove("hs");
        obj3.classList.remove("hs");
        obj4.classList.remove("hs");
      }
      obj5.onclick = function () {
        obj1.classList.add("cs");
        obj2.classList.add("cs");
        obj3.classList.add("cs");
        obj4.classList.add("cs");
        obj5.classList.add("cs");
        obja.innerHTML = "&#128522;";
        // objb.textContent = "非常满意！！";
      }
      obj5.onmouseover = function () {
        obj1.classList.add("hs");
        obj2.classList.add("hs");
        obj3.classList.add("hs");
        obj4.classList.add("hs");
        obj5.classList.add("hs");
      }
      obj5.onmouseout = function () {
        obj1.classList.remove("hs");
        obj2.classList.remove("hs");
        obj3.classList.remove("hs");
        obj4.classList.remove("hs");
        obj5.classList.remove("hs");
      }
      return { 'font-size': '50px' }
    },
    like_click: function (id1, id2) {
      var obj1 = document.getElementById(id1);
      var obj2 = document.getElementById(id2);
      obj1.onclick = function () {
        obj1.classList.add("dislike-addclass");
        obj2.classList.remove("like-addclass");
      }
      obj2.onclick = function () {
        obj1.classList.remove("dislike-addclass");
        obj2.classList.add("like-addclass");
      }
      return { "cursor": "pointer" }
    },
    submit_rate: function (id, id1, id2, id3, id4, id5) {
      var obj = document.getElementById(id);
      var obj1 = document.getElementById(id1);
      var obj2 = document.getElementById(id2);
      var obj3 = document.getElementById(id3);
      var obj4 = document.getElementById(id4);
      var obj5 = document.getElementById(id5);
      function a() {
        if (obj1.innerHTML == "" || obj2.innerHTML == "" || obj3.innerHTML == "" || obj4.innerHTML == "" || obj5.innerHTML == "") {
          obj.style.backgroundColor = "#e74c3c";
          obj.style.borderColor = "#c0392b";
          // return {"record1":obj1.innerText,"record2":"1","record3":obj3.innerText,"record4":obj4.innerText,"record5":obj5.innerText}
        }
        else {
          obj.style.backgroundColor = "#1abc9c";
          obj.style.borderColor = "#16a085";
          console.log('inner' + obj1.innerText);
        }
        return obj1.innerText
      }
      obj.onclick = a;
    },
    nav_click: function (id) {
      var obj = document.getElementById(id);
      var tag = obj.getElementsByTagName("a");
      for (var i = 0; i < tag.length; i++) {
        tag[i].onclick = function () {
          for (var j = 0; j < tag.length; j++) {
            tag[j].classList.remove("active");
          }
          this.classList.add("active");
        }
      }
      return { "cursor": "pointer" }
    },

    clipboard: function (data) {
      var dom = document.createElement("textarea");
      dom.value = data;
      // dom.setAttribute('style', 'display: block;width: 1px;height: 1px;');
      document.body.appendChild(dom);
      dom.select();
      var result = document.execCommand('copy');
      document.body.removeChild(dom);
      return result
    },
    issue_intro: function (id) {
      introJs().start();
      return 'mx-5'
    },
    materialIntroCopy: function (n) {
      let helpDialog = introJs();
      helpDialog.setOptions({
        steps: [
          {
            title: '勾选复制',
            element: document.querySelector('#search-type'),
            intro: '选择按关键字查询',
            position: 'right'
          },
          {
            title: '勾选复制',
            element: document.querySelector('#material-intro-1'),
            // intro: '<table border="1"><tr><th>0313000001</th></tr><tr><th>0313109001</th></tr><tr><th>0313000405</th></tr></table>',
            intro: '输入待查询料号',
            position: 'right'
          },
          {
            title: '勾选复制',
            element: document.querySelector('#search'),
            intro: '点击按钮进行查找',
            position: 'right'
          },
          {
            title: '勾选复制',
            element: document.querySelector('.dash-select-cell'),
            intro: '点击复选框进行勾选复制。',
            position: 'right'
          },
          {
            title: '勾选复制',
            element: document.querySelector('#query-table tr:nth-child(4) .dash-select-cell'),
            intro: '一次可以勾选复制多行。',
            position: 'right'
          },
        ]
      });
      helpDialog.onbeforechange(function (targetElement) {
        helpDialog._introItems[3].element = document.querySelector('.dash-select-cell');
        helpDialog._introItems[3].position = 'right';
        helpDialog._introItems[4].element = document.querySelector('#query-table tr:nth-child(4) .dash-select-cell');
        helpDialog._introItems[4].position = 'right';
      });
      helpDialog.start();
      return 'mx-0'
    },
    materialIntroBatchQuery: function (n) {
      let helpDialog = introJs();
      helpDialog.setOptions({
        steps: [
          {
            title: '复制查询',
            intro: '复制查询功能引导',
            position: 'right'
          },
          {
            title: '复制查询',
            element: document.querySelector('#material-intro-1'),
            intro: '<span>选中复制表格中料号并粘贴到查询框中</span><table border="1"><tr><th>0313000001</th></tr><tr><th>0313109001</th></tr><tr><th>0313000405</th></tr></table>',
            position: 'right'
          },
          {
            title: '复制查询',
            element: document.querySelector('#search'),
            intro: '点击按钮进行查找',
            position: 'right'
          },
        ]
      });
      helpDialog.start();
      return 'mx-0'
    },
    materialIntroTP: function (n) {
      let helpDialog = introJs();
      helpDialog.setOptions({
        steps: [
          {
            title: '查看TP参数',
            element: document.querySelector('#search-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">按类别查询</span></p>',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#database-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">常用料</span>（只有选择常用料才可以查看TP参数）</p>',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#category-1'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Active</span></p>',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#category-2'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Diode</span></p>',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#category-3'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">ESD Diode</span></p>',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#category-search'),
            intro: '点击查询',
            position: 'right'
          },
          {
            title: '查看TP参数',
            element: document.querySelector('#query-table tr:nth-child(3) td:nth-child(2)'),
            intro: '选择参数，即可查看TP参数',
            position: 'right'
          },
        ]
      });
      helpDialog.onbeforechange(function (targetElement) {
        helpDialog._introItems[6].element = document.querySelector('#query-table tr:nth-child(3) td:nth-child(2)');
        helpDialog._introItems[6].position = 'right';
      });
      helpDialog.start();
      return 'mx-0'
    },
    materialIntroFilter: function (n) {
      let helpDialog = introJs();
      helpDialog.setOptions({
        steps: [
          {
            title: '进一步筛选',
            element: document.querySelector('#search-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">按类别查询</span></p>',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#database-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">SAP</span></p>',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#category-1'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Active</span></p>',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#category-2'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Diode</span></p>',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#category-3'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">BRD</span></p>',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#category-search'),
            intro: '点击查询',
            position: 'right'
          },
          {
            title: '进一步筛选',
            element: document.querySelector('#query-table tr:nth-child(2) th:nth-child(4)'),
            intro: '<p>按条件进行筛选，比如输入<span style="color:#1abc9c;font-weight:bold;">3A*800V*SMD</span>按<span style="color:#1abc9c;font-weight:bold;">回车键</span>进行查询</p>',
            position: 'right'
          },
        ]
      });
      helpDialog.onbeforechange(function (targetElement) {
        helpDialog._introItems[6].element = document.querySelector('#query-table tr:nth-child(2) th:nth-child(4)');
        helpDialog._introItems[6].position = 'right';
      });
      helpDialog.start();
      return 'mx-0'
    },
    materialIntroTableInput: function (n) {
      let helpDialog = introJs();
      helpDialog.setOptions({
        steps: [
          {
            title: '表格输入',
            element: document.querySelector('#search-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">按类别查询</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#database-type'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">SAP</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#category-1'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Active</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#category-2'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">Diode</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#category-3'),
            intro: '<p>选择参数<span style="color:#1abc9c;font-weight:bold;">BRD</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#category-search'),
            intro: '点击查询',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#query-table tr:nth-child(3) td:nth-child(2)'),
            intro: '<p>选择<span style="color:#1abc9c;font-weight:bold;">领料</span></p>',
            position: 'right'
          },
          {
            title: '表格输入',
            element: document.querySelector('#tab-num li:nth-child(2)'),
            intro: '<p>点击<span style="color:#1abc9c;font-weight:bold;">库存领料</span></p>',
            position: 'bottom'
          },
          {
            title: '表格输入',
            element: document.querySelector('#picking-table tr:nth-child(2) td:nth-child(8)'),
            intro: '<p><span style="color:#1abc9c;font-weight:bold;">单击</span>单元格输入数量</p>',
            position: 'left'
          },
        ]
      });
      helpDialog.onbeforechange(function (targetElement) {
        helpDialog._introItems[6].element = document.querySelector('#query-table tr:nth-child(3) td:nth-child(2)');
        helpDialog._introItems[6].position = 'right';
        helpDialog._introItems[8].element = document.querySelector('#picking-table tr:nth-child(2) td:nth-child(8)');
        helpDialog._introItems[8].position = 'left';
      });
      helpDialog.start();
      return 'mx-0'
    },
    updateIndexContent: function (id, user) {
      let dept = user[0];
      let roleGroup = user[1].toLowerCase();
      console.log(roleGroup)
      let count = 0;
      fetch('/assets/data.json', {
        method: 'GET',
        mode: 'cors',// 允许发送跨域请求
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache'
        }
      }).then(function (response) {
        //打印返回的json数据
        response.json().then(function (data) {
          for (let d of data) {
            let row = Math.ceil((count + 1) / 5)
            let col = count % 5 + 1
            if (d["position"] == "content") {
              if (d["dept"].includes("all") || d["dept"].includes(dept)) {
                if (d["role_group"].includes("all") || d["role_group"].includes(roleGroup)) {
                  let ele = document.querySelector(`#index-content div:nth-child(${row}) div:nth-child(${col}) div p a`)
                  let ele1 = document.querySelector(`#index-content div:nth-child(${row}) div:nth-child(${col}) div > a`)
                  let ele2 = document.querySelector(`#index-content div:nth-child(${row}) div:nth-child(${col}) div`)
                  ele2.style.visibility = "visible"
                  ele.innerHTML = d["category"]
                  ele.href = d["href"]
                  ele1.href = d["href"]
                  ele1.innerHTML = d["icon"]
                  count++;
                }
              }
            }
          }
          console.log(count)
          let r = Math.ceil(count / 5)
          console.log(r)
          let c = (count - 1) % 5 + 1
          console.log(c)
          for (let i = 3; i > r; --i) {
            let rowHidden = document.querySelector(`#index-content>div:nth-child(${i})`)
            rowHidden.style.display = "none"
          }
          for (let j = 5; j > c; --j) {
            let colHidden = document.querySelector(`#index-content>div:nth-child(${r})>div:nth-child(${j})`)
            colHidden.style.visibility = "hidden"
          }
        })
      }).catch(function (e) {
        console.log('error: ' + e.toString());
      })
      return 'white'
    },
    inputToLower: function (value, id) {
      const target = document.getElementById(id);

      target.addEventListener('paste', (event) => {
        let paste = (event.clipboardData || window.clipboardData).getData('text');
        paste = paste.toLowerCase();
        const selection = window.getSelection();
        if (!selection.rangeCount) return false;
        selection.deleteFromDocument();
        event.target.value = paste;
        event.preventDefault();
        return paste;
      });
    },
    purVendorEdited: function (e, data) {
      if (e != undefined) {
        let id = e.row.id;
        for (var i = 0; i < data.length; i++) {
          // debugger;
          if (data[i].id == id) {
            data[i]["action"] = 'update';
          }
        }
        return data;
      }
    },

    cellValueChanged: function (id, data) {
      const api = dash_ag_grid.getApi(id);
      var rowNode = api.getRowNode(data.rowIndex);
      rowNode.setDataValue('action', 'update');
    },

    // ag-grid编辑单元格时，更新action字段
    update_action: async (n, id) => {
      if (n) {
        if (n[0].colId !== 'action') {
          const gridApi = await dash_ag_grid.getApiAsync(id)
          var rowNode = gridApi.getDisplayedRowAtIndex(n[0].rowIndex);
          rowNode.setDataValue('action', 'updated');
        }
      }
      return dash_clientside.no_update
    },
    // 扫料盘码时，更新checked字段为True
    update_checked: async (n, id, uid) => {
      if (n) {
        const gridApi = await dash_ag_grid.getApiAsync(id);
        var rowCount = gridApi.getDisplayedRowCount();
        if (uid.startsWith("R")) {
          uid = uid.substring(1)
        };
        for (var i = 0; i < rowCount; i++) {
          var rowNode = gridApi.getDisplayedRowAtIndex(i);
          if (rowNode.data.uid == uid) {
            rowNode.setDataValue('checked', true);
          };

        }
      }
      return dash_clientside.no_update
    }

  }
});
