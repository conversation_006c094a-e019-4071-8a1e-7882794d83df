import functools
import os
import re
import time
import warnings
from datetime import datetime, timedelta
from typing import Any, Callable
from urllib.parse import parse_qsl, urlsplit

import duckdb as dk
import flask
import numpy as np
import pandas as pd
import polars as pl
from chinese_calendar import get_holidays
from flask import g

from config import cache, engine, logging, pool, persist
from server import server

logger = logging.getLogger(__name__)
bom_type_dict = {"EE": "b_ee", "ME": "b_me", "MAG": "b_mag"}


@server.teardown_request
def teardown_request(exception):
    db = g.pop("db", None)
    if db is not None:
        if exception:
            logger.error(f"数据库提交失败: {exception}")
            db.rollback()
        else:
            db.commit()
        db.close()


# @server.teardown_appcontext
# @server.teardown_request
# def _db_close(exc):
#     if exc:
#         print(f"teardown_request_exception:{exc}")
#     db = g.pop("db", None)
#     if db is not None:
#         if not exc:
#             try:
#                 db.commit()
#             except Exception as e:
#                 raise Exception(e)
#             finally:
#                 db.close()
#         db.close()


def timer(func: Callable = None) -> Callable:
    if func is None:
        return functools.partial(timer)

    @functools.wraps(func)
    def decorated(*args, **kwargs) -> Any:  # *args: Any,
        t1 = time.perf_counter()
        result = func(*args, **kwargs)  # *args, **kwargs
        print(f"{func.__name__} use time: {time.perf_counter() - t1:.3f} s")
        return result

    return decorated


def get_nt_name() -> str:
    user = flask.request.environ.get(
        "HTTP_X_REMOTE_USER", f"Delta\\{os.environ.get('username')}"
    )
    user = user.split("\\")[1]
    return f"{user.title()}"


# @cache.memoize(expire=24 * 3600, tag="user")
def get_ssp_user() -> pd.DataFrame:
    sql = "select nt_name,dept_id,area,onno,role_group,dept from ssp.user \
        where termdate is null"
    df = read_sql(sql)
    df["nt_name"] = df["nt_name"].str.title()
    return df


def id_factory(page: str):
    def func(_id: str):
        """
        Dash pages require each component in the app to have a totally
        unique id for callbacks. This is easy for small apps, but harder for larger
        apps where there is overlapping functionality on each page.
        For example, each page might have a div that acts as a trigger for reloading;
        instead of typing "page1-trigger" every time, this function allows you to
        just use id('trigger') on every page.

        How:
            prepends the page to every id passed to it
        Why:
            saves some typing and lowers mental effort
        **Example**
        # SETUP
        from common import id_factory
        id = id_factory(__name__) # create the id function for that page

        # LAYOUT
        layout = html.Div(
            id=id('main-div')
        )
        # CALLBACKS
        @app.callback(
            Output(id('main-div'),'children'),
            Input(id('main-div'),'style')
        )
        def funct(this):
            ...
        """
        # pre = page.rsplit(".", 1)[0].replace(".", "-").replace("_", "-")
        pre = page.replace(".", "-").replace("_", "-")
        if isinstance(_id, str):
            return f"{pre}-{_id}"
        elif isinstance(_id, dict):
            _id.update({"type": f"{pre}-{_id.get('type', '')}"})
            return _id

    return func


def read_sql(sql: str, con: str = None, params: list = None) -> pd.DataFrame:
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", UserWarning)
        with persist.connection() as conn:
            df = pd.read_sql(sql, conn, params=params)
    # conn = engine.connect()
    # df = pd.read_sql(text(sql), con=conn, params=params)
    # conn.close()
    return df


def read_db(sql: str, params: list = None, schema_overrides=None):
    df = pl.read_database(
        sql,
        connection=engine,
        execute_options={"parameters": params},
        infer_schema_length=1000,
        schema_overrides=schema_overrides,
    )
    return df


def ce_cats() -> pd.DataFrame:
    sql = "select category_1 as cat1,category_2 as cat2,category_3 as cat3 \
            from ssp_ce.a_mat_catalogue"
    df = read_sql(sql)
    return df


def mat_cats() -> tuple[list, list]:
    sql = "select distinct category_1 as cat1,pur_category2 as cat2 \
        from ssp_ce.a_mat_catalogue"
    with pool.connection() as conn:
        with conn.cursor() as cu:
            cu.execute(sql)
            res = cu.fetchall()
            cat1 = sorted(set(i["cat1"] for i in res))
            cat2 = sorted(set(i["cat2"] for i in res))
            return cat1, cat2


@cache.memoize(expire=3600)
def stock_out_handler():
    now = datetime.now()
    quarter = (now - timedelta(days=90)).date()
    half_year = (now - timedelta(days=180)).date()

    sql = "select area,checkcode,stockoutdate,qty,dept,prt_id \
    from stockout where stockoutdate>=DATE_SUB(CURDATE(), INTERVAL 1 year) \
    and dept_id in (SELECT id FROM dept WHERE purchasing_permit='Y') and type in %s \
    UNION SELECT area,checkcode,gmt_create as stockoutdate,qty,dept,prt_id \
    FROM bom_stockout where gmt_create>=DATE_SUB(CURDATE(),INTERVAL 1 year) \
    and dept_id in (SELECT id FROM dept WHERE purchasing_permit='Y') \
    and source like %s"
    params = [["SM", "Debug", "stockout"], r"%stockout%"]
    last_year_stockout = read_sql(sql, params=params)
    df1 = last_year_stockout.loc[last_year_stockout["prt_id"].isna()]
    df2 = last_year_stockout.loc[last_year_stockout["prt_id"].notna()]
    df3 = df2.drop_duplicates(subset=["prt_id", "area", "checkcode"])
    last_year_stockout = pd.concat([df1, df3])

    sql = f"select area,checkcode,count(checkcode) as half_times,\
        sum(qty) as half_qty from last_year_stockout \
        where stockoutdate>='{half_year}' group by area,checkcode"
    stockout_half_year = dk.execute(sql).df()

    sql = f"select area,checkcode,count(checkcode) as quarter_times,\
        sum(qty) as quarter_qty from last_year_stockout \
        where stockoutdate>='{quarter}' group by area,checkcode"
    stockout_quarter = dk.execute(sql).df()

    sql = f"with x as (select area,checkcode,sum(qty) as qty,dept,count(dept) as count \
    from last_year_stockout where stockoutdate>='{quarter}' group by area,\
    checkcode,dept order by area,count(dept) desc) \
    select area,checkcode,array_agg([dept,cast(qty as varchar)])[:3] as dept_qty \
    from x group by area,checkcode"
    stockout_dept = dk.execute(sql).df()
    stockout_dept["dept_qty"] = (
        stockout_dept["dept_qty"].apply(lambda x: {i[0]: i[-1] for i in x}).astype(str)
    )

    sql = "select area,checkcode,count(checkcode) as year_times,\
        sum(qty) as year_qty,max(qty) as year_max,max(stockoutdate) as latestdate \
    from last_year_stockout group by area,checkcode"
    last_year_stockout = dk.execute(sql).df()

    return (
        last_year_stockout,
        stockout_half_year,
        stockout_quarter,
        stockout_dept,
    )


def safety_stock_handler(df: pd.DataFrame):
    params = df["checkcode"].unique().tolist()
    sql = "select area,checkcode,qty as stock_qty from ssp.stock where checkcode in %s"
    stock = read_sql(sql, params=[params])

    sql = "select (select area from ssp.dept where id=dept_id) as area,\
    des,prtno,checkcode,r_qty as transit_qty from pur \
    where (application=%s or (application=%s and prtno is not null)) \
    and pur_status not in %s"
    params = ["stock", "debug", ["closed", "cancel"]]
    transit = read_sql(sql, params=params)
    c1 = transit["des"].str.contains("SMD")
    c2 = transit["area"] == "HZ"
    transit["area"] = np.where(c1 & c2, "SH", transit["area"])
    transit = transit.groupby(["area", "checkcode"])["transit_qty"].sum()

    stockout_year, stockout_half, stockout_quarter, stockout_dept = stock_out_handler()

    df = (
        df.merge(stock, on=["area", "checkcode"], how="left")
        .merge(transit, on=["area", "checkcode"], how="left")
        .merge(stockout_dept, on=["area", "checkcode"], how="left")
        .merge(stockout_year, on=["area", "checkcode"], how="left")
        .merge(stockout_half, on=["area", "checkcode"], how="left")
        .merge(stockout_quarter, on=["area", "checkcode"], how="left")
    )

    cols = [
        "stock_qty",
        "year_times",
        "year_qty",
        "year_max",
        "half_times",
        "half_qty",
        "quarter_times",
        "quarter_qty",
        "transit_qty",
    ]
    df.fillna({x: 0 for x in cols}, inplace=True)

    c1 = df["half_times"] <= 5
    c2 = (df["half_times"] > 5) & (df["half_times"] < 10)
    c3 = df["half_times"] >= 10

    df["safetystock"] = np.where(c1, df["year_max"] * 1.5, df["safetystock"])
    df["safetystock"] = np.where(c2, df["half_qty"], df["safetystock"])
    df["safetystock"] = np.where(c3, df["year_qty"], df["safetystock"])
    df["safetystock"] = np.where(
        df["custom_qty"] > 0, df["custom_qty"], df["safetystock"]
    )
    df["safetystock"] = df["safetystock"].astype(int)

    df["order_condition"] = np.where(
        c1, df["stock_qty"] + df["transit_qty"] - df["year_max"], 1
    )

    df["order_condition"] = np.where(
        c2,
        df["stock_qty"] + df["transit_qty"] - df["quarter_qty"],
        df["order_condition"],
    )

    df["order_condition"] = np.where(
        c3,
        df["stock_qty"] + df["transit_qty"] - (df["year_qty"] / 2).astype(int),
        df["order_condition"],
    )

    c4 = df["order_condition"] <= 0

    df["order_qty"] = np.where(
        c1 & c4, df["safetystock"] - df["transit_qty"] - df["stock_qty"], 0
    )

    df["order_qty"] = np.where(
        c2 & c4,
        df["safetystock"] - df["transit_qty"] - df["stock_qty"],
        df["order_qty"],
    )

    df["order_qty"] = np.where(
        c3 & c4,
        df["safetystock"] - df["transit_qty"] - df["stock_qty"],
        df["order_qty"],
    )

    c5 = df["custom_qty"] > 0

    df["order_qty"] = np.where(c5 & c4, df["pur_qty"], df["order_qty"])

    return df


def mat_category(df: pd.DataFrame) -> pd.DataFrame:
    """_判断材料类型_

    Parameters
    ----------
    df : pd.DataFrame
        _description_

    Returns
    -------
    pd.DataFrame
        _description_
    """
    df["mat_catelogue"] = None
    df["mat_group"] = None
    sql = "select category_1 as c1,pur_category2 as c2,\
        pur_keyword_pn as cond_pn,pur_keyword_des as cond_des \
        from ssp_ce.a_mat_catalogue \
        where pur_keyword_pn is not null and  pur_keyword_des is not null"
    cat = read_sql(sql)

    cat = cat.dropna(subset=["cond_pn", "cond_des"])
    cat["cond_pn"] = "^" + cat["cond_pn"] + "$"
    cat["cond_des"] = "^" + cat["cond_des"] + "$"
    cat["cond_pn"] = cat["cond_pn"].str.replace("*", ".*", regex=False)
    cat["cond_des"] = cat["cond_des"].str.replace("*", ".*", regex=False)
    for i in df.itertuples():
        for j in cat.itertuples():
            if re.match(j.cond_pn, i.deltapn) and re.match(j.cond_des, i.des):
                df["mat_catelogue"][i.Index] = j.c1
                df["mat_group"][i.Index] = j.c2
                break
    return df


def ce_mat_category(df: pd.DataFrame) -> pd.DataFrame:
    """_判断材料类型_

    Parameters
    ----------
    df : pd.DataFrame
        _description_

    Returns
    -------
    pd.DataFrame
        _description_
    """
    df["cat1"] = None
    df["cat2"] = None
    df["cat3"] = None
    sql = "select category_1 as cat1,category_2 as cat2,category_3 as cat3,\
        pur_keyword_pn as cond_pn,pur_keyword_des as cond_des \
        from ssp_ce.a_mat_catalogue \
        where pur_keyword_pn is not null and  pur_keyword_des is not null"
    cat = read_sql(sql)

    cat = cat.dropna(subset=["cond_pn", "cond_des"])
    cat["cond_pn"] = "^" + cat["cond_pn"] + "$"
    cat["cond_des"] = "^" + cat["cond_des"] + "$"
    cat["cond_pn"] = cat["cond_pn"].str.replace("*", ".*", regex=False)
    cat["cond_des"] = cat["cond_des"].str.replace("*", ".*", regex=False)
    for i in df.itertuples():
        for j in cat.itertuples():
            if re.match(j.cond_pn, i.deltapn) and re.match(j.cond_des, i.des):
                df["cat1"][i.Index] = j.cat1
                df["cat2"][i.Index] = j.cat2
                df["cat3"][i.Index] = j.cat3
                break
    return df


def df_to_html(
    df: pd.DataFrame,
    title: str = "",
    href: str = "",
    link_text="进入SSP处理",
):
    """
    Write an entire dataframe to an HTML file with nice formatting.
    """

    result = """
    <html>
    <head>
    <style>

        h2 {
            text-align: center;
            font-family: Helvetica, Arial, sans-serif;
        }
        table { 
            margin-left: auto;
            margin-right: auto;
        }
        table, th, td {
            border: 1px solid black;
            border-collapse: collapse;
            
        }
        th, td {
            padding: 5px;
            text-align: left;
            font-family: Helvetica, Arial, sans-serif;
            font-size: 90%;
        }
        th {
            background-color: #00a0e9;
            color:white;
        }
        table tbody tr:hover {
            background-color: #dddddd;
        }
        .wide {
            width: 90%; 
        }

    </style>
    </head>
    <body>
    """

    df.columns = df.columns.str.upper()
    result += f"{title}<br><br>"
    table = df.fillna("").to_html(index=False)
    result += table
    if href:
        link_style = (
            "display:block;background-color:#00a0e9;height:40px;width:40px;color:white"
        )
        link = f"<br><a href={href} style={link_style}>{link_text}</a>"
        result += link
    result += """
    </body>
    </html>
    """
    return result


def mat_info(dept_id: int, customer: str, cond: str, params: list):
    """[添加材料禁用信息备注]"""
    sql = "select * from ssp_csg.mat_status where dept_id=%s"
    df1 = read_sql(sql, params=[dept_id])
    dept_cust = f"{dept_id}:{customer}"

    sql = (
        f"select deltapn,des,mfgname,mfgpn,checkcode,bg_block,gradeb,eol,nrnd,msl_block,\
        esd_block,antis,non_auto,autom,not_in_bgallpartlist,double85,ai,ssp_block,\
        non_hf->>%s as non_hf,\
        common_part->>%s as common_part,\
        bu_std_part->>%s as bu_std_part,\
        system_block->>%s as system_block,\
        limit_use->>%s as limit_use_dept_cust, \
        limit_use->>%s as limit_use_dept_all, \
        limit_use->>%s as limit_use_all_all,\
        limit_use->>%s as limit_use_all,\
        quality_issue->>%s as quality_issue_dept_cust,\
        quality_issue->>%s as quality_issue_dept_all,\
        quality_issue->>%s as quality_issue_all_all,\
        quality_issue->>%s as quality_issue_all,\
        ce_alert->>%s as ce_alert_dept_cust,\
        ce_alert->>%s as ce_alert_dept_all,\
        ce_alert->>%s as ce_alert_all_all,\
        ce_alert->>%s as ce_alert_all\
        from ssp_csg.mat_info where {cond}"
    )
    params = [
        f'$."{dept_cust}"',
        f'$."{dept_id}"',
        f'$."{dept_id}"',
        f'$."{dept_id}"',
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
        f'$."{dept_cust}"',
        f'$."{dept_id}:ALL"',
        '$."ALL:ALL"',
        "$.*",
    ] + params
    df = read_sql(sql, params=params)

    df["limit_use"] = np.where(
        df["limit_use_all_all"].notna(), df["limit_use_all_all"], ""
    )
    df["limit_use"] = np.where(
        df["limit_use_dept_all"].notna(), df["limit_use_dept_all"], df["limit_use"]
    )
    df["limit_use"] = np.where(
        df["limit_use_dept_cust"].notna(), df["limit_use_dept_cust"], df["limit_use"]
    )

    df["quality_issue"] = np.where(
        df["quality_issue_all_all"].notna(), df["quality_issue_all_all"], ""
    )
    df["quality_issue"] = np.where(
        df["quality_issue_dept_all"].notna(),
        df["quality_issue_dept_all"],
        df["quality_issue"],
    )
    df["quality_issue"] = np.where(
        df["quality_issue_dept_cust"].notna(),
        df["quality_issue_dept_cust"],
        df["quality_issue"],
    )

    df["ce_alert"] = np.where(
        df["ce_alert_all_all"].notna(), df["ce_alert_all_all"], ""
    )
    df["ce_alert"] = np.where(
        df["ce_alert_dept_all"].notna(), df["ce_alert_dept_all"], df["ce_alert"]
    )
    df["ce_alert"] = np.where(
        df["ce_alert_dept_cust"].notna(), df["ce_alert_dept_cust"], df["ce_alert"]
    )

    df["warning"] = np.where(df["ce_alert_all"].notna(), "LowRisk", None)
    df["warning"] = np.where(df["limit_use_all"].notna(), "MediumRisk", df["warning"])
    df["warning"] = np.where(df["quality_issue_all"].notna(), "HighRisk", df["warning"])

    if not df.empty:  # ? 是否即将EOL
        params = df["deltapn"].unique().tolist()
        ph = ",".join(["%s"] * len(params))
        sql = f"select deltapn from ssp_br.z_basicdata_nrpeol \
            where deltapn in ({ph}) and status in (%s,%s)"
        params = params + ["eol", "nrp"]
        eol = read_sql(sql, params=params)

        c1 = ~df["des"].str.startswith(("EOL", "NRND"))
        c2 = df["deltapn"].isin(eol["deltapn"])
        df["warning"] = np.where(c1 & c2, "HighRisk", df["warning"])

    df1 = df1.loc[:, df1.columns.isin(df.columns)]
    df1 = df1.astype(int)
    df = df.fillna("")

    dfb = df1.loc[:, df1.iloc[0].isin([1, 2, 3])]
    cols = dfb.sort_values(0, axis=1).columns
    df["block"] = [
        ",".join(getattr(y, i) for i in cols if getattr(y, i)) for y in df.itertuples()
    ]
    # ! 0341486306 DES_CDBU和DES_IMBU不一样
    dfs = df[dfb.columns]
    dfs = dfs.replace("", 5)
    dfs = dfs.fillna(5)
    dfs = dfs.apply(lambda x: x.where(x == 5, dfb[x.name][0]))
    df["block_grade"] = dfs.min(axis=1).astype(int)

    dfr = df1.loc[:, df1.iloc[0].isin([4, 6, 7])]
    cols = dfr.sort_values(0, axis=1).columns
    df["remark"] = [
        ",".join(getattr(y, i) for i in cols if getattr(y, i)) for y in df.itertuples()
    ]

    dfs = df[dfr.columns]
    dfs = dfs.replace("", 5)
    dfs = dfs.fillna(5)
    dfs = dfs.apply(lambda x: x.where(x == 5, dfr[x.name][0]))
    df["remark_grade"] = dfs.max(axis=1).astype(int)

    # df['status']=df[['remark_grade','block_grade']].min(axis=1).astype(int)
    df["source"] = "main"
    columns = [
        "checkcode",
        "deltapn",
        "des",
        "mfgname",
        "mfgpn",
        "block_grade",
        "remark_grade",
        "block",
        "remark",
        "warning",
        "antis",
        "double85",
        "ai",
        "source",
    ]
    df = df.reindex(columns=columns)
    return df


def add_mat_category(df: pd.DataFrame) -> pd.DataFrame:
    """添加材料类型cat1,cat2,cat3"""
    columns = df.columns
    cats_list = ["cat1", "cat2", "cat3", "ce"]

    if (df["deltapn"] == "").all() and (df["mfgpn"] == "").all():
        return df
    sql = "select id,category_1 as cat1,category_2 as cat2,category_3 as cat3, \
    pur_keyword_pn as cond_pn,pur_keyword_des as cond_des,owner_ce as ce \
        from ssp_ce.a_mat_catalogue"
    cats = read_sql(sql)

    df = df.reindex(columns=df.columns.difference(cats_list).tolist() + cats_list)
    df = df.rename(
        columns={
            "cat1": "cat1_old",
            "cat2": "cat2_old",
            "cat3": "cat3_old",
            "ce": "ce_old",
        }
    )
    # df1 = df.reindex(columns=df.columns.difference(cats_list).tolist() + cats_list)
    df1 = df.reindex(columns=df.columns.difference(cats_list)).reset_index()

    sql = "select *exclude(index) from df1 left join \
        (select df1.index,b.cat1,b.cat2,b.cat3,b.ce from df1,LATERAL (\
            select cat1,cat2,cat3,ce from cats \
            where df1.des glob cond_des and df1.deltapn glob cond_pn \
                ORDER BY id limit 1)b)c on df1.index=c.index"

    df = dk.sql(sql).df()
    c1 = df["cat1_old"].notna()
    c2 = df["cat1"].isna()
    df["cat1"] = np.where(c1 & c2, df["cat1_old"], df["cat1"])

    c1 = df["cat2_old"].notna()
    c2 = df["cat2"].isna()
    df["cat2"] = np.where(c1 & c2, df["cat2_old"], df["cat2"])

    c1 = df["cat3_old"].notna()
    c2 = df["cat3"].isna()
    df["cat3"] = np.where(c1 & c2, df["cat3_old"], df["cat3"])

    c1 = df["ce_old"].notna()
    c2 = df["ce"].isna()
    df["ce"] = np.where(c1 & c2, df["ce_old"], df["ce"])
    df = df.reindex(columns=columns.difference(cats_list).tolist() + cats_list)
    # sql = "select \
    #     (select {'cat1_x':cat1,'cat2_x':cat2,'cat3_x':cat3,'ce_x':ce} from cats \
    #         where df.des glob cond_des and df.deltapn glob cond_pn \
    #             ORDER BY id limit 1) as cats \
    #                 from df"

    # df1 = dk.sql(sql).df()
    # df1 = df1["cats"].apply(pd.Series)
    # df = pd.concat([df, df1], axis=1)
    # df["cat1"] = np.where(df["cat1_x"].notna(), df["cat1_x"], df["cat1"])
    # df["cat2"] = np.where(df["cat2_x"].notna(), df["cat2_x"], df["cat2"])
    # df["cat3"] = np.where(df["cat3_x"].notna(), df["cat3_x"], df["cat3"])
    # df["ce"] = np.where(df["ce_x"].notna(), df["ce_x"], df["ce"])
    # df = df.drop(columns=["cat1_x", "cat2_x", "cat3_x", "ce_x"])
    return df


def dropdown_conditional():
    sql = "select  category_1 as cat1,category_2 as cat2,category_3 as cat3 \
        from ssp_ce.a_mat_catalogue"
    df = read_sql(sql)
    df = df.fillna("")
    dc = [
        {
            "if": {"column_id": "cat1"},
            "options": [{"label": i, "value": i} for i in df["cat1"].unique()],
        }
    ]

    for cat1 in df["cat1"].unique():
        opt = df.loc[df["cat1"] == cat1]["cat2"].unique()
        dci = {
            "if": {"column_id": "cat2", "filter_query": f'{"{cat1}"} eq "{cat1}"'},
            "options": [{"label": i, "value": i} for i in opt],
        }
        dc.append(dci)

    for cat2 in df["cat2"].unique():
        opt = df.loc[df["cat2"] == cat2]["cat3"].unique()
        dci = {
            "if": {"column_id": "cat3", "filter_query": f'{"{cat2}"} eq "{cat2}"'},
            "options": [{"label": i, "value": i} for i in opt],
        }
        dc.append(dci)
    return dc


def material_information_complete(
    df: pd.DataFrame, by: str = "deltapn"
) -> pd.DataFrame:
    """材料信息补全
    1. 从ssp_csg.mat_info中补全材料信息
    """
    columns = df.columns
    df = df.fillna("")

    df[by] = df[by].str.strip().str.upper()
    params = df[by].unique().tolist()
    params = [i for i in params if i]
    if not params:
        return df

    if by == "deltapn":
        sql = "select deltapn,des as des1,\
            mfgname as mfgname1,mfgpn as mfgpn1 \
                from ssp_csg.mat_info where deltapn in %s"

    elif by == "mfgpn":
        sql = "select mfgpn,des as des1,\
            mfgname as mfgname1,deltapn as deltapn1 \
                from ssp_csg.mat_info where mfgpn in %s"

    df1 = read_sql(sql, params=[params])
    df1.drop_duplicates(subset=by, inplace=True)
    df = df.merge(df1, how="left", on=by)

    df["des"] = np.where(df["des1"].notnull(), df["des1"], df["des"])
    df["mfgname"] = np.where(df["mfgname1"].notnull(), df["mfgname1"], df["mfgname"])
    if by == "deltapn":
        df["mfgpn"] = np.where(df["mfgpn1"].notnull(), df["mfgpn1"], df["mfgpn"])
    elif by == "mfgpn":
        df["deltapn"] = np.where(
            df["deltapn1"].notnull(), df["deltapn1"], df["deltapn"]
        )
    df = df.reindex(columns=columns)
    return df


def get_ce_owner(data: dict):
    """获取ce_owner"""
    cat_dict = {
        "category_1": data.get("cat1"),
        "category_2": data.get("cat2"),
        "category_3": data.get("cat3"),
    }
    cat_dict = {i: j for i, j in cat_dict.items() if j}
    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select owner_ce from ssp_ce.a_mat_catalogue \
                where category_1=%s and category_2=%s and category_3=%s"
            cu.execute(sql, list(cat_dict.values()))
            res = cu.fetchone()
            if res:
                return res.get("owner_ce")
            else:
                return "Ying.Gao"


def df_add_ce_owner(df: pd.DataFrame) -> pd.DataFrame:
    sql = "select category_1 as cat1,category_2 as cat2,category_3 as cat3,\
        owner_ce from ssp_ce.a_mat_catalogue"
    df1 = read_sql(sql)
    df1 = df1.drop_duplicates(["cat1", "cat2", "cat3"])

    df = df.reindex(columns=df.columns.difference(["owner_ce"]))
    df = df.merge(df1, on=["cat1", "cat2", "cat3"], how="left")
    df["owner_ce"] = df["owner_ce"].fillna("Ying.Gao")
    df = df.replace({"": None, np.nan: None})
    return df


def df_insert(table, df: pd.DataFrame):
    if "." in table:
        schema, table = table.split(".")
    else:
        schema = "ssp"
        table = table

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "select column_name from information_schema.columns \
                where TABLE_SCHEMA=%s and TABLE_NAME=%s"
            cu.execute(sql, [schema, table])
            res = cu.fetchall()

            columns = [
                i["column_name"].lower()
                for i in res
                if i["column_name"].lower() not in ("id", "gmt_create")
            ]
            df.columns = df.columns.str.lower()
            df = df.reindex(columns=columns)
            df = df.replace({np.nan: None})
            sql = f"insert into {schema}.{table}({','.join(df.columns)}) values({','.join(['%s'] * df.columns.size)})"
            params = df.values.tolist()
            cu.executemany(sql, params)
            conn.commit()


def df_insert_cu(table, df: pd.DataFrame, cu):
    if "." in table:
        schema, table = table.split(".")
    else:
        schema = "ssp"
        table = table

    sql = "select column_name from information_schema.columns \
        where TABLE_SCHEMA=%s and TABLE_NAME=%s"
    cu.execute(sql, [schema, table])
    res = cu.fetchall()

    columns = [
        i["column_name"].lower()
        for i in res
        if i["column_name"].lower() not in ("id", "gmt_create")
    ]
    df.columns = df.columns.str.lower()
    df = df.reindex(columns=columns)
    df = df.replace({np.nan: None})
    sql = f"insert into {schema}.{table}({','.join(df.columns)}) values({','.join(['%s'] * df.columns.size)})"
    params = df.values.tolist()
    cu.executemany(sql, params)


def df_update(table, df: pd.DataFrame):
    with pool.connection() as conn:
        with conn.cursor() as cu:
            col1 = df.columns.difference(["id"]).tolist()
            fields = ",".join(f"{i}=%s" for i in col1)
            sql = f"update {table} set {fields} where id = %s"
            df = df.reindex(columns=col1 + ["id"])
            df = df.replace({np.nan: None, "": None})
            params = df.values.tolist()
            cu.executemany(sql, params)
            conn.commit()


def parse_search(search):
    url = dict(parse_qsl(urlsplit(search).query))
    return url


def bom_check(prt_id: int, bom_id: int = None, source: str = "bom") -> pd.DataFrame:
    # TODO: 优化RD标注为SMD的正式料号加入SMD库
    if source == "bom":
        sql = "select * from ssp.bom_initial where bom_id=%s"
        params = [bom_id]
    else:
        # sql = "select * from ssp.bom_change where prt_id=%s \
        #     and change_type!=%s \
        #     and id IN (select MAX(id) \
        #     FROM bom_change GROUP BY prt_id,designno)"
        # params = [prt_id, "del"]
        sql = "select * from ssp.bom_change where bom_id=%s"
        params = [bom_id]
    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()
    if df.empty:
        return df

    df = df.fillna("")
    cols = df.columns

    df["pcb_remark"] = np.where(
        df["designno"] == "", df["pcb_remark"] + "位置号空白", df["pcb_remark"]
    )
    df["pcb_remark"] = np.where(
        df["packaging"] == "", df["pcb_remark"] + ",SMD/DIP需设定", df["pcb_remark"]
    )

    sql = "select deltapn from ssp_csg.mat_info where deltapn in %s"
    params = [df["deltapn"].unique().tolist()]
    mat = read_sql(sql, params=params)
    c1 = ~df["deltapn"].isin(mat["deltapn"])
    df["pcb_remark"] = np.where(
        c1, df["pcb_remark"] + ",CSG中无此料号", df["pcb_remark"]
    )

    df["pcb_remark"] = np.where(
        df["deltapn"] == "", df["pcb_remark"] + ",台达料号空白", df["pcb_remark"]
    )

    df1 = (
        df.query("source == '100'")
        .groupby(["designno", "source"], as_index=False)["designno"]
        .value_counts()
    )
    df2 = df.groupby("designno").source.apply(list).rename("source_list")

    df = df.merge(df1, on=["designno", "source"], how="left").merge(
        df2, on="designno", how="left"
    )

    c1 = df["designno"] != ""
    c2 = df["count"] > 1
    df["pcb_remark"] = np.where(
        c1 & c2, df["pcb_remark"] + ",位置号重复", df["pcb_remark"]
    )

    c3 = df["source_list"].apply(lambda x: x.count("100")) > 1
    df["pcb_remark"] = np.where(
        c1 & c3, df["pcb_remark"] + ",多个主source", df["pcb_remark"]
    )

    c4 = df["source_list"].apply(lambda x: x.count("100")) == 0
    df["pcb_remark"] = np.where(
        c1 & c4, df["pcb_remark"] + ",无主source", df["pcb_remark"]
    )

    if source == "bom":
        sql = "select distinct designno from ssp.bom_initial where prt_id=%s and bom_id!=%s"
        df1 = read_sql(sql, params=[prt_id, bom_id])
        if not df1.empty:
            c1 = df["designno"].isin(df1["designno"])
            df["pcb_remark"] = np.where(
                c1, df["pcb_remark"] + ",位置号重复", df["pcb_remark"]
            )

    c1 = df["cat1"] == ""
    df["pcb_remark"] = np.where(
        c1, df["pcb_remark"] + ",材料类别需设定", df["pcb_remark"]
    )
    df = df[df["pcb_remark"] != ""]
    df = df.reindex(columns=cols)
    df = df.sort_values(by=["designno", "source"])
    return df


def bom_union_shortage_change(prt_id: int, bom_id: int, source: str):
    if source == "bom":
        sql = "SELECT prt_id,bom_id,prtno,designno,deltapn,des,mfgname,mfgpn,checkcode,\
            packaging,cat1,cat2,cat3\
            from bom_initial WHERE prt_id=%s and source=%s"
        params = [prt_id, "100"]
    else:
        sql = "SELECT prt_id,bom_id,prtno,designno,deltapn,des,mfgname,mfgpn,checkcode,\
            packaging,cat1,cat2,cat3 from smbom WHERE prt_id=%s"
        params = [prt_id]

    df1 = read_sql(sql, params=params)
    df1["designno"] = df1["designno"].str.strip().str.upper()

    # todo =========如果bom change表增长降低性能，下面sql要优化=======
    sql = "SELECT prt_id,bom_id,prtno,change_type,designno,deltapn,des,mfgname,mfgpn,\
        checkcode,packaging,cat1,cat2,cat3 \
            from bom_change WHERE prt_id=%s AND id IN (select MAX(id) \
            FROM bom_change GROUP BY prt_id,designno)"
    params = [prt_id]
    df3 = read_sql(sql, params=params)
    df3["designno"] = df3["designno"].str.strip().str.upper()
    df3["change_type"] = df3["change_type"].str.strip().str.lower()

    c1 = df3["change_type"] == "change"
    c2 = df3["change_type"] == "add"
    df3_add_change = df3.loc[c1 | c2]
    df1 = df1.loc[~df1["designno"].isin(df3["designno"])]
    df = pd.concat([df1, df3_add_change])

    # *==========替代料部分==========
    sql = "SELECT id as shortage_id,designno,confirmed_ss,rd_remark\
        from bom_shortage WHERE prt_id=%s and bom_id=%s"
    params = [prt_id, bom_id]
    df2 = read_sql(sql, params=params)
    if df2.empty:
        df["rd_remark"] = ""
    else:
        df2["designno"] = df2["designno"].str.split(",")
        df2 = df2.explode("designno")
        df2 = df2.set_index("shortage_id")
        df2["rd_remark"].fillna("", inplace=True)
        df2 = df2.groupby("designno").agg(
            {
                "confirmed_ss": lambda x: x.dropna().sort_index().tail(1)
                if x.notna().any()
                else None,
                "rd_remark": lambda x: ",".join(set(x)),
            }
        )

        df = df.merge(df2, on="designno", how="left")

        df["deltapn"] = np.where(
            df["confirmed_ss"].notna(), df["confirmed_ss"], df["deltapn"]
        )

    df = df_add_checkcode(df)
    df = df_add_packaging(df)
    df = df_add_mat_category(df)
    df["rd_remark"] = df["rd_remark"].fillna("")
    df["qpa"] = 1

    if source == "bom":
        df = df.loc[df["bom_id"] == int(bom_id)]

    return df


def bom_shortage_list(prt_id: int, bom_id: int = None, source: str = "bom"):
    df = bom_union_shortage_change(prt_id, bom_id, source)

    sql = "select id as prt_id,smd_area,dip_area,qty,dept,dept_id from prt \
        where id=(select prt_id from bom_record where id=%s)"
    prt = read_sql(sql, params=[bom_id])

    smd_area = prt["smd_area"].iloc[0]
    dip_area = prt["dip_area"].iloc[0]
    qty = prt["qty"].iloc[0]
    dept = prt["dept"].iloc[0]
    dept_group = dept.split("_")[0]
    dept_id = prt["dept_id"].iloc[0]

    sql = "select checkcode,area,qty as stock_qty,deltapn as deltapn_stock from stock \
        where limituse=%s or limituse like %s"
    if dept_id == 17:
        params = ["ALL", f"%{dept}%"]
    else:
        params = ["ALL", f"%{dept_group}%"]
    stock = read_sql(sql, params=params)
    stock = stock.drop_duplicates(["checkcode", "area"])

    df["qpa"] = 1
    df = df.groupby("checkcode", as_index=False).agg(
        {
            "designno": lambda x: ",".join(x),
            "deltapn": "first",
            "des": "first",
            "mfgname": "first",
            "mfgpn": "first",
            "qpa": "sum",
            "packaging": "first",
            "prtno": "first",
            "prt_id": "first",
            "cat1": "first",
            "cat2": "first",
            "cat3": "first",
        }
    )
    df["area"] = np.where(df["packaging"] == "SMD", smd_area, dip_area)
    df = df.merge(stock, on=["checkcode", "area"], how="left")
    df["stock_qty"] = df["stock_qty"].fillna(0)
    df["qty"] = qty

    # params = df["deltapn"].unique().tolist()
    # 原BOM替代
    sql = "select designno as ss_group,checkcode,deltapn,packaging \
        from ssp.bom_initial where prt_id=%s"
    df0 = read_sql(sql, params=[prt_id])

    df0_set = (
        df0.groupby("checkcode", group_keys=False).ss_group.apply(frozenset).unique()
    )

    df0_set = [i for i in df0_set if len(i) > 1]

    df0["ss_group"] = df0["ss_group"].apply(
        lambda x: next((i for i in df0_set if x in i), x)
    )

    df0 = df0.drop_duplicates(["checkcode", "ss_group"])
    df0["area"] = np.where(df0["packaging"] == "SMD", smd_area, dip_area)

    df0_grp = df0.merge(stock, on=["area", "checkcode"], how="left")

    df0_grp["rank"] = df0_grp.groupby(["ss_group", "area"]).stock_qty.rank(
        method="first", ascending=False
    )

    df0_grp = df0_grp[df0_grp["rank"] == 1]

    df0_grp = df0_grp.rename(columns={"deltapn": "ss_bom", "stock_qty": "ss_bom_qty"})
    df0_grp = df0_grp[["ss_group", "area", "ss_bom", "ss_bom_qty"]]

    df = df.merge(df0[["checkcode", "ss_group"]], on="checkcode", how="left").merge(
        df0_grp, on=["ss_group", "area"], how="left"
    )

    # 8码替代
    df = df.reset_index()
    sql = "select * from first8_yards"
    first8 = read_sql(sql)

    sql = "select index from df where exists(select * from first8 \
            where df.deltapn glob first8.c_deltapn and df.des glob first8.c_des)"
    idx = dk.sql(sql).df()
    df["first8_yards"] = np.where(
        df["index"].isin(idx["index"]), df["deltapn"].str.slice(0, 8), None
    )

    c1 = df["first8_yards"].dropna().unique()
    first8_yards = stock.loc[stock["deltapn_stock"].str.slice(0, 8).isin(c1)]

    first8_yards["first8_yards"] = first8_yards["deltapn_stock"].str.slice(0, 8)
    first8_yards = first8_yards.rename(
        columns={"deltapn_stock": "ss_f8", "stock_qty": "ss_f8_qty"}
    )

    first8_yards["rank"] = first8_yards.groupby(
        ["area", "first8_yards"]
    ).ss_f8_qty.rank(method="first", ascending=False)

    first8_yards = first8_yards[first8_yards["rank"] == 1]
    first8_yards = first8_yards.reindex(
        columns=["area", "first8_yards", "ss_f8", "ss_f8_qty"]
    )

    df = df.merge(
        first8_yards,
        left_on=["first8_yards", "area"],
        right_on=["first8_yards", "area"],
        how="left",
    )

    params = df["deltapn"].unique().tolist()

    # a_oftenmaterial
    sql = "select distinct deltapn as checkcode,sub_group as common_group \
        from ssp_ce.a_oftenmaterial where sub_group in \
            (select sub_group from ssp_ce.a_oftenmaterial where deltapn in %s)"

    common = read_sql(sql, params=[params])

    common_group_set = (
        common.groupby("checkcode", group_keys=False)
        .common_group.apply(frozenset)
        .unique()
    )
    common_group_set = [i for i in common_group_set if len(i) > 1]

    common["common_group"] = common["common_group"].apply(
        lambda x: next((i for i in common_group_set if x in i), x)
    )
    common = common.drop_duplicates(["checkcode", "common_group"])

    common_grp = common.merge(stock, on="checkcode", how="left")

    common_grp["rank"] = common_grp.groupby(["common_group", "area"]).stock_qty.rank(
        method="first", ascending=False
    )

    common_grp = common_grp[common_grp["rank"] == 1]
    common_grp = common_grp.rename(
        columns={"checkcode": "ss_common", "stock_qty": "ss_common_qty"}
    )
    common_grp = common_grp[["common_group", "area", "ss_common", "ss_common_qty"]]

    df = df.merge(common, on="checkcode", how="left").merge(
        common_grp, on=["common_group", "area"], how="left"
    )
    df["common_rank"] = df.groupby("checkcode").ss_common_qty.rank(
        method="first", ascending=False, na_option="bottom"
    )
    df = df[df["common_rank"] == 1]

    # z_basicdata_koagp
    sql = (
        "select distinct deltapn as checkcode,koa_group from ssp_br.z_basicdata_koagp \
        where koa_group in (select koa_group from ssp_br.z_basicdata_koagp \
            where deltapn in %s and koa_group is not null)"
    )
    koa = read_sql(sql, params=[params])
    koa = koa.drop_duplicates(["checkcode", "koa_group"])
    koa_grp = koa.merge(stock, on="checkcode", how="left")
    koa_grp["rank"] = koa_grp.groupby(["koa_group", "area"]).stock_qty.rank(
        method="first", ascending=False
    )
    koa_grp = koa_grp[koa_grp["rank"] == 1]
    koa_grp = koa_grp.rename(columns={"checkcode": "ss_koa", "stock_qty": "ss_koa_qty"})
    koa_grp = koa_grp[["koa_group", "area", "ss_koa", "ss_koa_qty"]]

    df = df.merge(koa, on="checkcode", how="left").merge(
        koa_grp, on=["koa_group", "area"], how="left"
    )

    stock = get_stock(df["checkcode"].unique().tolist(), dept)
    df = bom_demand_qty(df, stock)
    df = bom_shortage_qty(df, prt_id, bom_id, source, dept)

    df["deltapn_ss"] = None
    df["deltapn_ss_qty"] = None
    c1 = df["short_qty"] > 0
    c2 = df["ss_f8_qty"] >= df["demand_qty"]
    c3 = df["ss_common_qty"] >= df["demand_qty"]
    c4 = df["ss_koa_qty"] >= df["demand_qty"]
    c5 = df["ss_bom_qty"] >= df["demand_qty"]

    df["deltapn_ss"] = np.where(c1 & c3, df["ss_common"], df["deltapn_ss"])
    df["deltapn_ss_qty"] = np.where(c1 & c3, df["ss_common_qty"], df["deltapn_ss_qty"])

    df["deltapn_ss"] = np.where(c1 & c2, df["ss_f8"], df["deltapn_ss"])
    df["deltapn_ss_qty"] = np.where(c1 & c2, df["ss_f8_qty"], df["deltapn_ss_qty"])

    df["deltapn_ss"] = np.where(c1 & c4, df["ss_koa"], df["deltapn_ss"])
    df["deltapn_ss_qty"] = np.where(c1 & c4, df["ss_koa_qty"], df["deltapn_ss_qty"])

    df["deltapn_ss"] = np.where(c1 & c5, df["ss_bom"], df["deltapn_ss"])
    df["deltapn_ss_qty"] = np.where(c1 & c5, df["ss_bom_qty"], df["deltapn_ss_qty"])
    df["bom_id"] = bom_id
    c6 = df["deltapn_ss"].isna()
    c7 = df["stock_qty"] > 0
    df["status"] = "ok"
    df["status"] = np.where(c1 & c6, "nostock", df["status"])
    df["status"] = np.where(c1 & c7, "lessstock", df["status"])

    params = df["deltapn_ss"].dropna().tolist()
    if params:
        sql = "select distinct deltapn as deltapn_ss,des as des_ss \
            from ssp_csg.mat_info where deltapn in %s"
        mat = read_sql(sql, params=[params])
        df = df.merge(mat, on="deltapn_ss", how="left")

    df = df.loc[df["designno"].notna()]
    df_insert("bom_shortage", df)

    return df


def bom_outbound_result(
    prt_id: int, bom_id: int, nt_name: str, source: str = "bom"
) -> pd.DataFrame:
    df = bom_union_shortage_change(prt_id, bom_id, source)
    df = df.groupby("checkcode", as_index=False).agg(
        {
            "designno": lambda x: ",".join(set(x)),
            "rd_remark": lambda x: ",".join(set(x)),
            "qpa": "sum",
            "packaging": "first",
            "deltapn": "first",
            "des": "first",
            "mfgname": "first",
            "mfgpn": "first",
            "cat1": "first",
            "cat2": "first",
            "cat3": "first",
        }
    )

    sql = "select id as prt_id,prtno,smd_area,dip_area,qty,dept,dept_id,proj,\
        ee,me,mat1_date,bom_owner,smstatus,fsdate_req from prt where id=%s"
    prt = read_sql(sql, params=[prt_id])
    mat1_date = prt["mat1_date"].iloc[0]
    dept = prt["dept"].iloc[0]
    smd_area = prt["smd_area"].iloc[0]
    dip_area = prt["dip_area"].iloc[0]
    qty = prt["qty"].iloc[0]
    fsdate_req = prt["fsdate_req"].iloc[0]
    prtno = prt["prtno"].iloc[0]
    dept_id = prt["dept_id"].iloc[0]

    df["area"] = np.where(df["packaging"] == "SMD", smd_area, dip_area)
    df["qty"] = qty

    stock = get_stock(df["checkcode"].unique().tolist(), dept)
    df = bom_demand_qty(df, stock)
    # TODO不能改成查全部库存，不然有查库阶段的缺料不一致,但是不查全部库存好像也有问题，待解决
    df = bom_shortage_qty(df, prt_id, bom_id, source, dept)

    df["prt_id"] = prt_id
    df["bom_id"] = bom_id
    df = df.merge(prt, on="prt_id", how="left")
    conn = pool.connection()
    cu = conn.cursor()
    now = datetime.now()

    # ! ===============采购数量大于0
    # item_pur = []
    start_date = now.replace(microsecond=0)
    df_pur1 = df.loc[df["short_qty"] > 0]
    if not df_pur1.empty:
        df_pur1["application"] = "project"
        df_pur1["qty"] = df_pur1["short_qty"]
        df_pur1["r_qty"] = df_pur1["short_qty"]
        df_pur1["start_date"] = start_date
        df_pur1["req_date"] = fsdate_req
        df_pur1["item_pur"] = int(f"{datetime.now():%y%m%d%H%M%S%f}")
        df_pur1["item_pur"] = df_pur1["item_pur"] + range(df_pur1.shape[0])
        df_pur1["remark"] = np.where(df_pur1["pur_id"].notna(), "追加数量", "")
        df_pur1["is_new_material"] = np.where(df_pur1["stock_id"].isna(), "Y", "N")
        # *--------机构材料rd换机构工程师me---------------
        df_pur1["rd"] = np.where(
            df_pur1["cat1"].str.contains("mechanical", case=False),
            df_pur1["me"],
            df_pur1["ee"],
        )
        df_pur1["mat_remark"] = df_pur1["rd_remark"] + "," + df_pur1["remark"]
        df_pur1["mat_remark"] = df_pur1["mat_remark"].str.replace(",", "", regex=False)
        df_pur1["mat_catelogue"] = df_pur1["cat1"]
        df_pur1["mat_group"] = df_pur1["cat2"]
        df_pur1["mat_type"] = df_pur1["cat3"]
        df_pur1["pur_status"] = "pending"
        df_pur1["dest_area"] = df_pur1["area"]
        df_insert_cu("ssp.pur", df_pur1, cu)
        # item_pur = df_pur1["item_pur"].tolist()

    # ! ================采购数量小于0
    df_pur2 = df.loc[df["short_qty"] < 0]
    if not df_pur2.empty:
        df_pur2["pur_id"] = df_pur2["pur_id"].str.split(",")
        df_pur2["pur_status"] = df_pur2["pur_status"].str.split(",")
        df_pur2["pur_qty"] = df_pur2["pur_qty"].str.split(",")
        df_pur2["start_date"] = df_pur2["start_date"].str.split(",")
        df_pur2["application"] = df_pur2["application"].str.split(",")
        df_pur2 = df_pur2.explode(
            ["pur_id", "pur_status", "pur_qty", "start_date", "application"]
        )
        c1 = ~df_pur2["pur_status"].str.lower().isin(["closed", "cancel"])
        c2 = df_pur2["application"].str.lower() == "project"
        df_pur2_1 = df_pur2.loc[c1 & c2]
        if not df_pur2_1.empty:
            df_pur2_1 = df_pur2_1.sort_values(by=["checkcode", "start_date"])
            df_pur2_1["pur_qty"] = df_pur2_1["pur_qty"].astype(int)
            df_pur2_1["cumsum_qty"] = df_pur2_1.groupby("checkcode").pur_qty.cumsum()
            df_pur2_1["new_pur_qty"] = df_pur2_1["cumsum_qty"] + df_pur2_1["short_qty"]
            df_pur2_1["new_pur_qty"] = np.where(
                df_pur2_1["new_pur_qty"] < 0, 0, df_pur2_1["new_pur_qty"]
            )
            df_pur2_1["pur_status"] = np.where(
                df_pur2_1["new_pur_qty"] == 0, "cancel", df_pur2_1["pur_status"]
            )

            df_pur2_1 = df_pur2_1.loc[df_pur2_1["pur_qty"] > df_pur2_1["new_pur_qty"]]
            df_pur2_1["mat_remark"] = (
                "需求数量"
                + df_pur2_1["pur_qty"].astype(int).astype(str)
                + ">"
                + df_pur2_1["new_pur_qty"].astype(int).astype(str)
                + df_pur2_1["rd_remark"]
            )
            df_pur2_1 = df_pur2_1.replace({np.nan: None})
            sql = "update ssp.pur set qty=%s,pur_status=%s,\
                mat_remark=concat_ws(',',mat_remark,%s) where id=%s"
            params = df_pur2_1[
                ["new_pur_qty", "pur_status", "mat_remark", "pur_id"]
            ].values.tolist()
            cu.executemany(sql, params)

    # !------主料仍需备料-----------
    if source == "bom":
        sql = "select checkcode,deltapn,des,mfgname,mfgpn,demand_qty,area,\
            cat1,cat2,cat3 from ssp.bom_shortage \
                where bom_id=%s and apply_main_source=%s"
        params = [bom_id, "Y"]
    else:
        sql = "select checkcode,deltapn,des,mfgname,mfgpn,demand_qty,area,\
            cat1,cat2,cat3 from ssp.bom_shortage \
                where prt_id=%s and apply_main_source=%s"
        params = [prt_id, "Y"]
    df_pur3 = read_sql(sql, params=params)
    if not df_pur3.empty:
        df_pur3["prt_id"] = prt_id
        df_pur3["bom_id"] = bom_id
        df_pur3 = df_pur3.merge(prt, on="prt_id", how="left")
        df_pur3 = bom_shortage_qty(df_pur3, prt_id, bom_id, source, dept)
        df_pur3 = df_pur3.loc[df_pur3["short_qty"] > 0]
        if not df_pur3.empty:
            df_pur3["pur_status"] = "pending"
            df_pur3["application"] = "debug"
            df_pur3["qty"] = df_pur3["short_qty"]
            df_pur3["r_qty"] = df_pur3["short_qty"]
            df_pur3["start_date"] = now
            df_pur3["req_date"] = fsdate_req
            df_pur3["item_pur"] = int(f"{datetime.now():%y%m%d%H%M%S%f}")
            df_pur3["item_pur"] = df_pur3["item_pur"] + range(df_pur3.shape[0])
            df_pur3["mat_remark"] = "主料仍需备料"
            df_pur3["mat_catelogue"] = df_pur3["cat1"]
            df_pur3["mat_group"] = df_pur3["cat2"]
            df_pur3["mat_type"] = df_pur3["cat3"]
            df_pur3["rd"] = np.where(
                df_pur3["cat1"].str.contains("mechanical", case=False),
                df_pur3["me"],
                df_pur3["ee"],
            )
            df_pur3["dest_area"] = df_pur3["area"]
            df_insert_cu("ssp.pur", df_pur3, cu)

    # # !-------磁件出库,磁库存id小于16384---------
    # c1 = df["stock_id"] < 16384
    # c2 = df["stockout_qty"] > 0
    # df_mag = df.loc[c1 & c2]
    # if not df_mag.empty:
    #     df_mag["mag_stock_list"] = df_mag["stock_id"]
    #     df_mag["owner"] = df_mag["prtno"]
    #     df_mag["qty"] = df_mag["stockout_qty"]
    #     df_mag["gmt_create"] = datetime.now()
    #     sql = "insert into ssp_ext.mag_stock_out(owner,qty,gmt_create,mag_stock_list) \
    #         values(%s,%s,%s,%s)"
    #     params = df_mag[
    #         ["owner", "qty", "gmt_create", "mag_stock_list"]
    #     ].values.tolist()
    #     cu.executemany(sql, params)

    # ------只返回出库的数据，不返回采购的数据---------
    c1 = df["stockout_qty"] != 0
    df_stockout = df.loc[c1]

    dfc = pd.DataFrame()
    if source == "change":
        c1 = df["closed_qty"] > 0
        c2 = df["stockout_qty"] < 0
        dfc = df.loc[c1 & c2]
        if not dfc.empty:
            df_stockout = df_stockout.loc[df_stockout.index.difference(dfc.index)]

    if (not df_stockout.empty) and (not mat1_date):
        df_stockout["deltapn"] = np.where(
            df_stockout["deltapn"].isna(),
            df_stockout["deltapn_stockout"],
            df_stockout["deltapn"],
        )
        df_stockout["area"] = np.where(
            df_stockout["area"].isna(),
            df_stockout["area_stockout"],
            df_stockout["area"],
        )
        df_stockout["type"] = "SM"
        df_stockout["owner1"] = nt_name
        df_stockout["qty"] = df_stockout["stockout_qty"]
        df_stockout["stockoutdate"] = now
        df_stockout["source"] = "bom"
        df_stockout["mag_stock_list"] = np.where(
            df_stockout["deltapn"].str.startswith("287"), df_stockout["stock_id"], None
        )

        df_insert_cu("ssp.stockout", df_stockout, cu)
        sql = "update ssp.stock set qty=qty-%s where id=%s"
        df_stock = df_stockout.loc[df_stockout["stock_id"].notna()]
        params = df_stock[["stockout_qty", "stock_id"]].values.tolist()
        cu.executemany(sql, params)

    # !-------------变更确认清单----------------
    l1 = []
    # *已经备料时,不管BOM还是change，都到变更确认
    if mat1_date:
        # * 与dfc重复,注释掉
        # c1 = df["closed_qty"] > 0
        # c2 = df["stockout_qty"] < 0
        # df11 = df.loc[c1 & c2]
        # if not df11.empty:
        #     df11["source"] = "pur"
        #     df11["qty"] = df11["stockout_qty"]
        #     l1.append(df11)

        c1 = ~df.index.isin(dfc.index)
        c2 = df["stockout_qty"] != 0
        df2 = df.loc[c1 & c2]
        if not df2.empty:
            df2["source"] = "stockout"
            df2["qty"] = df2["stockout_qty"]
            l1.append(df2)

    # *change时，已经采购close的，到变更确认
    if source == "change":
        if not dfc.empty:
            dfc["source"] = "pur"
            dfc["qty"] = dfc["stockout_qty"]
            l1.append(dfc)

        c1 = df["pur_status"].str.lower() == "closed"
        c2 = df["short_qty"] < 0
        df1 = df.loc[c1 & c2]
        if not df1.empty:
            df1["source"] = "pur"
            df1["qty"] = df1["short_qty"]
            l1.append(df1)

    if l1:
        df12 = pd.concat(l1)
        col1 = df12.columns.difference(["source", "qty"])
        col1 = {i: "first" for i in col1}
        df12 = df12.groupby("checkcode", as_index=False).agg(
            {"source": lambda x: ",".join(set(x)), "qty": "sum"} | col1
        )
        df12 = df12.loc[df12["qty"] != 0]
        # *--------累计之前出库确认数量---------------
        sql = "select area,checkcode,sum(qty) as  pre_qty\
            from ssp.bom_stockout where prt_id=%s group by area,checkcode"
        df_pre_qty = read_sql(sql, params=[prt_id])
        df12 = df12.merge(df_pre_qty, on=["area", "checkcode"], how="left")
        df12["pre_qty"] = df12["pre_qty"].fillna(0)
        df12["qty"] = df12["qty"] - df12["pre_qty"]
        df12 = df12.loc[df12["qty"] != 0]
        df12["bom_owner"] = nt_name
        df_insert_cu("ssp.bom_stockout", df12, cu)

    # !------SMBOM先删除，后插入-----------
    df_smbom = df.loc[df["designno"].notna()]
    df_smbom["designno"] = df_smbom["designno"].str.split(",")
    df_smbom = df_smbom.explode("designno")
    df_smbom["up_date"] = now

    if source == "bom":
        sql = "delete from ssp.smbom where bom_id=%s"
        params = [bom_id]
    else:
        sql = "delete from ssp.smbom where prt_id=%s"
        params = [prt_id]
    cu.execute(sql, params)
    df_insert_cu("ssp.smbom", df_smbom, cu)

    # !------更新 BOM record状态-----------
    sql = "update ssp.bom_record set status=%s,finish_date=%s where id=%s"
    params = ["closed", now, bom_id]
    cu.execute(sql, params)

    # !-------解锁后再次锁定-------------
    sql = "update ssp.prt set \
        prtbomreceive=if(prtbomreceive=%s,%s,prtbomreceive) \
        where id=%s"
    cu.execute(sql, ["X", "N", prt_id])

    sql = "select bomtype,receive_date from ssp.bom_record where id=%s"
    params = [bom_id]
    cu.execute(sql, params)
    bom_record = cu.fetchone()
    receive_date = bom_record.get("receive_date") or now
    bom_type = bom_record.get("bomtype")

    if source == "bom":
        bom_type = bom_type.split(",")
        bom_type = {bom_type_dict.get(i): "X" for i in bom_type if bom_type_dict.get(i)}
        field = ",".join(f"{i}='{j}'" for i, j in bom_type.items())
        sql = f"update ssp.prt set {field} where id=%s"
        cu.execute(sql, [prt_id])

    # !------规格添加工作记录-----------
    if bom_type == "change_qty":
        doc_type = "CHANGE_QTY"
    elif bom_type == "change":
        doc_type = "CHANGE"
    else:
        doc_type = "SMBOM"

    sql = "select * from ssp_spec.due_day where dept_id=%s and doc_type=%s"
    cu.execute(sql, [dept_id, doc_type])
    res = cu.fetchone()

    ecn_qty = res.get("ecn_qty")
    psl_qty = res.get("psl_qty")
    work_time_minute = res.get("work_time_minute")
    std_release_lt = res.get("due_day")

    holidays = get_holidays(datetime(now.year, 1, 1), datetime(now.year, 12, 31))
    dr = pd.date_range(receive_date, now, freq="min")
    dr = [i for i in dr if i.date() not in holidays]
    act_release_lt = round(len(dr) / (24 * 60), 2)

    if act_release_lt <= std_release_lt:
        release_ontime = "Y"
    else:
        release_ontime = None

    sql = "insert into ssp_spec.task\
        (status,dept,dept_id,owner,spec,doc_type,model,input_date,release_date,\
        ecn_qty,psl_qty,work_time_minute,std_release_lt,act_release_lt,release_ontime) \
        values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    params = [
        "close",
        dept,
        dept_id,
        nt_name,
        nt_name,
        doc_type,
        prtno,
        receive_date,
        now,
        ecn_qty,
        psl_qty,
        work_time_minute,
        std_release_lt,
        act_release_lt,
        release_ontime,
    ]
    cu.execute(sql, params)
    conn.commit()
    conn.close()
    return df, start_date


def get_stock(checkcode: list, dept: str):
    sql = "select area,checkcode,id as stock_id,stockno,qty as stock_qty, \
        deltapn as stock_deltapn from ssp.stock_union_mag \
            where checkcode in %s and (limituse=%s or limituse like %s)"

    if dept in ["EVCS_SYSTEM"]:
        limituse = dept
    else:
        limituse = dept.split("_")[0]
    params = [checkcode, "ALL", f"%{limituse}%"]

    stock = read_sql(sql, params=params)
    stock = stock.drop_duplicates(["area", "checkcode"])
    return stock


def get_all_stock(checkcode: list):
    sql = "select area,checkcode,id as stock_id,stockno,qty as stock_qty, \
        deltapn as stock_deltapn from ssp.stock_union_mag \
            where checkcode in %s"
    params = [checkcode]
    stock = read_sql(sql, params=params)
    stock = stock.drop_duplicates(["area", "checkcode"])
    return stock


def bom_demand_qty(df: pd.DataFrame, stock: pd.DataFrame) -> pd.DataFrame:
    sql = "select c_stockno,c_deltapn,c_des,c_area,ratio,loss,ratio_gt500 \
        from ssp.z_gross_stockout order by sorter"
    df1 = read_sql(sql)

    df["deltapn"] = df["deltapn"].str.strip()
    df["des"] = df["des"].str.strip()
    df["area"] = df["area"].str.strip()
    df["checkcode"] = df["checkcode"].str.strip()
    df["des_packaging"] = df["des"] + " " + df["packaging"]

    # stock = get_stock(df["checkcode"].unique().tolist(), dept)
    df = df.reindex(columns=df.columns.difference(["stock_qty", "stock_id", "stockno"]))
    df = df.merge(stock, on=["area", "checkcode"], how="left")

    df["stockno"] = df["stockno"].fillna("")
    df["net_qty"] = df["qpa"] * df["qty"]

    c = []
    for _, y in df1.iterrows():
        c1 = df["deltapn"].str.contains(y["c_deltapn"], na=False)
        c2 = df["des_packaging"].str.contains(y["c_des"], na=False)
        c3 = df["area"].str.contains(y["c_area"], na=False)
        c4 = df["stockno"].str.contains(y["c_stockno"], na=False)
        dfx = df.query("@c1&@c2&@c3&@c4")

        if not dfx.empty:
            dfx["demand_qty"] = dfx["net_qty"].apply(
                lambda z: min(z, 500) * y["ratio"]
                + max((z - 500), 0) * y["ratio_gt500"]
                + y["loss"]
            )
            c.append(dfx)
            df = df.loc[~df.index.isin(dfx.index)]
    dfc = pd.concat(c)
    dfc["demand_qty"] = dfc.groupby("checkcode").demand_qty.cumsum()
    # ! ===float 转 int 数量少了1
    dfc["demand_qty"] = dfc["demand_qty"] * np.sign(dfc["qty"])
    dfc["demand_qty"] = dfc["demand_qty"].astype(int)
    diff_cols = ["stock_qty", "stock_id", "stockno", "net_qty", "qty"]
    dfc = dfc.reindex(columns=dfc.columns.difference(diff_cols))
    return dfc


def bom_shortage_qty(
    df: pd.DataFrame,
    prt_id: int,
    bom_id: int,
    source: str,
    dept: str,
    all_stock: bool = False,
) -> pd.DataFrame:
    if source == "bom":
        sql = "select checkcode,deltapn as deltapn_pur,\
            dest_area as area_pur,\
            sum(qty) as old_short_qty,\
            SUM(CASE WHEN pur_status in ('received','closed') THEN qty ELSE 0 END) as closed_qty,\
            group_concat(id) as pur_id,\
            group_concat(qty) as pur_qty,\
            group_concat(pur_status) as pur_status,\
            group_concat(application) as application, \
            group_concat(start_date) as start_date \
            from pur where bom_id=%s and application=%s group by checkcode"
        pur = read_sql(sql, params=[bom_id, "project"])

        sql = "select checkcode,deltapn as deltapn_stockout,\
            area as area_stockout,sum(qty) as old_stockout_qty,\
            group_concat(id) as stockout_id \
            from stockout where bom_id=%s and source=%s \
                group by area,checkcode"
        stockout = read_sql(sql, params=[bom_id, "bom"])
    else:
        sql = "select checkcode,deltapn as deltapn_pur,\
            dest_area as area_pur,\
            sum(qty) as old_short_qty,\
            SUM(CASE WHEN pur_status in ('received','closed') THEN qty ELSE 0 END) as closed_qty,\
            group_concat(id) as pur_id,\
            group_concat(qty) as pur_qty,\
            group_concat(pur_status) as pur_status,\
            group_concat(application) as application, \
            group_concat(start_date) as start_date \
            from pur where prt_id=%s and application=%s group by checkcode"
        pur = read_sql(sql, params=[prt_id, "project"])

        sql = "select checkcode,deltapn as deltapn_stockout,\
            area as area_stockout,sum(qty) as old_stockout_qty,\
            group_concat(id) as stockout_id \
            from stockout where prt_id=%s and source=%s \
                group by area,checkcode"
        stockout = read_sql(sql, params=[prt_id, "bom"])

    pur["checkcode"] = pur["checkcode"].str.strip().str.upper()
    stockout["checkcode"] = stockout["checkcode"].str.strip().str.upper()

    df = df.merge(pur, on="checkcode", how="outer")
    df["area"] = np.where(df["area"].isna(), df["area_pur"], df["area"])
    df = df.merge(stockout, on="checkcode", how="outer")
    df["area"] = np.where(df["area"].isna(), df["area_stockout"], df["area"])

    checkcode = df["checkcode"].unique().tolist()
    if all_stock:
        stock = get_all_stock(checkcode)
    else:
        stock = get_stock(checkcode, dept)
    df = df.merge(stock, on=["area", "checkcode"], how="left")

    # *查是否安全库存
    # dept_group = dept.split("_")[0]
    sql = "select area,checkcode,id as safetystock_id \
        from safetystock where status=%s and checkcode in %s"
    ss = read_sql(sql, params=["processing", tuple(checkcode)])
    df = df.merge(ss, on=["area", "checkcode"], how="left")

    # df = (
    #     df.merge(pur, on="checkcode", how="outer")
    #     .merge(stockout, on="checkcode", how="outer")
    #     .merge(stock, on=["area", "checkcode"], how="left")
    # )
    c1 = df["pur_id"].notna()
    c2 = df["deltapn"].isna()
    df["deltapn"] = np.where(c1 & c2, df["deltapn_pur"], df["deltapn"])
    c1 = df["stockout_id"].notna()
    c2 = df["deltapn"].isna()
    df["deltapn"] = np.where(c1 & c2, df["deltapn_stockout"], df["deltapn"])

    df["demand_qty"] = df["demand_qty"].fillna(0)
    df["stock_qty"] = df["stock_qty"].fillna(0)
    df["old_short_qty"] = df["old_short_qty"].fillna(0)
    df["old_stockout_qty"] = df["old_stockout_qty"].fillna(0)
    df["closed_qty"] = df["closed_qty"].fillna(0)
    df["stockout_qty"] = df["demand_qty"] - df["old_stockout_qty"]

    # ! 反馈(项目增加套数，前次申请已close,材料有库存，系统还出缺料),将该注释代码移至下面
    # df["stockout_qty"] = df[["stockout_qty", "stock_qty"]].min(axis=1)

    # *-----已关闭的采购记录算已出库,这样stockout就不会再生成记录---------
    # *---------有两笔缺料时，一笔close,另外一笔还未close，添加了closed_qty---------
    # c1 = df["pur_status"].str.lower() == "closed"
    # c2 = df["old_short_qty"] >= df["stockout_qty"]
    # c3 = df["old_short_qty"] < df["stockout_qty"]
    # df["stockout_qty"] = np.where(c1 & c2, 0, df["stockout_qty"])
    # df["stockout_qty"] = np.where(
    #     c1 & c3, df["stockout_qty"] - df["old_short_qty"], df["stockout_qty"]
    # )
    df["stockout_qty"] = df["stockout_qty"] - df["closed_qty"]
    df["stockout_qty"] = df[["stockout_qty", "stock_qty"]].min(axis=1)

    df["short_qty"] = (
        df["demand_qty"]
        - df["old_stockout_qty"]
        - df["old_short_qty"]
        - df["stockout_qty"]
    )
    return df


# def df_to_excel(file: str, name: str, df: pd.DataFrame):
#     file = Path(file)
#     print(Path(file).name)
#     wb = openpyxl.load_workbook(file)
#     ws = wb.worksheets[0]
#     data = df.values.tolist()
#     for row in data:
#         ws.append(row)
#     wb.save(f"{name}_{file.name}")


# def duck_df(table: str):
#     try:
#         dk.sql("detach t")
#     except:
#         pass
#     dk.sql(f"attach '{duck2}' as t")
#     df = dk.sql(f"select * from t.{table}").df()
#     dk.sql("detach t")
#     return df


# @dc.memoize_stampede(cache, expire=24 * 3600, beta=0.5)
def df_add_packaging(df: pd.DataFrame):
    cols = ["packaging"]
    cols_diff = df.columns.difference(cols).tolist()
    df = df.reindex(columns=cols_diff + cols)

    sql = "select c_deltapn,c_des,r_type from smddip"
    df2 = read_sql(sql)

    dfc1 = dk.sql("select c_deltapn from df2 where c_deltapn!='*' and r_type='B'")
    dfc2 = dk.sql("select c_des from df2 where c_des!='*' and r_type='B'")
    dfc3 = dk.sql("select c_deltapn from df2 where r_type='A'")

    df = df.reset_index()

    sql = "select index from df where \
        exists(select * from dfc1 where df.deltapn=dfc1.c_deltapn)"
    idx1 = dk.sql(sql).df()

    sql = "select index from df where \
                exists(select * from dfc2 where df.des glob dfc2.c_des)"
    idx2 = dk.sql(sql).df()

    sql = "select index from df where \
                exists(select * from dfc3 where df.deltapn glob dfc3.c_deltapn)"
    idx3 = dk.sql(sql).df()

    c1 = df["index"].isin(idx1["index"])

    c2 = df["index"].isin(idx2["index"])
    c3 = df["index"].isin(idx3["index"])
    c4 = df["deltapn"].str.endswith("CE", na=False)  # * 临时料号不判为DIP
    c5 = df["deltapn"].str.startswith(("03", "15"))

    df["packaging"] = np.where(c1, "SMD", df["packaging"])
    df["packaging"] = np.where(c2, "SMD", df["packaging"])
    df["packaging"] = np.where(c3 & ~c1 & ~c2 & ~c4, "DIP", df["packaging"])
    df["packaging"] = np.where(c5, "SMD", df["packaging"])
    return df


def df_add_checkcode(df: pd.DataFrame):
    cols = ["deltapn", "des", "mfgname", "mfgpn", "checkcode", "pcb_remark"]
    cols_diff = df.columns.difference(cols).tolist()
    df = df.reindex(columns=cols_diff + cols)
    df[cols] = df[cols].fillna("")
    df["deltapn"] = df["deltapn"].str.strip().str.upper()
    df["mfgpn"] = df["mfgpn"].str.strip().str.upper()
    df["pcb_remark"] = ""

    sql = "select temp_pn as deltapn,deltapn as deltapn_std,des,mfgname,mfgpn \
        from ssp.ce_temp_pn where temp_pn in %s"
    params = df["deltapn"].dropna().unique().tolist()
    dfce = read_sql(sql, params=[params])
    if not dfce.empty:
        dfce["checkcode"] = np.where(
            dfce["deltapn_std"].notna(), dfce["deltapn_std"], dfce["deltapn"]
        )
        df = df.merge(dfce[["deltapn", "deltapn_std"]], on="deltapn", how="left")
        c1 = df["deltapn_std"].notna()
        df["deltapn"] = np.where(c1, df["deltapn_std"], df["deltapn"])
        df["pcb_remark"] = np.where(c1, "已有正式料号", df["pcb_remark"])

    dfce = dfce.reindex(columns=["deltapn", "des", "mfgname", "mfgpn", "checkcode"])
    sql = "select deltapn, des, mfgname, mfgpn,checkcode \
        from ssp_csg.mat_info where deltapn in %s"

    params = [df["deltapn"].dropna().unique().tolist()]
    mat = read_sql(sql, params=params)
    mat = pd.concat([mat, dfce])
    mat = mat.drop_duplicates("deltapn").add_suffix("_x")
    mat = mat.loc[mat["deltapn_x"] != ""]
    df = df.merge(mat, left_on="deltapn", right_on="deltapn_x", how="left")
    df["deltapn"] = np.where(df["deltapn_x"].notna(), df["deltapn_x"], df["deltapn"])
    df["des"] = np.where(df["des_x"].notna(), df["des_x"], df["des"])
    df["mfgname"] = np.where(df["mfgname_x"].notna(), df["mfgname_x"], df["mfgname"])
    df["mfgpn"] = np.where(df["mfgpn_x"].notna(), df["mfgpn_x"], df["mfgpn"])
    df["checkcode"] = np.where(
        df["checkcode_x"].notna(), df["checkcode_x"], df["deltapn"]
    )

    # 缺deltapn,按照mfgpn查找
    c1 = df["deltapn"] == ""
    c2 = df["mfgname"] != ""
    c3 = df["mfgpn"] != ""

    df2 = df.loc[c1 & c2 & c3]
    if not df2.empty:
        sql = "select deltapn, des, mfgname, mfgpn,checkcode \
            from ssp_csg.mat_info where mfgpn in %s"

        params = [df2["mfgpn"].dropna().unique().tolist()]
        mat = read_sql(sql, params=params)
        if not mat.empty:
            mat = mat.drop_duplicates(["mfgname", "mfgpn"]).add_suffix("_y")
            mat = mat.loc[mat["mfgpn_y"] != ""]

            df = df.merge(
                mat,
                left_on=["mfgname", "mfgpn"],
                right_on=["mfgname_y", "mfgpn_y"],
                how="left",
            )
            c1 = df["deltapn_y"].notna()
            c2 = df.index.isin(df2.index)
            df["deltapn"] = np.where(c1 & c2, df["deltapn_y"], df["deltapn"])
            df["des"] = np.where(c1 & c2, df["des_y"], df["des"])
            df["mfgname"] = np.where(c1 & c2, df["mfgname_y"], df["mfgname"])
            df["mfgpn"] = np.where(c1 & c2, df["mfgpn_y"], df["mfgpn"])
            df["checkcode"] = np.where(c1 & c2, df["checkcode_y"], df["checkcode"])
            df["pcb_remark"] = np.where(c1 & c2, "已有正式料号", df["pcb_remark"])

            c1 = df["mfgpn"] != ""
            c2 = df["deltapn"] == ""
            df3 = df.loc[c1 & c2]
            if not df3.empty:
                sql = "select mfgname, mfgpn,temp_pn,des from ssp.ce_temp_pn where deltapn is null"
                where = [
                    f"(mfgname='{i.mfgname}' and mfgpn='{i.mfgpn}')"
                    for i in df2.drop_duplicates(["mfgname", "mfgpn"]).itertuples()
                ]
                where = " or ".join(where)
                sql = sql + " and " + f"({where})"
                temp = read_sql(sql)
                temp = temp.drop_duplicates(["mfgname", "mfgpn"]).add_suffix("_z")

                if not temp.empty:
                    df = df.merge(
                        temp,
                        left_on=["mfgname", "mfgpn"],
                        right_on=["mfgname_z", "mfgpn_z"],
                        how="left",
                    )
                    c1 = df["temp_pn_z"].notna()
                    c2 = df.index.isin(df2.index)
                    df["deltapn"] = np.where(c1 & c2, df["temp_pn_z"], df["deltapn"])
                    df["checkcode"] = np.where(
                        c1 & c2, df["temp_pn_z"], df["checkcode"]
                    )
                    df["des"] = np.where(c1 & c2, df["des_z"], df["des"])
                    df["pcb_remark"] = np.where(
                        c1 & c2, "已有临时料号", df["pcb_remark"]
                    )

    df = df.reindex(columns=cols_diff + cols)
    return df


def df_add_mat_category(df: pd.DataFrame) -> pd.DataFrame:
    """添加材料类型cat1,cat2,cat3"""
    cols = ["cat1", "cat2", "cat3"]
    cols_diff = df.columns.difference(cols).tolist()

    sql = "select id,category_1 as cat1,pur_category2 as cat2,pur_category_3 as cat3, \
    pur_keyword_pn as cond_pn,pur_keyword_des as cond_des from ssp_ce.a_mat_catalogue"
    cats = read_sql(sql)

    df = df.reindex(columns=cols_diff + cols)

    df = df.rename(
        columns={
            "cat1": "cat1_old",
            "cat2": "cat2_old",
            "cat3": "cat3_old",
        }
    )
    df1 = df.reindex(columns=df.columns.difference(cols)).reset_index()
    sql = "select *exclude(index) from df1 left join \
        (select df1.index,b.cat1,b.cat2,b.cat3 from df1,LATERAL (\
            select cat1,cat2,cat3 from cats \
            where df1.des glob cond_des and df1.deltapn glob cond_pn \
                ORDER BY id limit 1)b)c on df1.index=c.index"

    df = dk.sql(sql).df()

    c1 = df["cat1_old"].notna()
    c2 = df["cat1"].isna()
    df["cat1"] = np.where(c1 & c2, df["cat1_old"], df["cat1"])

    c1 = df["cat2_old"].notna()
    c2 = df["cat2"].isna()
    df["cat2"] = np.where(c1 & c2, df["cat2_old"], df["cat2"])

    c1 = df["cat3_old"].notna()
    c2 = df["cat3"].isna()
    df["cat3"] = np.where(c1 & c2, df["cat3_old"], df["cat3"])
    df = df.reindex(columns=cols_diff + cols)
    return df


def db_insert(table: str, data: dict = None, cu=None):
    data.pop("id", None)
    field = ",".join(data.keys())
    ph = ",".join(["%s"] * len(data))
    sql = f"insert into {table}({field}) values({ph})"
    params = data.values()
    if cu is None:
        with pool.connection() as conn:
            with conn.cursor() as cu:
                cu.execute(sql, params)
                conn.commit()
    else:
        cu.execute(sql, params)


def get_temp_pn(dept_id: int, df: pd.DataFrame) -> pd.DataFrame:
    df = df.reindex(columns=df.columns.difference(["temp_pn"]))
    df["mfgname"] = df["mfgname"].str.strip().str.upper()
    df["mfgpn"] = df["mfgpn"].str.strip().str.upper()
    df["index"] = df.index
    if dept_id in (4, 5, 22):
        suffix = "MCE"
    else:
        suffix = "CE"

    with pool.connection() as conn:
        with conn.cursor() as cu:
            sql = "insert into ssp.ce_temp_pn(des, mfgname,mfgpn) \
                select %s, %s, %s from dual where not exists \
                    (select id from ssp.ce_temp_pn where mfgname=%s and mfgpn=%s)"
            cu.executemany(
                sql,
                df.reindex(columns=["des", "mfgname", "mfgpn", "mfgname", "mfgpn"])
                .replace({np.nan: None})
                .values.tolist(),
            )
            sql = f"update ssp.ce_temp_pn set temp_pn=concat('NEW',lpad(id,7,'0'),'{suffix}') \
                where temp_pn is null"
            cu.execute(sql)
            conn.commit()

    sql = "select mfgname, mfgpn, temp_pn from ssp.ce_temp_pn \
        where (mfgname,mfgpn) in %s"
    params = df[["mfgname", "mfgpn"]].values.tolist()
    df1 = read_sql(sql, params=[params])

    df = df.merge(df1, on=["mfgname", "mfgpn"], how="left")
    df = df.set_index("index", drop=True)
    return df


def pur_etd():
    sql = "select distinct id as prt_id,prtno,pcbstatus as pcb_date \
            from ssp.prt where smstatus in %s"
    params = [["Planning", "ProgOK", "PlanOK"]]
    prt = (
        read_db(sql, params=params)
        .with_columns(
            pl.col("pcb_date")
            .str.to_datetime(strict=False)
            .dt.date()
            .dt.offset_by("3d")
            .alias("pcb_date_3d")
        )
        .filter(pl.col("pcb_date_3d").is_not_null())
        .with_columns(pl.col("pcb_date").str.to_datetime(strict=False))
    )

    prt_id = prt["prt_id"].unique().to_list()
    sql = "select prt_id,es_date,pur_status,pur,deltapn,dept from ssp.pur \
        where prt_id in %s and pur!=%s and pur_status not in %s"
    params = [prt_id, "bo.sm.wang", ["cancel", "closed", "received"]]
    pur = (
        read_db(sql, params=params)
        .join(prt, on="prt_id", how="inner")
        .with_columns(
            es_gt_pcb=pl.col("es_date") > pl.col("pcb_date_3d"),
            pur=pl.col("pur").str.to_titlecase(),
        )
    )
    return pur
