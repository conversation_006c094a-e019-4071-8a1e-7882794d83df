# -*- coding: utf-8 -*-
import logging
import os
from pathlib import Path

import MySQLdb
import MySQLdb.cursors
import psycopg2.extras
import zen
from alchemical import Alchemical
from datafiles import auto
from dbutils.pooled_db import PooledDB
from dbutils.persistent_db import PersistentDB
from diskcache import Cache
from dotenv import load_dotenv

logging.basicConfig(
    level=logging.ERROR,  # 日志级别
    format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    datefmt="%Y-%m-%d %H:%M:%S",  # 时间格式
)

load_dotenv()
SSP_DIR = Path(os.getenv("SSP_DIR"))
UPLOAD_FOLDER_ROOT = SSP_DIR / "3. Document backup" / "upload"
CE_UPLOAD_FOLDER = SSP_DIR / "3. Document backup" / "ce"
BOM_DIR = SSP_DIR / "3. Document backup" / "BOM_Record"
ALCHEMICAL_DATABASE_URL = os.getenv("ALCHEMICAL_DATABASE_URL")
SECRET_KEY = os.getenv("SECRET_KEY")
cfg = auto("./utils/config/config.yml")


def to_dict(self):
    return {c.name.lower(): getattr(self, c.name, None) for c in self.__table__.columns}


def loader(key):
    with open("./utils/" + key, "r") as f:
        return f.read()


rule = zen.ZenEngine({"loader": loader})
db = Alchemical(ALCHEMICAL_DATABASE_URL, engine_options={"pool_pre_ping": True})
db.Model.to_dict = to_dict
engine = db.get_engine()
cache = Cache("~/cache")
pool = PooledDB(
    creator=MySQLdb,
    host=os.getenv("MYSQL_HOST"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE"),
    cursorclass=MySQLdb.cursors.DictCursor,
    charset="utf8mb4",
)
persist = PersistentDB(
    creator=MySQLdb,
    maxusage=1000,
    host=os.getenv("MYSQL_HOST"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE"),
    cursorclass=MySQLdb.cursors.DictCursor,
    charset="utf8mb4",
)
pg_conn = {
    "host": os.getenv("PG_HOST"),
    "port": os.getenv("PG_PORT"),
    "user": os.getenv("PG_USER"),
    "password": os.getenv("PG_PASSWORD"),
    "dbname": os.getenv("PG_DBNAME"),
    "cursor_factory": psycopg2.extras.RealDictCursor,
}

styles = {
    "root": {
        "display": "flex",
        "flexDirection": "row",
        "alignItems": "center",
    },
    "label": {
        "width": 100,
        "fontSize": 13,
    },
    "input": {
        "width": 180,
    },
}

IMG_TYPE = (
    "tiff",
    "bmp",
    "gif",
    "png",
    "jpeg",
    "jpg",
    "webp",
    "ico",
    "tif",
    "TIFF",
    "BMP",
    "GIF",
    "PNG",
    "JPEG",
    "JPG",
    "WEBP",
    "ICO",
    "TIF",
)
D3_DATE_FMT = "params.value?d3.timeFormat('%m/%d')(params.value):''"
