# -*- coding: utf-8 -*-
from urllib.parse import parse_qsl, urlsplit

import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash, dcc

from common import parse_search
from components.notice import notice
from config import UPLOAD_FOLDER_ROOT
from datetime import datetime

# from dbtool import db
from utils import db

from . import layout

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/pn/sw/ce", title="IC+软体组合件申请-CE")


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State(id("table"), "data"),
    State("url", "search"),
)
def ce_submit(n_clicks, data, search):
    if not n_clicks:
        raise PreventUpdate

    for item in data:
        db.update("ce.pn_sw", item)

    url = dict(parse_qsl(urlsplit(search).query))
    task_id = url.get("task")

    if all(item["new_deltapn"] for item in data):
        db.update(
            "ce.task", {"status": "close", "id": task_id, "end_date": datetime.now()}
        )
    else:
        db.update("ce.task", {"status": "ongoing", "id": task_id})

    return notice("提交成功"), True


@callback(
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State("url", "search"),
)
def download_data(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    task_id = url.get("task")

    task = db.execute("select * from ce.task where id = %s", (task_id,))
    df0 = pd.DataFrame(task)
    df0 = df0.reindex(columns=["type", "dept", "applicant", "status"])

    data = db.execute("select * from ce.pn_sw where task_id = %s", (task_id,))
    df = pd.DataFrame(data)
    df = pd.concat([df0, df], axis=1)
    df = df.fillna("")
    for col in [
        "application_attachment",
        "controlling_attachment",
    ]:
        df[col] = np.where(
            df[col] == "",
            df[col],
            UPLOAD_FOLDER_ROOT.as_uri() + "/" + df[col],
        )
    df = df.reindex(
        columns=df.columns.difference(
            ["id", "task_id", "create_time", "update_time"], sort=False
        )
    )
    col1 = db.execute("SHOW FULL COLUMNS FROM ce.task")
    col1 = {i.get("field"): i.get("comment") for i in col1}
    col2 = db.execute("SHOW FULL COLUMNS FROM ce.pn_sw")
    col2 = {i.get("field"): i.get("comment") for i in col2}
    col1.update(col2)
    df = df.rename(columns=col1)
    return dcc.send_data_frame(df.T.to_excel, f"PN_NEW_{task_id}.xlsx")
