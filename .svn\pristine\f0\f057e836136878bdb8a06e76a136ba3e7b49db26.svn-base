# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
from dash_extensions.enrich import dcc, html
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator
from common import id_factory, get_nt_name
from components.file_browser import file_browser
from config import SSP_DIR, cfg
import feffery_antd_components as fac
import dash_mantine_components as dmc

id = id_factory(__name__)

ns = Namespace("myNamespace", "tabulator")


tab1_submit_btn = dbc.<PERSON><PERSON>(
    "提交",
    color="success",
    size="sm",
    style={"width": "70px"},
    id=id("tab1_submit_btn"),
)
tab2_submit_btn = dbc.<PERSON><PERSON>(
    "提交",
    color="success",
    size="sm",
    style={"width": "70px"},
    id=id("tab2_submit_btn"),
)
# tab2_dowload_btn = dbc.<PERSON><PERSON>(
#     "下载", color="danger", size="sm", style={"width": "70px"}, id=id("tab2_dowload_btn")
# )


add_row = dbc.But<PERSON>(
    className="fa fa-plus",
    size="sm",
    color="light",
    id=id("add_row"),
)

search_dropdown = dcc.Dropdown(
    id=id("search_dropdown"),
    placeholder="设备序列号",
    multi=True,
    style={
        "width": "200px",
    },
    options=[
        {"label": "X0001", "value": "X0001"},
        {"label": "X0002", "value": "X0002"},
        {"label": "X0003", "value": "X0003"},
        {"label": "X0004", "value": "X0004"},
    ],
)
tab1_table = DashTabulator(
    id=id("tab1_table"),
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "390px",
        "selectable": True,
    },
    columns=[
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
        },
        {
            "title": "ACTION",
            "field": "action",
            "editor": "select",
            "editorParams": ns("actionValue"),
        },
        {
            "title": "序列号",
            "field": "equipment_pn",
            "headerFilter": "input",
        },
        {
            "title": "功率",
            "field": "power",
            "headerFilter": "input",
        },
        {
            "title": "电压",
            "field": "voltage",
            "headerFilter": "input",
        },
        {
            "title": "是否共用",
            "field": "public",
            "headerFilter": "input",
        },
        {
            "title": "项目描述",
            "field": "description",
            "headerFilter": "input",
        },
        {
            "title": "可用于项目号",
            "field": "prtno",
            "headerFilter": "input",
        },
        {
            "title": "当前状态",
            "field": "state",
            "headerFilter": "input",
        },
        {
            "title": "保管人",
            "field": "owner",
            "headerFilter": "input",
        },
        {
            "title": "存放楼层",
            "field": "storage_floor",
            "headerFilter": "input",
        },
        {
            "title": "设备备注",
            "field": "equipment_remarks",
            "headerFilter": "input",
        },
        {
            "title": "项目",
            "field": "project",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "EE",
            "field": "ee",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ME",
            "field": "me",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "PM",
            "field": "pm",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "申请使用日期",
            "field": "application_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "使用楼层",
            "editor": "select",
            "editorParams": ["三号楼10F", "三号楼1F", "四号楼6F", "其他"],
            "field": "use_floor",
            "headerFilter": "input",
        },
        {
            "title": "预计结束日期",
            "editor": "input",
            "field": "expected_end_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "预计返回日期",
            "editor": "input",
            "field": "expected_return_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "寄送目的地",
            "editor": "input",
            "field": "destination",
            "headerFilter": "input",
        },
        {
            "title": "申请备注",
            "editor": "input",
            "field": "application_remarks",
            "headerFilter": "input",
        },
        {
            "title": "赠予原因",
            "editor": "input",
            "field": "reason_giving",
            "headerFilter": "input",
        },
        {
            "title": "申请延期归还时间",
            "editor": "input",
            "field": "apply_return_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "延期原因",
            "editor": "input",
            "field": "reason_delay",
            "headerFilter": "input",
        },
    ],
    data=[],
)

tab2_table = DashTabulator(
    id=id("tab2_table"),
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "390px",
        "selectable": True,
    },
    downloadButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Export",
        "type": "xlsx",
    },
    clearFilterButtonType={
        "css": "btn btn-sm btn-outline-dark",
        "text": "Clear Filters",
    },
    columns=[
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
        },
        {
            "title": "ACTION",
            "field": "action",
            "editor": "select",
            "editorParams": ns("actionValue2"),
        },
        {
            "title": "申请事由",
            "field": "purpose",
        },
        {
            "title": "序列号",
            "editor": "input",
            "field": "equipment_pn",
            "headerFilter": "input",
        },
        {
            "title": "功率",
            "editor": "input",
            "field": "power",
            "headerFilter": "input",
        },
        {
            "title": "电压",
            "editor": "input",
            "field": "voltage",
            "headerFilter": "input",
        },
        {
            "title": "是否共用",
            "editor": "input",
            "field": "public",
            "headerFilter": "input",
        },
        {
            "title": "项目描述",
            "editor": "input",
            "field": "description",
            "headerFilter": "input",
        },
        {
            "title": "可用于项目号",
            "editor": "input",
            "field": "prtno",
            "headerFilter": "input",
        },
        {
            "title": "当前状态",
            "editor": "input",
            "field": "state",
            "headerFilter": "input",
        },
        {
            "title": "保管人",
            "editor": "input",
            "field": "owner",
            "headerFilter": "input",
        },
        {
            "title": "存放楼层",
            "editor": "input",
            "field": "storage_floor",
            "headerFilter": "input",
        },
        {
            "title": "设备备注",
            "editor": "input",
            "field": "equipment_remarks",
            "headerFilter": "input",
        },
        {
            "title": "项目",
            "field": "project",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "EE",
            "field": "ee",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "ME",
            "field": "me",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "PM",
            "field": "pm",
            "editor": "input",
            "headerFilter": "input",
        },
        {
            "title": "申请使用日期",
            "field": "application_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "使用楼层",
            "editor": "select",
            "editorParams": ["三号楼10F", "三号楼1F", "四号楼6F", "其他"],
            "field": "use_floor",
            "headerFilter": "input",
        },
        {
            "title": "预计结束日期",
            "field": "expected_end_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "预计返回日期",
            "field": "expected_return_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "寄送目的地",
            "editor": "input",
            "field": "destination",
            "headerFilter": "input",
        },
        {
            "title": "申请备注",
            "editor": "input",
            "field": "application_remarks",
            "headerFilter": "input",
        },
        {
            "title": "赠予原因",
            "editor": "input",
            "field": "reason_giving",
            "headerFilter": "input",
        },
        {
            "title": "申请延期归还时间",
            "editor": "input",
            "field": "apply_return_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "延期原因",
            "editor": "input",
            "field": "reason_delay",
            "headerFilter": "input",
        },
    ],
    data=[],
)
tab3_table = DashTabulator(
    id=id("tab3_table"),
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "390px",
        "selectable": True,
    },
    columns=[
        {
            "title": "操作日期",
            # "editor": "input",
            "field": "gmt_create",
            "headerFilter": "input",
        },
        {
            "title": "申请事由",
            "field": "purpose",
            "headerFilter": "input",
        },
        {
            "title": "管理员作业",
            "field": "action",
            "headerFilter": "input",
        },
        {
            "title": "序列号",
            "field": "equipment_pn",
            "headerFilter": "input",
        },
        {
            "title": "功率",
            "field": "power",
            "headerFilter": "input",
        },
        {
            "title": "电压",
            "field": "voltage",
            "headerFilter": "input",
        },
        {
            "title": "当前状态",
            "field": "state",
            "headerFilter": "input",
        },
        {
            "title": "保管人",
            "field": "owner",
            "headerFilter": "input",
        },
        {
            "title": "存放楼层",
            "field": "storage_floor",
            "headerFilter": "input",
        },
        {
            "title": "设备备注",
            "field": "equipment_remarks",
            "headerFilter": "input",
        },
        {
            "title": "项目",
            "field": "project",
            "headerFilter": "input",
        },
        {
            "title": "EE",
            "field": "ee",
            "headerFilter": "input",
        },
        {
            "title": "ME",
            "field": "me",
            "headerFilter": "input",
        },
        {
            "title": "PM",
            "field": "pm",
            "headerFilter": "input",
        },
        {
            "title": "申请使用日期",
            "field": "application_date",
            "headerFilter": "input",
        },
        {
            "title": "使用楼层",
            "field": "use_floor",
            "headerFilter": "input",
        },
        {
            "title": "预计结束日期",
            "field": "expected_end_date",
            "headerFilter": "input",
        },
        {
            "title": "预计返回日期",
            "field": "expected_return_date",
            "headerFilter": "input",
        },
        {
            "title": "寄送目的地",
            "field": "destination",
            "headerFilter": "input",
        },
        {
            "title": "申请备注",
            "field": "application_remarks",
            "headerFilter": "input",
        },
        {
            "title": "赠予原因",
            "field": "reason_giving",
            "headerFilter": "input",
        },
        {
            "title": "申请延期归还时间",
            "field": "apply_return_date",
            "headerFilter": "input",
        },
        {
            "title": "延期原因",
            "field": "reason_delay",
            "headerFilter": "input",
        },
    ],
    data=[],
)


tab1_content = html.Div([tab1_submit_btn, tab1_table])
tab2_content = dmc.Stack(
    [tab2_table, dmc.Group([add_row, tab2_submit_btn], position="apart")]
)
tab3_content = html.Div([search_dropdown, tab3_table])
tab4_content = file_browser(SSP_DIR / "program" / "DOC" / "dummyload", __name__)


def layout(**kwargs):
    nt_name = get_nt_name().lower()
    if nt_name in cfg.dummy_admin:
        disabled = False
    else:
        disabled = True
    layout = dbc.Container(
        [
            fac.AntdTabs(
                items=[
                    {
                        "key": "1",
                        "label": "EE&PM界面",
                        "children": tab1_content,
                        "disabled": disabled,
                    },
                    {
                        "key": "2",
                        "label": "管理员处理",
                        "children": tab2_content,
                        "disabled": disabled,
                    },
                    {
                        "key": "3",
                        "label": "管理员查询",
                        "children": tab3_content,
                        "disabled": disabled,
                    },
                    {
                        "key": "4",
                        "label": "文档",
                        "children": tab4_content,
                        "disabled": disabled,
                    },
                ],
                id=id("tabs"),
                activeKey="1",
            ),
            html.Div(id=id("notice")),
        ],
        fluid=True,
    )
    return layout
