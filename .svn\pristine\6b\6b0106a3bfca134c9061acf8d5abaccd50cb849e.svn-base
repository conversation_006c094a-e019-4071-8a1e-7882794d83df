# -*- coding: utf-8 -*-
import os
from datetime import date, datetime

from pony.orm import Database, Optional, Required

db = Database()
db.bind(
    provider="mysql",
    host=os.getenv("MYSQL_HOST"),
    user=os.getenv("MYSQL_USER"),
    passwd=os.getenv("MYSQL_PASSWORD"),
    port=int(os.getenv("MYSQL_TCP_PORT")),
    db="ssp_spec",
)


class Ecn_sm(db.Entity):
    _table_ = "ecn_sm"
    status = Optional(str)
    owner = Optional(str)
    model = Required(str)
    ee = Required(str)
    dept_id = Required(int)
    dept = Required(str)
    doctype = Required(str)
    ecnno = Required(str)
    qty = Optional(str)
    running_date = Optional(datetime)
    remark = Optional(str)
    input_date = Optional(datetime)
    release_date = Optional(datetime)
    followup_date = Optional(datetime)
    actlt = Optional(int)
    release_ontime = Optional(str)
    followup_lt = Optional(int)
    followup_ontime = Optional(str)
    urgent = Optional(str)


class Due_day(db.Entity):
    _table_ = "due_day"
    dept = Required(str)
    doc_type = Required(str)
    due_day = Required(int)
    dept_id = Required(int)


class Doctype(db.Entity):
    _table_ = "doctype"
    dept_id = Required(int)
    dept = Required(str)
    doc_type = Required(str)
    form_id = Required(str)
    action_process = Required(str)


class Task(db.Entity):
    _table_ = "task"
    status = Optional(str)  # 状态
    owner = Optional(str)
    spec = Optional(str)
    doc_type = Optional(str)  # 文件类型
    model = Required(str)  # 机种名称
    ee = Required(str)
    me = Optional(str)
    customer = Optional(str)
    dept_id = Required(int)
    dept = Required(str)
    input_date = Optional(date)  # 创建日期
    submit_date = Optional(date)
    release_date = Optional(date)
    request_date = Optional(date)  # 需求日期
    std_submit_lt = Optional(int)
    act_submit_lt = Optional(int)
    std_release_lt = Optional(int)
    act_release_lt = Optional(int)
    submit_ontime = Optional(str)
    release_ontime = Optional(str)
    second_source = Optional(str)
    attachment = Optional(str)
    upload_address = Optional(str)
    other_address = Optional(str)
    action_process = Optional(str)
    remark = Optional(str)


class Modification(db.Entity):
    _table_ = "modification"
    task_id = Required(int)
    current = Optional(str)
    user = Required(str)
    modified_date = Required(datetime)
    status = Required(str)
    remark = Optional(str)
    attachment = Optional(str)
    model = Optional(str)
    ee = Required(str)
    me = Required(str)
    request_date = Required(date)
    spec = Optional(str)


class Apply_form(db.Entity):
    _table_ = "apply_form"
    col_name = Required(str)
    label = Required(str)
    type = Required(str)
    size = Required(str)
    order = Required(str)
    required = Optional(str)


class Duty(db.Entity):
    _table_ = "duty"
    dept_id = Required(int)
    owner = Required(str)
    dept = Required(str)


db.generate_mapping(create_tables=True)
