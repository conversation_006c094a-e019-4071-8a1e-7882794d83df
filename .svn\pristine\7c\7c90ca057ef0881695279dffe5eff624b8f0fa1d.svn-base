# coding: utf-8
from sqlalchemy import Column, DECIMAL, DateTime, Index, JSON, MetaData, String, TIMESTAMP, Table, text
from sqlalchemy.dialects.mysql import INTEGER

metadata = MetaData()


t_csg = Table(
    'csg', metadata,
    Column('ID', INTEGER(10), primary_key=True, nullable=False),
    Column('CltDel', String(255)),
    Column('Packing', String(20)),
    <PERSON>umn('DeltaPN', String(30), primary_key=True, nullable=False),
    <PERSON>umn('UN', String(10)),
    <PERSON>umn('LF', String(10)),
    <PERSON>umn('HF', String(10)),
    <PERSON>umn('MS', String(6)),
    <PERSON>umn('WS', String(6)),
    <PERSON>umn('CB', String(8)),
    Column('DES', String(100)),
    Column('TYPE', String(12)),
    Column('GROUP', String(20)),
    Column('PRODUCT', String(16)),
    Column('DMS', String(8)),
    Column('STD_MATERIAL', String(20)),
    Column('MFGCODE', String(20)),
    Column('MFGNAME', String(30)),
    Column('MFGPN', String(60), index=True),
    Column('ESD', String(28)),
    Column('Plant_Block_Status', String(45)),
    Column('Block_plants', String(45)),
    Column('T_Headcode', String(45)),
    Column('T_code', String(45)),
    Column('T_PCAD_ALLOther', INTEGER(11), server_default=text("'1'"), comment='0代表Delete不可选\\n1代表可用'),
    Column('T_PCAD_ADP', INTEGER(11), server_default=text("'1'")),
    Column('T_PCAD_AMP', INTEGER(11), server_default=text("'1'")),
    Column('T_PCAD_DCBU', INTEGER(11), server_default=text("'1'")),
    Column('T_PCAD_NBE', INTEGER(11), server_default=text("'1'")),
    Column('T_PCAD_ATI', INTEGER(11), server_default=text("'1'")),
    Column('T_PCAD_APE', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_ALLOther', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_ADP', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_AMP', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_DCBU', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_NBE', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_ATI', INTEGER(11), server_default=text("'1'")),
    Column('T_Normal_APE', INTEGER(11), server_default=text("'1'")),
    Column('T_MemoDelete', String(255)),
    Column('T_ListPriority_STDUPS', DECIMAL(5, 1), server_default=text("'0.0'")),
    Column('T_ListPriority_STDLGT', DECIMAL(5, 1), server_default=text("'0.0'")),
    Column('T_CAPpriority', DECIMAL(5, 2), server_default=text("'0.00'")),
    Column('T_Memo2ANTIS', INTEGER(11), server_default=text("'0'"), comment='CS→2;\\nRegularAnti-S→10;\\nNonAnti-S→1'),
    Column('T_Memo2Double85', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_ThickRES', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_ThinRES', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_CSRES', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_DIPRES', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_OtherRES', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_DISCCAP', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_eCAP', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_FilmCap', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_MlccCAP', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_TanCap', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_xCap', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_yCap', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_Crystal', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_DIO', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_IC', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_LED', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_Module', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_MosTrIgbt', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_NtcPtc', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_Photo', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_RelayFuse', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_Shunt', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2D85_VariGas', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2AutoM', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2Walsin', INTEGER(11), server_default=text("'1'"), comment='1为无问题,0代表非灌胶(有问题）'),
    Column('T_Memo2EOL', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2HF', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo2Surge', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo2GradeB', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3NRP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3MPBG', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo3ESD', INTEGER(11), server_default=text("'0'")),
    Column('T_Memo3MSL', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3SystemBlock', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3FilterBlockALLOther', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3ALLOtherCustomer', String(45)),
    Column('TL_Memo3FilterBlockDES', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3DESCustomer', String(45)),
    Column('TL_Memo3FilterBlockLGT', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3LGTCustomer', String(45)),
    Column('TL_Memo3FilterBlockSPA', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3SPACustomer', String(45)),
    Column('TL_Memo3FilterBlockRTP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3RTPCustomer', String(45)),
    Column('TL_Memo3FilterBlockDCBU', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3DCBUCustomer', String(45)),
    Column('TL_Memo3FilterBlockAMP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3AMPCustomer', String(45)),
    Column('TL_Memo3FilterBlockADP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3ADPCustomer', String(45)),
    Column('TL_Memo3FilterBlockNBE', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3NBECustomer', String(45)),
    Column('TL_Memo3FilterBlockIDC', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3IDCCustomer', String(45)),
    Column('TL_Memo3FilterBlockEVCS', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3EVCSCustomer', String(45)),
    Column('TL_Memo3FilterBlockSTD_LGT', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_LGTCustomer', String(45)),
    Column('TL_Memo3FilterBlockSTD_UPS', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_UPSCustomer', String(45)),
    Column('TL_Memo3FilterBlockSTD_EVC', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_EVCCustomer', String(45)),
    Column('TL_Memo3FilterBlockATI', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3ATICustomer', String(45)),
    Column('TL_Memo3FilterBlockAPE', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3APECustomer', String(45)),
    Column('TL_Filter_Count', INTEGER(11)),
    Column('TL_Memo3SystemBlock_DCBU', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3DCBU_plant', String(45)),
    Column('TL_Memo3SystemBlock_ADP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3ADP_plant', String(45)),
    Column('TL_Memo3SystemBlock_NBE', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3NBE_plant', String(45)),
    Column('TL_Memo3SystemBlock_AMP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3AMP_plant', String(45)),
    Column('TL_Memo3SystemBlock_EVCS', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3EVCS_plant', String(45)),
    Column('TL_Memo3SystemBlock_IDC', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3IDC_plant', String(45)),
    Column('TL_Memo3SystemBlock_DES', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3DES_plant', String(45)),
    Column('TL_Memo3SystemBlock_LGT', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3LGT_plant', String(45)),
    Column('TL_Memo3SystemBlock_STD_LGT', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_LGT_plant', String(45)),
    Column('TL_Memo3SystemBlock_STD_UPS', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_UPS_plant', String(45)),
    Column('TL_Memo3SystemBlock_STD_EVC', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3STD_EVC_plant', String(45)),
    Column('TL_Memo3SystemBlock_SPA', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3SPA_plant', String(45)),
    Column('TL_Memo3SystemBlock_RTP', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3RTP_plant', String(45)),
    Column('TL_Memo3SystemBlock_Allother', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3Allother_plant', String(45)),
    Column('TL_Memo3SystemBlock_ATI', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3ATI_plant', String(45)),
    Column('TL_Memo3SystemBlock_APE', INTEGER(11), server_default=text("'0'")),
    Column('TL_Memo3APE_plant', String(45)),
    Column('TL_SystemBlock_Count', INTEGER(11)),
    Index('DirectForPCAD', 'ID', 'DeltaPN', 'MFGNAME', 'MFGPN', 'T_code'),
    Index('Query_for_SMBOM', 'DeltaPN', 'DES', 'MFGNAME', 'MFGPN'),
    Index('mark', 'ID', 'HF', 'DeltaPN', 'MS', 'WS', 'ESD'),
    Index('PackingQuery', 'ID', 'DeltaPN', 'MFGPN', 'Packing')
)


t_csg_cr_group = Table(
    'csg_cr_group', metadata,
    Column('ID', INTEGER(11), primary_key=True),
    Column('DeltaPN', String(45)),
    Column('DES', String(255)),
    Column('MFGNAME', String(255)),
    Column('MFGPN', String(255)),
    Column('hFEmi', String(45)),
    Column('hFEma', String(45)),
    Column('MinVBrMI', String(45)),
    Column('MinVBrMA', String(45)),
    Column('CTRMI', String(45)),
    Column('CTRMA', String(45)),
    Column('Volmi', String(45)),
    Column('Volma', String(45)),
    Column('Curmi', String(45)),
    Column('Curma', String(45)),
    Column('Rdsonmi', String(45)),
    Column('Rdsonma', String(45)),
    Column('MaxTrr25MI', String(45)),
    Column('MaxTrr25MA', String(45)),
    Column('PeakPowerMi', String(45)),
    Column('PeakPowerMa', String(45)),
    Column('AccuracyMI', String(45)),
    Column('AccuracyMa', String(45)),
    Column('CRGroup', String(45)),
    Column('Dept', String(45))
)


t_del_csg_serial = Table(
    'del_csg_serial', metadata,
    Column('id', INTEGER(11), primary_key=True),
    Column('gmt_create', DateTime, nullable=False),
    Column('gmt_modified', DateTime, nullable=False),
    Column('deltapn', String(255), nullable=False),
    Column('des', String(255), nullable=False),
    Column('mfgname', String(255), nullable=False),
    Column('mfgpn', String(255), nullable=False),
    Column('checkcode', String(255), nullable=False, unique=True)
)


t_eol_list = Table(
    'eol_list', metadata,
    Column('id', INTEGER(10), primary_key=True, comment='自增id'),
    Column('deltapn', String(255), nullable=False, unique=True, server_default=text("''"), comment='台达料号'),
    Column('sub_group', String(255), nullable=False, server_default=text("''"), comment='eol分组'),
    Column('deltapn_2nd', String(255), nullable=False, server_default=text("''"), comment='台达料号（替代）')
)


t_eol_list_group = Table(
    'eol_list_group', metadata,
    Column('id', INTEGER(10), primary_key=True, comment='自增id'),
    Column('deltapn', String(255), nullable=False, server_default=text("''"), comment='台达料号'),
    Column('des', String(255), nullable=False, server_default=text("''")),
    Column('mfgname', String(255), nullable=False, server_default=text("''")),
    Column('mfgpn', String(255), nullable=False, server_default=text("''")),
    Column('grp', String(255), nullable=False, server_default=text("''"), comment='eol分组'),
    Column('source', String(255), nullable=False, server_default=text("''")),
    Column('checkcode', String(255), nullable=False, server_default=text("''")),
    Index('unique_deltapn_grp', 'deltapn', 'grp', unique=True)
)


t_mat_info = Table(
    'mat_info', metadata,
    Column('id', INTEGER(10), primary_key=True, comment='自增id'),
    Column('gmt_create', DateTime, nullable=False, comment='创建时间'),
    Column('gmt_modified', DateTime, nullable=False, comment='修改时间'),
    Column('deltapn', String(255), nullable=False, index=True, comment='台达料号'),
    Column('des', String(255), nullable=False, index=True, server_default=text("''"), comment='描述'),
    Column('mfgname', String(255), nullable=False, index=True, server_default=text("''"), comment='厂商'),
    Column('mfgpn', String(255), nullable=False, index=True, server_default=text("''"), comment='厂商料号'),
    Column('checkcode', String(255), nullable=False, comment='系列料号'),
    Column('bg_block', String(255), nullable=False, server_default=text("''")),
    Column('gradeb', String(255), nullable=False, server_default=text("''")),
    Column('eol', String(255), nullable=False, server_default=text("''")),
    Column('nrnd', String(255), nullable=False, server_default=text("''")),
    Column('ssp_block', String(255), nullable=False, server_default=text("''"), comment='csg T_Normal_ALLOther等于0'),
    Column('msl_block', String(255), nullable=False, server_default=text("''")),
    Column('esd_block', String(255), nullable=False, server_default=text("''")),
    Column('non_auto', String(255), nullable=False, server_default=text("''")),
    Column('autom', String(255), nullable=False, server_default=text("''")),
    Column('non_hf', JSON),
    Column('not_in_bgallpartlist', String(255), nullable=False, server_default=text("''")),
    Column('common_part', JSON),
    Column('bu_std_part', JSON),
    Column('quality_issue', JSON),
    Column('limit_use', JSON),
    Column('ce_alert', JSON),
    Column('system_block', JSON),
    Column('antis', String(255), nullable=False, server_default=text("''"), comment='抗硫'),
    Column('double85', String(255), nullable=False, server_default=text("''"), comment='双85'),
    Column('ws', String(255), nullable=False, server_default=text("''"), comment='AI'),
    Column('qty', INTEGER(11), nullable=False, server_default=text("'0'")),
    Column('ai', String(255), nullable=False, server_default=text("''"))
)


t_mat_status_table = Table(
    'mat_status_table', metadata,
    Column('id', INTEGER(10), primary_key=True, comment='自增id'),
    Column('dept_id', INTEGER(11), nullable=False, comment='部门ID'),
    Column('bg_block', String(255), nullable=False),
    Column('gradeb', String(255), nullable=False),
    Column('eol', String(255), nullable=False),
    Column('nrnd', String(255), nullable=False),
    Column('ssp_block', String(255), nullable=False, comment='csg T_Normal_ALLOther等于0'),
    Column('quality_issue', String(255), nullable=False, server_default=text("'2'")),
    Column('limit_use', String(255), nullable=False, server_default=text("'3'")),
    Column('ce_alert', String(255), nullable=False, server_default=text("'4'")),
    Column('system_block', String(255), nullable=False),
    Column('msl_block', String(255), nullable=False),
    Column('esd_block', String(255), nullable=False),
    Column('non_auto', String(255), nullable=False),
    Column('autom', String(255), nullable=False),
    Column('non_hf', String(255), nullable=False),
    Column('common_part', String(255), nullable=False),
    Column('bu_std_part', String(255), nullable=False),
    Column('not_in_bgallpartlist', String(255), nullable=False),
    Column('antis', String(255), nullable=False),
    Column('double85', String(255), nullable=False),
    Column('ws', String(255), nullable=False),
    Column('ai', String(255), nullable=False, server_default=text("'6'"))
)


t_systemblock = Table(
    'systemblock', metadata,
    Column('ID', INTEGER(11), primary_key=True),
    Column('Plant', String(255)),
    Column('DeltaPN', String(600)),
    Column('BlockStatus', String(255)),
    Index('DeltaPN_for_trigger', 'ID', 'DeltaPN'),
    Index('systemblockCheck', 'ID', 'Plant', 'DeltaPN')
)


t_toola_data = Table(
    'toola_data', metadata,
    Column('id', INTEGER(10), primary_key=True),
    Column('gmt_create', TIMESTAMP, server_default=text("CURRENT_TIMESTAMP")),
    Column('fce', String(255), nullable=False),
    Column('bom_type', String(255), comment='BOM类型'),
    Column('prtno', String(255)),
    Column('deltapn', String(255)),
    Column('des', String(255)),
    Column('mfgname', String(255)),
    Column('mfgpn', String(255)),
    Column('grp', String(255), comment='2nd source'),
    Column('design_no', String(255), comment='位置号'),
    Column('rd_remark', String(255)),
    Column('remark1', String(255)),
    Column('remark2', String(255)),
    Column('remark3', String(255)),
    Column('dip_or_smd', String(255)),
    Column('sh_stock', String(255)),
    Column('hz_stock', String(255)),
    Column('issue', String(255), comment='问题'),
    Column('rd_confirmed', String(255), comment='确认结果'),
    Index('unique_prtno_design_no_deltapn', 'prtno', 'design_no', 'deltapn', unique=True)
)
