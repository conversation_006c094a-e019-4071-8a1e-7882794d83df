# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import dash_table, html

from common import dropdown_conditional, get_nt_name, get_ssp_user, id_factory

id = id_factory(__name__)


tab_add_row = dbc.<PERSON><PERSON>(
    className="fa fa-plus",
    size="sm",
    color="light",
    id=id("add-row"),
)

columns = [
    {"id": "deltapn", "name": "台达料号", "presentation": "input"},
    {"id": "des", "name": "描述", "presentation": "input"},
    {"id": "mfgname", "name": "厂商", "presentation": "input"},
    {"id": "mfgpn", "name": "厂商型号", "presentation": "input"},
    {"id": "cat1", "name": "材料类型1", "presentation": "dropdown"},
    {"id": "cat2", "name": "材料类型2", "presentation": "dropdown"},
    {"id": "cat3", "name": "材料类型3", "presentation": "dropdown"},
]

table = dash_table.DataTable(
    data=[{}],
    columns=columns,
    editable=True,
    row_deletable=True,
    is_focused=True,
    id=id("table"),
    style_cell={
        "whiteSpace": "normal",
        "height": "auto",
        "textAlign": "left",
        "font-family": "Helvetica",
        "font-size": "10px",
    },
    css=[{"selector": ".dash-spreadsheet-menu-item", "rule": "display:none"}],
    dropdown_conditional=dropdown_conditional(),
)

attachment = fac.AntdDraggerUpload(
    apiUrl="/upload/",
    text="上传客户需求文件附件",
    id=id("attachment"),
    lastUploadTaskRecord={},
)

submit = dmc.Button("提交", id=id("rd-submit"))


def layout(**kwargs):
    nt_name = get_nt_name()
    users = get_ssp_user()
    user = users.loc[users["nt_name"] == nt_name]
    role_group = user["role_group"].iloc[0]
    if role_group == "CE":
        applicant_disabled = False
    else:
        applicant_disabled = True
    form_part_1 = html.Div(
        [
            dmc.Stack(
                [
                    dmc.Center(dmc.Text("客户专用申请单", weight=700, id=id("title"))),
                    dmc.Divider(),
                    dmc.Group(
                        [
                            dmc.Select(
                                label="申请人(Applicant)",
                                withAsterisk=True,
                                size="xs",
                                id=id("applicant"),
                                placeholder="申请人(Applicant)",
                                value=nt_name,
                                data=users["nt_name"].tolist(),
                                disabled=applicant_disabled,
                                searchable=True,
                            ),
                            dmc.TextInput(
                                label="客户名称(Customer)",
                                withAsterisk=True,
                                size="xs",
                                style={"width": 200},
                                id=id("customer"),
                            ),
                        ]
                    ),
                    table,
                ]
            ),
            tab_add_row,
            dmc.Space(h=5),
        ]
    )
    return dmc.Container(dmc.Stack([form_part_1, attachment, submit]))
