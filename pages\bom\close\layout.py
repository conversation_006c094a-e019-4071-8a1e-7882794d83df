# -*- coding: utf-8 -*-
from io import BytesIO

import dash_mantine_components as dmc
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dcc

from common import id_factory, parse_search, read_sql

id = id_factory(__name__)


def layout(**query):
    prtno = query.get("prtno")
    layout = dmc.Container(
        dmc.Stack(
            [
                dmc.Center(dmc.Text(prtno, weight=800, underline=True)),
                dmc.Divider(),
                dmc.<PERSON><PERSON>("下载样制BOM", id=id("download-btn")),
                dcc.Download(id=id("download-data")),
            ],
            spacing=10,
        ),
        fluid=True,
    )
    return layout


@callback(
    Output(id("download-data"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State("url", "search"),
)
def download_smbom(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate
    url = parse_search(search)
    prtno = url.get("prtno")
    prt_id = url.get("prt_id")
    sql = "select PrtNo,DesignNO,DeltaPN,DES,MFGNAME,MFGPN,Packaging,Checkcode \
        from ssp.smbom where prt_id=%s"
    df = read_sql(sql, params=[prt_id])
    df.dropna(axis=1, how="all", inplace=True)

    bio = BytesIO()
    with pd.ExcelWriter(bio, engine="xlsxwriter") as writer:
        df.to_excel(writer, sheet_name=prtno, index=False)
        worksheet = writer.sheets[prtno]  # pull worksheet object
        for i, col in enumerate(df.columns):
            width = max(df[col].apply(lambda x: len(str(x))).max(), len(col))
            worksheet.set_column(i, i, width)
        writer.save()
        bio.seek(0)
        workbook = bio.read()
    return dcc.send_bytes(workbook, f"{prtno}_SMBOM.xlsx")
