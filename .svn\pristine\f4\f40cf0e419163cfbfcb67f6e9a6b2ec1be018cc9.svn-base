<svg width="220" height="100" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg" aria-labelledby="logoTitle logoDesc">
  <title id="logoTitle">AI 搜索 Logo</title>
  <desc id="logoDesc">一个放大镜图标，镜片内部有代表人工智能的连接节点图案，右侧附有"AI 搜索"字样。</desc>

  <defs>
    <!-- 定义镜片区域的径向渐变填充 -->
    <radialGradient id="aiGradient" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:#4DD0E1; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0097A7; stop-opacity:1" />
    </radialGradient>

    <!-- 剪切路径 -->
    <clipPath id="lensClip">
      <circle cx="40" cy="50" r="18"/>
    </clipPath>
  </defs>

  <!-- 图标部分 (缩小尺寸) -->
  <g id="icon-group">
      <!-- 放大镜结构 -->
      <g id="magnifying-glass" stroke="#37474F" stroke-width="3" fill="none">
         <circle cx="40" cy="50" r="20"/>
         <line x1="54" y1="64" x2="68" y2="78" stroke-linecap="round" stroke-width="5"/>
      </g>

      <!-- 镜片内容区域 -->
      <g id="lens-content">
        <circle cx="40" cy="50" r="18" fill="url(#aiGradient)"/>

        <!-- AI 网络图案 -->
        <g clip-path="url(#lensClip)" stroke="#FFFFFF" stroke-width="1.2" fill="#FFFFFF">
            <line x1="30" y1="40" x2="50" y2="60"/>
            <line x1="50" y1="40" x2="30" y2="60"/>
            <line x1="25" y1="50" x2="55" y2="50"/>
            <line x1="40" y1="35" x2="40" y2="65"/>
            <line x1="33" y1="58" x2="47" y2="42"/>

            <!-- 节点 -->
            <circle cx="30" cy="40" r="2.4"/>
            <circle cx="50" cy="60" r="2.4"/>
            <circle cx="50" cy="40" r="2.4"/>
            <circle cx="30" cy="60" r="2.4"/>
            <circle cx="25" cy="50" r="2"/>
            <circle cx="55" cy="50" r="2"/>
            <circle cx="40" cy="35" r="2"/>
            <circle cx="40" cy="65" r="2"/>
            <circle cx="40" cy="50" r="2.8"/>
        </g>
      </g>
  </g>

  <!-- 文字部分 (调整位置与缩小后的图标对齐) -->
  <text x="75" y="50"
        font-family="'Arial', 'Helvetica', 'Microsoft YaHei', sans-serif"
        font-size="30"
        fill="#37474F"
        text-anchor="start"
        dominant-baseline="middle"
        font-weight="bold">
    AI 搜索
  </text>
</svg>