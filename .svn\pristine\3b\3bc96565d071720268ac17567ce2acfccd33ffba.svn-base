# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components as fac
from dash import html

from common import id_factory

# from dbtool import db
from utils import db

id = id_factory(__name__)

check_items = [
    {"label": "物质成分", "value": "物质成分", "disabled": True},
    {"label": "操作温度", "value": "operation_temp", "disabled": False},
    {"label": "Lifetime/TCT", "value": "life_time", "disabled": False},
    {"label": "封装", "value": "封装", "disabled": True},
    {"label": "Reflow", "value": "Reflow", "disabled": True},
    {"label": "耐燃等级", "value": "flammability", "disabled": False},
    {"label": "产地调查", "value": "产地调查", "disabled": True},
    {"label": "国产厂商和替代料", "value": "国产厂商和替代料", "disabled": True},
    {
        "label": "材料属性:HF/WS/ESD/MSL/LF",
        "value": "材料属性:HF/WS/ESD/MSL/LF",
        "disabled": True,
    },
    {"label": "材料属性:ANTI-S", "value": "材料属性:ANTI-S", "disabled": True},
    {"label": "材料属性:双85", "value": "材料属性:双85", "disabled": True},
    {"label": "工业等级", "value": "工业等级", "disabled": True},
    {"label": "Rohs", "value": "Rohs", "disabled": True},
    {
        "label": "Automotive Parts Survey Form",
        "value": "auto_motive",
        "disabled": False,
    },
    {"label": "重量调查", "value": "重量调查", "disabled": True},
    {
        "label": "X-Ray Sensitivity Survey",
        "value": "X-Ray Sensitivity Survey",
        "disabled": True,
    },
    {"label": "ECCN", "value": "ECCN", "disabled": True},
]

checkbox = dmc.CheckboxGroup(
    children=[
        dmc.Checkbox(
            label=x["label"], value=x["value"], disabled=x["disabled"], size="xs", mb=10
        )
        for x in check_items
    ],
    id=id("survey"),
    label="调查内容",
    orientation="horizontal",
    withAsterisk=True,
    offset="md",
    size="xs",
    mb=5,
    persistence_type="local",
    persistence=True,
)


def form_part_1(rd):
    form_part_1 = dmc.Stack(
        [
            dmc.Center(dmc.Text("材料调查申请单", weight=700, id=id("title"))),
            dmc.Divider(),
            dmc.Paper(
                [
                    html.Div(
                        [
                            dmc.Text("材料信息填写", size="xs", weight=700),
                            dmc.Text(
                                "material information information",
                                color="dimmed",
                                size="xs",
                            ),
                        ],
                    ),
                    dmc.Divider(),
                    dmc.Group(
                        [
                            dmc.Select(
                                label="申请人",
                                data=rd,
                                id=id("applicant"),
                                size="xs",
                                persistence_type="local",
                                persistence=True,
                                searchable=True,
                                clearable=True,
                            ),
                            # dmc.Select(
                            #     label="申请部门",
                            #     data=dept,
                            #     size="xs",
                            #     id=id("dept"),
                            #     persistence_type="local",
                            #     persistence=True,
                            #     searchable=True,
                            #     clearable=True,
                            # ),
                            dmc.TextInput(
                                label="机种名",
                                id=id("model"),
                                size="xs",
                                persistence=True,
                                persistence_type="local",
                            ),
                            dmc.TextInput(
                                label="客户",
                                id=id("customer"),
                                size="xs",
                                persistence=True,
                                persistence_type="local",
                            ),
                            dmc.Select(
                                label="是否指派CE",
                                id=id("assign"),
                                placeholder="Select one",
                                size="xs",
                                data=["是", "否"],
                                persistence_type="local",
                                persistence=True,
                                clearable=True,
                            ),
                        ],
                        spacing=0,
                        align="end",
                        grow=True,
                    ),
                    dmc.Space(h=10),
                    dmc.Paper(
                        checkbox,
                        withBorder=True,
                        shadow="xs",
                        p="xs",
                        style={"background-color": "#f1f5f8"},
                    ),
                    dmc.Space(h=10),
                    fac.AntdDraggerUpload(
                        apiUrl="/upload/",
                        text="上传",
                        id=id("attachment"),
                        lastUploadTaskRecord={},
                        # style={"width": "455px"},
                    ),
                ],
                withBorder=True,
                shadow="xs",
                p="xs",
                style={"background-color": "#f1f5f8"},
            ),
            dmc.Button("提交", id=id("submit")),
            dmc.Divider(id=id("persistence-clear")),
        ]
    )
    return form_part_1


def layout(**kwargs):
    res = db.execute("select distinct nt_name from ssp.user where termdate is null")
    rd = [i["nt_name"].title() for i in res]
    layout = dmc.Container(dmc.Stack([form_part_1(rd)]))
    return layout
