# coding: utf-8
from sqlalchemy import Column, String
from sqlalchemy.dialects.mysql import INTEGER
from sqlalchemy.ext.declarative import declarative_base

from config import db

Base = db.Model


class AMatCatalogue(Base):
    __tablename__ = "a_mat_catalogue"
    __table_args__ = {"schema": "ssp_ce"}

    ID = Column(INTEGER(11), primary_key=True)
    category_1 = Column(String(255))
    category_2 = Column(String(255))
    category_3 = Column(String(255))
    category_4 = Column(String(255))
    category_5 = Column(String(255))
    ce_dataBase_name = Column(String(255))
    ce_keyword_pn = Column(String(255))
    ce_keyword_des = Column(String(255))
    ce_keyword_des_exclusive = Column(String(255))
    ce_keyword_styletp = Column(String(255))
    ce_trigger = Column(INTEGER(11), nullable=False)
    pur_category_3 = Column(String(255))
    pur_keyword_pn = Column(String(255))
    pur_keyword_des = Column(String(255))
    owner_ce = Column(String(255))
    owner_pur = Column(String(255))
    pur_category2 = Column(String(255))


db.create_all()
