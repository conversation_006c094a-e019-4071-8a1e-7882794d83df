import dash_bootstrap_components as dbc
import dash_uploader as du
import numpy as np
import pandas as pd
from config import UPLOAD_FOLDER_ROOT
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    dash,
    dcc,
    html,
    no_update,
    Serverside,
)
from dash_tabulator import DashTabulator
from tasks import bg_access_record
from common import mat_info
import dash_mantine_components as dmc
import feffery_antd_components as fac
import duckdb as dk
import openpyxl
from openpyxl.styles import PatternFill
from datetime import datetime
from components.file_browser import file_browser
from config import SSP_DIR

dash.register_page(__name__, title="YPCS21")

uploader = du.Upload(
    id="dash-uploader",
    text="点击上传YPCS21文件",
    filetypes=["xlsx"],
    # default_style={"border-color": "rgba(26, 188, 156)", "min-height": "100%"},
)

redFill = PatternFill(start_color="00FF9900", end_color="00FF9900", fill_type="solid")

style_header = {
    "backgroundColor": "rgba(52, 73, 94)",
    "color": "white",
    "fontWeight": "bold",
    "fontSize": "12px",
    # 'border': '1px solid',
    "textTransform": "uppercase",
}

style_cell = {
    "whiteSpace": "normal",
    "height": "auto",
    "textAlign": "left",
    "font-family": "Helvetica",
    "font-size": "10px",
}

plm = {
    "orig item text line1": "item text line1-1",
    "orig item text line2": "item text line2-1",
    "new item text line1": "item text line1-2",
    "new item text line2": "item text line2-2",
}

alt0 = [chr(i).upper() for i in range(ord("a"), ord("z") + 1)]
alt1 = list(range(10))
alt01 = {f"{i}{j}" for i in alt0 for j in alt0 + alt1}
alt0a = alt0 + [f"A{i}" for i in alt0]

ypcs21_upload = html.Div(
    fac.AntdUpload(
        buttonContent="点击上传YPCS21文件",
        id="ypcs21_upload",
        apiUrl="/upload/",
        fileTypes=["xlsx", "XLSX"],
        showUploadList=False,
    ),
    id="ypcs21_upload_div",
)

tab1_content = html.Div(
    [
        dcc.Store(id="dce-store", data={}),
        dmc.Space(h="xs"),
        ypcs21_upload,
        dmc.Space(h="md"),
        dcc.Loading(id="ypcs21-result"),
        dmc.Space(h="md"),
        dmc.Divider(),
        dmc.Space(h="md"),
        dcc.Loading(id="ypan-result"),
        dcc.Download(id="ypcs21-download"),
    ]
)

tab2_content = html.Div(
    [
        dcc.Store(id="ypcs21-store"),
        dmc.Space(h="xs"),
        dbc.Row(
            [
                dbc.Col(uploader, width=10),
                dbc.Col(dbc.Button("下载处理结果", id="ypcs21-download-btn"), width=2),
            ]
        ),
        html.Br(),
        dcc.Loading(dcc.Download(id="ypcs21-download"), fullscreen=True),
        dcc.Loading(html.Div(id="ypcs21-output"), fullscreen=True),
    ]
)

tab3_content = file_browser(SSP_DIR / "program" / "DOC" / "ypcs21", __name__)


def layout(**q):
    active_tab = q.get("tab", "1")
    layout = dbc.Container(
        [
            dbc.Tabs(
                [
                    dbc.Tab(tab1_content, label="YPCS21&YPAN检查", tab_id="1"),
                    dbc.Tab(tab2_content, label="YPCS21检查", tab_id="2"),
                    dbc.Tab(tab3_content, label="说明文档", tab_id="3"),
                ],
                id="tab-num",
                active_tab=active_tab,
            )
        ],
        fluid=True,
        className="ml-3 pr-5",
    )
    return layout


# ---------回调函数----------------
@callback(
    Output("ypcs21_upload", "uploadId"),
    Input("ypcs21_upload_div", "n_clicks"),
)
def dce_file_upload_id(n):
    t = datetime.now().strftime("%y%m%d%H%M%S")
    return f"ypcs21_{t}"


@callback(
    Output("ypcs21-result", "children"),
    Output("dce-store", "data"),
    Input("ypcs21_upload", "lastUploadTaskRecord"),
    State("ypcs21_upload", "uploadId"),
)
def dce_file_check(record, upload_id):
    if record is None:
        raise PreventUpdate

    file = UPLOAD_FOLDER_ROOT / record["taskId"] / record["fileName"]
    try:
        df = pd.read_excel(file, sheet_name=1, dtype=str, keep_default_na=False)
        head = {"change type", "model name", "assembly"} - set(df.columns.str.lower())
        if head:
            alert = dmc.Alert(
                title="YPCS21必须在第2个Sheet,请确认",
                color="red",
            )
            return alert, no_update
    except Exception:
        return dmc.Alert(title="YPCS21仅找到1个Sheet,请确认", color="red"), no_update

    df.columns = df.columns.str.lower().str.strip()
    if df.columns.isin(plm.keys()).any():
        df = df.rename(columns=plm)

    df["status"] = ""
    c1 = df["change type"].isin(["ADD", "CHG", "DEL"])
    df["status"] = np.where(c1, df["status"], "Change Type限CHG,ADD,DEL")

    c1 = df["change type"] != ""
    c2 = df["model name"] == ""
    df["status"] = np.where(c1 & c2, df["status"] + ",Model Name不能空", df["status"])

    df["component1"] = df["component1"].str.strip()
    df["component2"] = df["component2"].str.strip()
    c1 = df["qty2"] == ""
    c2 = df["qty2"] != ""
    c3 = df["qty2"].str.strip().str.match("^\d+\.?\d{0,3}$")
    df["status"] = np.where(
        c1 | (c2 & c3), df["status"], df["status"] + ",QTY2小数点后最多3位"
    )

    c1 = df["sortstring2"].str.len() > 10
    df["status"] = np.where(c1, df["status"] + ",sortstring2最多10位", df["status"])

    c1 = df["item text line1-2"].str.len() > 40
    df["status"] = np.where(c1, df["status"] + ",item text line1最多40位", df["status"])

    c1 = df["usage1"] == "100%"
    c2 = df["usage1"] == "1"
    c3 = df["usage1"] == "0%"
    df["usage1"] = np.where(c1 | c2, "100", df["usage1"])
    df["usage1"] = np.where(c3, "0", df["usage1"])
    c1 = df["usage2"] == "100%"
    c2 = df["usage2"] == "1"
    c3 = df["usage2"] == "0%"
    df["usage2"] = np.where(c1 | c2, "100", df["usage2"])
    df["usage2"] = np.where(c3, "0", df["usage2"])
    c1 = ~df["usage1"].isin(["100", "0", ""])
    c2 = ~df["usage2"].isin(["100", "0", ""])
    df["status"] = np.where(c1 | c2, df["status"] + ",Usage限100,0,空", df["status"])

    c1 = df["alt. grp2"] != ""
    df["status"] = np.where(c1, df["status"] + ",M列必须为空", df["status"])

    grp = df.groupby(["model name", "sortstring2"]).agg(
        {"usage2": set, "sortstring2": "count"}
    )

    c1 = grp["usage2"].apply(lambda x: "" in x)
    c2 = grp["sortstring2"] >= 2
    ref = grp.loc[c1 & c2].index.get_level_values(1)
    ref = [i for i in ref if i]

    c1 = df["sortstring2"].isin(ref)
    c2 = df["usage2"] == ""
    df["status"] = np.where(
        c1 & c2, df["status"] + ",位置>=2时,GRP(N栏)不能为空", df["status"]
    )

    c1 = df["change type"] == "CHG"
    c2 = df["model name"] == ""
    c3 = df["component1"] == ""
    c4 = df["component2"] == ""
    df["status"] = np.where(
        c1 & (c2 | c3 | c4), df["status"] + ",CHG时,B/D/L列不能空", df["status"]
    )

    c1 = df["change type"] == "DEL"
    c2 = df["model name"] == ""
    c3 = df["component1"] == ""
    df["status"] = np.where(
        c1 & (c2 | c3), df["status"] + ",DEL时,B/D列不能空", df["status"]
    )

    c2 = (
        df[
            [
                "component2",
                "alt. grp2",
                "usage2",
                "qty2",
                "unit2",
                "sortstring2",
                "item text line1-2",
                "item text line2-2",
            ]
        ]
        != ""
    ).any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",DEL时,L至S列必须为空", df["status"]
    )

    c1 = df["change type"] == "ADD"
    c2 = (
        df[
            [
                "component1",
                "alt. grp1",
                "usage1",
                "qty1",
                "unit1",
                "sortstring1",
                "item text line1-1",
                "item text line2-1",
            ]
        ]
        != ""
    ).any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",ADD时,D至K列必须为空", df["status"]
    )

    c2 = (df[["model name", "assembly", "component2"]] == "").any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",ADD时,B/C/L列不能空", df["status"]
    )

    c1 = df["change type"].str.startswith("add evaluation report below")
    c2 = df["change type"] == ""
    df["status"] = np.where(c1 | c2, "", df["status"])

    df1 = df.loc[df["status"] != ""]
    if not df1.empty:
        wb = openpyxl.load_workbook(file)
        ws = wb.worksheets[1]
        data = df["status"].tolist()
        for i, j in enumerate(data, start=2):
            ws[f"W{i}"].value = j
        wb.save(file)
        alert = dmc.Alert(
            [
                dbc.Button(
                    "下载YPCS21检查结果",
                    id="dce-download",
                    size="sm",
                    download=file.as_posix(),
                ),
            ],
            title="YPCS21检查错误,请参考W列修改",
            color="red",
        )
        return alert, no_update
    else:
        alert = dmc.Alert(
            fac.AntdUpload(
                buttonContent="点击上传YPAN文件",
                id="ypan_upload",
                apiUrl="/upload/",
                multiple=True,
                fileTypes=["xlsx", "XLSX"],
                showUploadList=False,
                uploadId=upload_id,
            ),
            title="YPCS21检查正确",
            color="green",
        )
        return alert, Serverside({"data": df, "file": file.as_posix()})


@callback(
    Output("ypan-result", "children"),
    Output("ypcs21-result", "children"),
    Input("ypcs21_upload_div", "n_clicks"),
)
def clear_ypan_result(n):
    return None, None


@callback(
    Output("ypcs21-download", "data"),
    Input("dce-download", "n_clicks"),
    State("dce-download", "download"),
)
def dce_download(n_clicks, file):
    if n_clicks is None:
        raise PreventUpdate
    return dcc.send_file(file)


@callback(
    Output("ypan-result", "children"),
    Input("ypan_upload", "lastUploadTaskRecord"),
    State("dce-store", "data"),
    State("user", "data"),
)
def ypan_file_check(record, store, user):
    if not record:
        raise PreventUpdate
    alert = []
    set_ypan = set()
    dfs = []
    dce = store["data"]
    dce_file = store["file"]
    dce = dce.reset_index()
    dce["index"] = dce["index"] + 2
    dce = dce.rename(columns={"index": "rowid"})
    dce = dce.loc[dce["change type"].str.upper().isin(["CHG", "DEL", "ADD"])]

    files = {UPLOAD_FOLDER_ROOT / i["taskId"] / i["fileName"] for i in record}
    for file in files:
        if file.exists():
            df = pd.read_excel(file, dtype=str, keep_default_na=False)
            df.columns = df.columns.str.lower()
            head = {"level", "part no", "alt"} - set(df.columns)
            if head:
                alert.append(dmc.Text(f"{file.stem}文件格式不正确"))
                continue
            c2 = df.iloc[0, 2].upper()
            file_name = file.stem.upper()
            if file_name != c2:
                alert.append(dmc.Text(f"{file_name},请命名为{c2}"))
            else:
                set_ypan.add(file_name)
                df["model_name"] = c2
                dfs.append(df)
    if alert:
        return dmc.Alert(alert, title="文件名不符合要求", color="red")
    else:
        set_dce = {i for i in dce["model name"] if i}
        x = set_dce - set_ypan
        y = set_ypan - set_dce

        if x:
            alert = dmc.List([dmc.ListItem(i) for i in x])
            return dmc.Alert(alert, title="如下YPAN文件未上传", color="red")

        if y:
            alert = dmc.List([dmc.ListItem(i) for i in y])
            return dmc.Alert(alert, title="如下YPAN文件无需上传", color="red")

        ypan = pd.concat(dfs, ignore_index=True)
        ypan.columns = ypan.columns.str.replace("/", "", regex=False).str.replace(
            "\s+", "_", regex=True
        )
        ypan = ypan.reindex(
            columns=[
                "model_name",
                "parent_level_pn",
                "part_no",
                "design_no",
                "alt",
                "%",
                "qpa",
                "um",
                "item_text",
            ]
        )
        ypan = ypan.applymap(str.strip)
        # dce["item text line"] = dce["item text line1-1"] + dce["item text line2-1"]
        d1 = [
            "model name",
            "assembly",
            "component1",
            "sortstring1",
            "alt. grp1",
            "usage1",
            "qty1",
            "unit1",
            "item text line1-1",
        ]
        d2 = [
            "model_name",
            "parent_level_pn",
            "part_no",
            "design_no",
            "alt",
            "%",
            "qpa",
            "um",
            "item_text",
        ]
        title = "YPAN处理成功"
        dd = dict(zip(d1, d2))
        for i, j in dce.iterrows():
            if j["change type"] != "ADD":
                idx = j[d1][j != ""].index
                col = [dd[i] for i in dd if i in idx]
                cond = " and ".join(f'"{i}"=?' for i in col)
                sql = f"select * from ypan where {cond}"
                params = j[idx]
                dfi = dk.execute(sql, params).df()
                if dfi.empty:
                    dce.loc[i, "status"] = f"{','.join(idx)}合并信息,在YPAN上不存在"
                else:
                    if dfi.shape[0] > 1:
                        dce.loc[i, "status"] = f"{','.join(idx)}合并信息,在YPAN上不唯一"
                    else:
                        for x, y in dd.items():
                            dce.loc[i, x] = dfi[y][0]
            else:
                sql = "select * from ypan where model_name=? and parent_level_pn=?"
                params = [j["model name"], j["assembly"]]
                dfi = dk.execute(sql, params).df()
                if dfi.empty:
                    dce.loc[i, "status"] = f"Assembly({j['assembly']}),在YPAN上不存在"
                    title = "ADD时,组合料号在YPAN中不存在,请参考W栏"

        dfx = dce.loc[(dce["status"] != "") & (dce["change type"] != "ADD")]
        if not dfx.empty:
            wb = openpyxl.load_workbook(dce_file)
            ws = wb.worksheets[1]
            for i in dfx.itertuples():
                ws[f"W{i.Index + 2}"].value = i.status
            wb.save(dce_file)
            btn = dbc.Button(
                "下载YPAN检查结果",
                id="dce-download",
                size="sm",
                download=dce_file,
            )
            return dmc.Alert(btn, title="YPCS21与YPAN信息不匹配", color="red")

        # ===========生成M栏自编码==========
        alt_set = ypan.groupby("parent_level_pn", as_index=False).agg(
            alt_set=("alt", lambda x: sorted(alt01 - set(x)))
        )
        dce_alt = dce.reset_index()
        c1 = dce_alt["change type"] == "ADD"
        c2 = dce_alt["sortstring1"] == ""
        dce_alt["sortstring1"] = np.where(
            c1 & c2, dce_alt["sortstring2"], dce_alt["sortstring1"]
        )
        dce_alt = dce_alt.groupby(
            ["assembly", "sortstring1"], as_index=False
        ).index.agg(set)
        dce_alt = dce_alt.merge(
            alt_set, left_on="assembly", right_on="parent_level_pn", how="inner"
        )
        dce_alt = dce_alt.reset_index()
        dce_alt["alt_new"] = dce_alt.apply(lambda x: x["alt_set"][x["level_0"]], axis=1)
        dce_alt = dce_alt.explode("index")[["index", "alt_new"]]
        dce_alt["index"] = dce_alt["index"].astype(int)

        dce_alt = dce_alt.set_index("index")
        dce = dce.merge(dce_alt, left_index=True, right_index=True, how="left")

        ypan_alt = ypan.groupby(
            ["parent_level_pn", "design_no"], as_index=False
        ).alt.first()

        # ========CHG填充============
        x1 = ["qty1", "unit1", "sortstring1", "item text line1-1", "item text line2-1"]
        x2 = ["qty2", "unit2", "sortstring2", "item text line1-2", "item text line2-2"]
        c1 = dce["change type"] == "CHG"  # A
        for i, j in zip(x1, x2):
            dce[j] = np.where(c1, dce[i], dce[j])

        # ========ADD填充===============
        c1 = dce["change type"] == "ADD"
        c2 = dce["qty2"] == ""
        c3 = dce["unit2"] == ""
        c4 = dce["usage2"] == ""
        dce["qty2"] = np.where(c1 & c2, "1", dce["qty2"])
        dce["unit2"] = np.where(c1 & c3, "PCE", dce["unit2"])

        # ========ALT填充==================
        """
        ALT编写要求：
        1.Q=空白时，A=CHG且E为非空，才能填充M，M=E;     Q=空白时的其他情况程式不做填充
        2.Q=非空时，相同组合料号（C)下相同的位置号（Q),其ALT（M)应该相同，如此位置在YPAN相同组合料号下存在，则应相同
        3.Q=非空时，相同组合料号（C)下的不同位置号（Q),其ALT(M)，不应该编写重复
        4.usage2(N)等于空,(alt. grp2)M等于空
        CHG的ALT填充先执行，ADD的ALT填充后执行
        """
        dce = dce.merge(
            ypan_alt,
            left_on=["assembly", "sortstring2"],
            right_on=["parent_level_pn", "design_no"],
            how="left",
        )

        dce["alt"] = dce["alt"].replace({"": None})

        c1 = dce["sortstring2"] == ""  # Q
        c2 = dce["usage2"] == ""  # N
        c3 = dce["alt"].notna()
        c4 = dce["alt. grp1"] != ""

        dce["alt. grp2"] = np.where(c1 & c4, dce["alt. grp1"], dce["alt. grp2"])
        dce["alt. grp2"] = np.where(~c1 & c3, dce["alt"], dce["alt. grp2"])
        dce["alt. grp2"] = np.where(~c1 & ~c3, dce["alt_new"], dce["alt. grp2"])
        dce["alt. grp2"] = np.where(c2, "", dce["alt. grp2"])

        c1 = dce["usage2"] != ""
        c2 = dce["sortstring2"] == ""
        c3 = dce["alt. grp1"] == ""
        dce["status"] = np.where(
            c1 & c2 & c3, dce["status"] + ",USER需自行编写M栏ALT2", dce["status"]
        )

        dce["design_no_count"] = dce.groupby(["model name", "design_no"])[
            "design_no"
        ].transform("count")

        c0 = dce["sortstring2"] != ""
        c1 = dce["design_no_count"] >= 2
        c2 = dce["alt"].isna()
        # breakpoint()

        dce["status"] = np.where(
            c0 & c1 & c2,
            dce["status"] + ",该位置在YPAN文件中为SINGLE SOURCE,注意调整其GRP(0,100)",
            dce["status"],
        )

        c3 = dce["usage2"] == ""
        dce["status"] = np.where(
            c0 & c1 & c3, dce["status"] + ",多SOURCE,GRP(N栏)不能为空", dce["status"]
        )

        dept_id = user.get("dept_id")
        dce = dcbu_ypcs21(dept_id, dce)
        dce = dce.fillna("")

        # dce.index = dce.index + 2

        dce = dce.set_index("rowid")
        dce.columns = alt0a[: dce.columns.size]

        # ========写入excel=============
        wb = openpyxl.load_workbook(dce_file)
        ws = wb.worksheets[1]

        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.fill = PatternFill("solid", fgColor="FFFFFF")
        col_all = (
            "C",
            "D",
            "L",
            "F",
            "N",
            "T",
            "U",
            "W",
            "AC",
            "AD",
            "AE",
            "AF",
            "AG",
            "AH",
        )  # "D", "L", "F", "N"覆盖，其他不覆盖
        col_chg = ("E", "F", "G", "H", "I", "J", "K", "O", "P", "Q", "R", "S", "M")
        col_add = ("O", "P", "M")

        for row in dce.itertuples():
            if row.A in ("CHG", "DEL"):
                cols = col_all + col_chg
            elif row.A == "ADD":
                cols = col_all + col_add
            for col in cols:
                cella = getattr(row, col)
                if cella:
                    cellb = ws[f"{col}{row.Index}"]
                    if col in col_all:
                        if cella != cellb.value:
                            cellb.value = cella
                            cellb.fill = redFill
                    else:
                        if not cellb.value:
                            if cella != cellb.value:
                                cellb.value = cella
                                cellb.fill = redFill
        wb.save(dce_file)
        btn = dbc.Button(
            "下载更新后的YPCS21文件", id="dce-download", size="sm", download=dce_file
        )
        return dmc.Alert(btn, id="ypan-alert", title=title, color="green")


@callback(
    Output("ypcs21-download", "data"),
    Input("ypcs21-download-btn", "n_clicks"),
    State("ypcs21-store", "data"),
    State("user", "data"),
)
def ypcs21_download(n_clicks, data, user):
    if not data:
        raise PreventUpdate

    file_path = data["file"]
    df = data["data"]
    df = df.fillna("")

    data = df.values.tolist()
    wb = openpyxl.load_workbook(file_path)
    ws = wb.worksheets[1]

    for index, row in enumerate(data, start=2):
        for col_num, value in enumerate(row, start=1):
            try:
                ws.cell(row=index, column=col_num).value = value
            except Exception as e:
                pass

    new_file_path = file_path.parent / f"{file_path.stem}_Result{file_path.suffix}"
    wb.save(new_file_path)

    bg_access_record(user, "YPCS21", "下载")
    return dcc.send_file(new_file_path)


@callback(
    Output("ypcs21-output", "children"),
    Output("ypcs21-store", "data"),
    Input("dash-uploader", "isCompleted"),
    State("dash-uploader", "fileNames"),
    State("dash-uploader", "upload_id"),
    State("user", "data"),
)
def callback_on_completion(iscompleted, filenames, upload_id, user):
    if not iscompleted:
        raise PreventUpdate
    if filenames is None:
        return html.Div("No Files Uploaded Yet!"), no_update

    file = UPLOAD_FOLDER_ROOT / upload_id / filenames[0]
    df = pd.read_excel(file, sheet_name=1, dtype=str, keep_default_na=False)
    df.columns = df.columns.str.lower().str.strip()

    dept_id = user.get("dept_id")
    if dept_id == 23:
        df = lgt_ypcs21(dept_id, df)
    elif dept_id in (1, 10):
        df = dcbu_ypcs21(dept_id, df)
    else:
        df = dcbu_ypcs21(dept_id, df)

    columns = []
    for i in df.columns:
        if i == "change type":
            columns.append(
                {"title": i, "field": i, "width": 100, "formatter": "textarea"}
            )
        else:
            columns.append(
                {
                    "title": i,
                    "field": i,
                }
            )

    output = DashTabulator(
        id="ypcs21-table",
        data=df.to_dict(orient="records"),
        options={"layout": "fitData", "height": "400px"},
        columns=columns,
    )
    bg_access_record(user, "YPCS21", "上传")
    return output, Serverside({"file": file, "data": df})


def dcbu_ypcs21(dept_id: int, df: pd.DataFrame):
    if df.columns.isin(plm.keys()).any():
        df = df.rename(columns=plm)

    origin_columns = df.columns
    if "status" not in df.columns:
        df["status"] = ""
    c1 = df["change type"].isin(["ADD", "CHG", "DEL"])
    df["status"] = np.where(c1, df["status"], "Change Type限CHG,ADD,DEL")
    c1 = df["change type"] != ""
    c2 = df["model name"] == ""
    c3 = df["assembly"] == ""
    df["status"] = np.where(
        c1 & (c2 | c3), df["status"] + ",Model Name和Assembly不能空", df["status"]
    )
    df["component1"] = df["component1"].str.strip()
    df["component2"] = df["component2"].str.strip()

    c1 = df["qty2"] == ""
    c2 = df["qty2"] != ""
    c3 = df["qty2"].str.strip().str.match("^\d+\.?\d{0,3}$")
    df["status"] = np.where(
        c1 | (c2 & c3), df["status"], df["status"] + ",QTY2小数点后最多3位"
    )

    c1 = df["sortstring2"].str.len() > 10
    df["status"] = np.where(c1, df["status"] + ",sortstring2最多10位", df["status"])
    c1 = df["item text line1-2"].str.len() > 40
    df["status"] = np.where(c1, df["status"] + ",item text line1最多40位", df["status"])

    c1 = df["usage1"] == "100%"
    c2 = df["usage1"] == "1"
    c3 = df["usage1"] == "0%"
    df["usage1"] = np.where(c1 | c2, "100", df["usage1"])
    df["usage1"] = np.where(c3, "0", df["usage1"])
    c1 = df["usage2"] == "100%"
    c2 = df["usage2"] == "1"
    c3 = df["usage2"] == "0%"
    df["usage2"] = np.where(c1 | c2, "100", df["usage2"])
    df["usage2"] = np.where(c3, "0", df["usage2"])

    c1 = ~df["usage1"].isin(["100", "0", ""])
    c2 = ~df["usage2"].isin(["100", "0", ""])
    df["status"] = np.where(c1 | c2, df["status"] + ",Usage限100,0,空", df["status"])

    c1 = df["change type"] == "CHG"
    c2 = df["component1"] == ""
    c3 = df["qty1"] == ""
    c4 = df["unit1"] == ""
    c5 = df["component2"] == ""
    c6 = df["qty2"] == ""
    c7 = df["unit2"] == ""
    df["status"] = np.where(
        c1 & (c2 | c3 | c4 | c5 | c6 | c7),
        df["status"] + ",CHG时,D/G/H/L/O/P列不能空",
        df["status"],
    )
    c2 = (
        df["component1"]
        + df["usage1"]
        + df["qty1"]
        + df["unit1"]
        + df["sortstring1"]
        + df["item text line1-1"]
        == df["component2"]
        + df["usage2"]
        + df["qty2"]
        + df["unit2"]
        + df["sortstring2"]
        + df["item text line1-2"]
    )
    df["status"] = np.where(
        c1 & c2, df["status"] + ",CHG时,变更前后信息不能相同", df["status"]
    )

    c1 = df["change type"] == "ADD"
    c2 = (
        df[
            [
                "component1",
                "alt. grp1",
                "usage1",
                "qty1",
                "unit1",
                "sortstring1",
                "item text line1-1",
                "item text line2-1",
            ]
        ]
        != ""
    ).any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",ADD时,D至K列必须为空", df["status"]
    )
    c2 = (df[["component2", "qty2", "unit2"]] == "").any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",ADD时,L/O/P列不能空", df["status"]
    )

    c1 = df["change type"] == "DEL"
    c2 = (df[["component1", "qty1", "unit1"]] == "").any(1)
    df["status"] = np.where(
        c1 & c2, df["status"] + ",DEL时,D/G/H列不能空", df["status"]
    )
    c2 = (
        df[
            [
                "component2",
                "alt. grp2",
                "usage2",
                "qty2",
                "unit2",
                "sortstring2",
                "item text line1-2",
                "item text line2-2",
            ]
        ]
        != ""
    ).any(1)

    df["status"] = np.where(
        c1 & c2, df["status"] + ",DEL时,L至S列必须为空", df["status"]
    )

    c1 = df["change type"].str.startswith("add evaluation report below")
    c2 = df["change type"] == ""
    df["status"] = np.where(c1 | c2, "", df["status"])

    params = df["component1"].unique().tolist() + df["component2"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    df1 = mat_info(dept_id, "all", cond, params)

    df1 = df1[["deltapn", "des", "mfgname", "mfgpn", "block", "remark"]]
    df1 = df1.drop_duplicates(["deltapn"])
    df = df.merge(df1, left_on="component2", right_on="deltapn", how="left")

    df["description (after)"] = df["des"]
    df["vendor(after)"] = df["mfgname"]
    df["vendor pn(after)"] = df["mfgpn"]
    df["block_status\n（程式生成）"] = df["block"]
    df["remark\n（程式生成）"] = df["remark"]
    df["表单填写问题\n（程式生成）"] = df["status"]

    df = df.merge(df1, left_on="component1", right_on="deltapn", how="left")
    df["description (before)"] = df["des_y"]
    df["vendor(before)"] = df["mfgname_y"]
    df["vendor pn(before)"] = df["mfgpn_y"]

    df = df.reindex(columns=origin_columns)
    return df


def lgt_ypcs21(dept: str, df: pd.DataFrame):
    df.insert(0, "remark", "")
    c1 = df["change type"].isin(["ADD", "CHG", "DEL"])
    df["remark"] = np.where(c1, df["remark"], "Change Type限CHG,ADD,DEL")
    c1 = df["change type"] != ""
    c2 = df["model name"] == ""
    c3 = df["assembly"] == ""
    df["remark"] = np.where(
        c1 & (c2 | c3), df["remark"] + ",Model Name和Assembly不能空", df["remark"]
    )
    df["component1"] = df["component1"].str.strip()
    df["component2"] = df["component2"].str.strip()

    c1 = df["qty2"] == ""
    c2 = df["qty2"] != ""
    c3 = df["qty2"].str.strip().str.match("^\d+\.?\d{0,3}$")
    df["remark"] = np.where(
        c1 | (c2 & c3), df["remark"], df["remark"] + ",QTY2小数点后最多3位"
    )

    c1 = df["sortstring2"].str.len() > 10
    df["remark"] = np.where(c1, df["remark"] + ",sortstring2最多10位", df["remark"])
    c1 = df["item text line1"].str.len() > 40
    df["remark"] = np.where(c1, df["remark"] + ",item text line1最多40位", df["remark"])

    c1 = df["usage1"] == "100%"
    c2 = df["usage1"] == "1"
    c3 = df["usage1"] == "0%"
    df["usage1"] = np.where(c1 | c2, "100", df["usage1"])
    df["usage1"] = np.where(c3, "0", df["usage1"])
    c1 = df["usage2"] == "100%"
    c2 = df["usage2"] == "1"
    c3 = df["usage2"] == "0%"
    df["usage2"] = np.where(c1 | c2, "100", df["usage2"])
    df["usage2"] = np.where(c3, "0", df["usage2"])

    c1 = ~df["usage1"].isin(["100", "0", ""])
    c2 = ~df["usage2"].isin(["100", "0", ""])
    df["remark"] = np.where(c1 | c2, df["remark"] + ",Usage限100,0,空", df["remark"])

    c1 = df["change type"] == "CHG"
    c2 = df["component1"] == ""
    c3 = df["qty1"] == ""
    c4 = df["unit1"] == ""
    c5 = df["component2"] == ""
    c6 = df["qty2"] == ""
    c7 = df["unit2"] == ""
    df["remark"] = np.where(
        c1 & (c2 | c3 | c4 | c5 | c6 | c7),
        df["remark"] + ",CHG时,Component,QTY,Unit不能空",
        df["remark"],
    )

    c2 = (
        df["component1"] + df["usage1"] + df["qty1"] + df["unit1"] + df["sortstring1"]
        == df["component2"]
        + df["usage2"]
        + df["qty2"]
        + df["unit2"]
        + df["sortstring2"]
    )

    df["remark"] = np.where(
        c1 & c2, df["remark"] + ",CHG时,变更前后信息不能相同", df["remark"]
    )

    c1 = df["change type"] == "ADD"
    c2 = (
        df[
            [
                "component1",
                "alt. grp1",
                "usage1",
                "qty1",
                "unit1",
                "sortstring1",
                "item text line1",
                "item text line2",
            ]
        ]
        != ""
    ).any(1)
    df["remark"] = np.where(
        c1 & c2, df["remark"] + ",ADD时,D至K列必须为空", df["remark"]
    )
    c2 = (df[["component2", "qty2", "unit2"]] == "").any(1)
    df["remark"] = np.where(
        c1 & c2, df["remark"] + ",ADD时,Component2,QTY2,Unit2不能空", df["remark"]
    )

    c1 = df["change type"] == "DEL"
    c2 = (df[["component1", "qty1", "unit1"]] == "").any(1)
    df["remark"] = np.where(
        c1 & c2, df["remark"] + ",DEL时,Component1,QTY1,Unit1不能空", df["remark"]
    )
    c2 = (df[["component2", "qty2", "unit2"]] != "").any(1)
    df["remark"] = np.where(
        c1 & c2, df["remark"] + ",DEL时,Component2,QTY2,Unit2必须为空", df["remark"]
    )

    params = df["component2"].unique().tolist()
    ph = ",".join(["%s"] * len(params))
    cond = f"deltapn in ({ph})"
    df1 = mat_info(dept, "all", cond, params)
    df1 = df1[["deltapn", "des", "mfgname", "mfgpn", "remark"]]
    df1 = df1.rename(columns={"deltapn": "component2", "remark": "status2"})
    df = df.merge(df1, on="component2", how="left")
    df["description"] = np.where(df["des"] != "", df["des"], df["description"])
    df["vendor"] = np.where(df["mfgname"] != "", df["mfgname"], df["vendor"])
    df["vendor pn"] = np.where(df["mfgpn"] != "", df["mfgpn"], df["vendor pn"])
    df["status"] = np.where(df["status2"] != "", df["status2"], df["status"])
    df = df.drop(["des", "mfgname", "mfgpn", "status2"], axis=1)
    return df
