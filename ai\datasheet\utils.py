import os
from datetime import date, datetime
from enum import Enum

import instructor
from openai import OpenAI
from pydantic import BaseModel, Field
import openai


# class Result(BaseModel):
#     result: str = Field(description="结果")
#     thinking: str = Field(description="模型思考过程")


class DataSheet(BaseModel):
    mfgpn: str = Field(description="厂商料号")
    drain_to_source_voltage_vdss: float = Field(description="单位为伏特(V)")
    drain_current_id: float = Field(description="单位为安培(A)")
    rdson_typ: float = Field(description="RDS(on),typ,单位为毫欧(mohm)")
    rdson_max: float = Field(description="RDS(on),max,单位为毫欧(mohm)")
    qg_typ: float = Field(description="单位为nC")
    tr_typ: float = Field(description="Rise time,单位为ns")
    tf_typ: float = Field(description="Fall time,单位为ns")
    coss_typ: float = Field(description="Output capacitance,单位为pf")
    qrr_typ: float = Field(description="Reverse recovery charg,单位为nc")
    eon: float = Field(description="单位为uJ")
    eoff: float = Field(description="单位为uJ")
    spec: str = Field(description="规格")
    package: str = Field(description="封装名称")
    smd_dip: str = Field(description="贴片材料还是插件材料")


def call_llm(prompt):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/Qwen/Qwen3-235B-A22B-FP8"

    # api_key = os.getenv("OPENROUTER_API_KEY")
    # base_url = "https://openrouter.ai/api/v1"
    # model = "google/gemini-2.5-flash"

    # api_key = os.getenv("siliconflow_api_key")
    # base_url = "https://api.siliconflow.cn/v1"
    # model = "Pro/moonshotai/Kimi-K2-Instruct"

    client = instructor.from_openai(
        OpenAI(api_key=api_key, base_url=base_url),
        mode=instructor.Mode.JSON,
    )
    res = client.chat.completions.create(
        model=model,
        response_model=DataSheet,
        messages=[{"role": "user", "content": prompt}],
    )
    return res


def call_embedding(input: list):
    api_key = os.getenv("QWEN3_API_KEY")
    base_url = "https://llmgateway.deltaww.com/v1/"
    model = "openai/jinaai/jina-embeddings-v3"
    client = openai.OpenAI(api_key=api_key, base_url=base_url)
    responses = client.embeddings.create(input=input, model=model)
    result = [data.embedding for data in responses.data]
    return result
