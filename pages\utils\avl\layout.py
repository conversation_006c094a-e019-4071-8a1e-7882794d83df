# -*- coding: utf-8 -*-
import dash_mantine_components as dmc
import feffery_antd_components.alias as fac
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback

from common import id_factory
from config import SSP_DIR, UPLOAD_FOLDER_ROOT

id = id_factory(__name__)


def avl_rule():
    avl_file = SSP_DIR / "3. Document backup" / "ssp_doc" / "avl.xlsx"
    df = pd.read_excel(avl_file, skiprows=4, dtype=str)
    df1 = df.loc[:, ["cat2", "deltapn", "des", "voltage", "current", "avl"]]
    df1["cat2"] = df1["cat2"].str.strip()
    df1["deltapn"] = df1["deltapn"].str.strip()
    df1["des"] = df1["des"].str.strip()
    df1["voltage"] = df1["voltage"].str.strip()
    df1["current"] = df1["current"].str.strip()
    df1 = df1.dropna(how="all")

    df1["cat2"] = df1["cat2"].fillna(method="ffill")
    df1["avl"] = df1["avl"].fillna(method="ffill")
    df1["deltapn"] = df1["deltapn"].fillna(".")
    df1["des"] = df1["des"].fillna(".")
    df1["voltage"] = df1["voltage"].fillna("")
    df1["current"] = df1["current"].fillna("")

    df2 = df.loc[
        :,
        [
            "cat2",
            "priority",
            "mfgname1",
            "mfgname2",
            "mfgname3",
            "mfgname4",
            "mfgname5",
            "mfgname6",
            "mfgname7",
            "mfgname8",
        ],
    ]
    df2["cat2"] = df2["cat2"].fillna(method="ffill")
    df2 = df2.replace(r"\（.*?\）", "", regex=True)
    df2 = df2.replace(r"\(.*?\)", "", regex=True)
    df2 = df2.melt(
        id_vars=["cat2", "priority"],
        value_vars=[
            "mfgname1",
            "mfgname2",
            "mfgname3",
            "mfgname4",
            "mfgname5",
            "mfgname6",
            "mfgname7",
            "mfgname8",
        ],
        var_name="mfgname_col",
        value_name="vendor name",
    )
    df2 = df2.dropna(subset=["vendor name"])
    df2 = df2.drop("mfgname_col", axis=1)
    df2 = df2.drop_duplicates(["cat2", "vendor name"])
    df2["vendor name"] = df2["vendor name"].str.strip().str.upper()
    return df1, df2


def layout(user=None, **kwargs):
    upload = fac.DraggerUpload(
        apiUrl="/upload/",
        text="上传附件",
        id=id("upload"),
        lastUploadTaskRecord={},
        fileTypes=["xlsx", "xls", "XLSX"],
        # style={
        #     "width": "100%",
        #     "height": "200px",
        #     "background": "#fafafa",
        #     "border": "1px dashed #d9d9d9",
        #     "borderRadius": "4px",
        #     "cursor": "pointer",
        #     "marginBottom": "20px",
        # },
    )

    tabs = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab("AVL", value="1"),
                ]
            ),
            dmc.TabsPanel(upload, value="1"),
        ],
        color="red",
        value="1",
        id=id("tabs"),
    )
    return tabs


@callback(
    Input(id("upload"), "lastUploadTaskRecord"),
)
def upload(record):
    if not record:
        raise PreventUpdate

    attach = f"{record.get('taskId')}"  # 获取上传附件的ID（自动生成）
    file_name = f"{record.get('fileName')}"  # 获取上传附件的文件名
    df_ypan = pd.read_excel(
        UPLOAD_FOLDER_ROOT / attach / file_name, dtype=str, keep_default_na=False
    )
    df_ypan = df_ypan.reset_index()
    df_ypan.columns = df_ypan.columns.str.lower()

    df0 = df_ypan[["index", "part no", "description", "vendor name"]]
    df0["vendor name"] = df0["vendor name"].str.strip().str.upper()
    df0["package"] = df0["description"].str.extract(r"(\b\d{4}\b|\S+-\d+P)")

    df0[["vol", "vol_unit"]] = df0["description"].str.extract(
        r"\s(?P<vol>[\d\.]+)(?P<vol_unit>[mK]?)V[\sAC]"
    )
    df0["vol_unit"] = df0["vol_unit"].str.replace("K", "1000").str.replace("m", "0.001")
    df0["vol_unit"] = np.where(df0["vol_unit"] == "", "1", df0["vol_unit"])
    df0["voltage_v"] = pd.to_numeric(df0["vol"], errors="coerce") * pd.to_numeric(
        df0["vol_unit"], errors="coerce"
    )
    df0[["cur", "cur_unit"]] = df0["description"].str.extract(
        r"\s(?P<cur>[\d\.]*-?[\d\.]+)(?P<cur_unit>[KmMNu]?)A[\sAC]"
    )
    df0["cur_unit"] = (
        df0["cur_unit"]
        .str.replace("K", "1000")
        .str.replace("M", "1000000")
        .str.replace("m", "0.001")
        .str.replace("u", "0.000001")
    )
    df0["cur_unit"] = np.where(df0["cur_unit"] == "", "1", df0["cur_unit"])
    df0["current_v"] = pd.to_numeric(df0["cur"], errors="coerce") * pd.to_numeric(
        df0["cur_unit"], errors="coerce"
    )
    df1, df2 = avl_rule()

    tmp = []
    for i in df1.itertuples():
        if "," in i.deltapn:
            c1 = df0["part no"].str.startswith(tuple(i.deltapn.split(",")))
        elif "-" in i.deltapn:
            start, end = i.deltapn.split("-")
            range_list = tuple(str(i) for i in range(int(start), int(end)))
            c1 = df0["part no"].str.startswith(range_list)
        else:
            c1 = df0["part no"].str.startswith(i.deltapn)

        if i.des.startswith("~"):
            c2 = ~df0["description"].str.contains(i.des[1:], case=False)
        else:
            c2 = df0["description"].str.contains(i.des, case=False)

        dfi = df0.loc[c1 & c2]
        if i.voltage:
            c3 = dfi.eval(f"voltage_v {i.voltage}")
            dfi = dfi.loc[c3]
        if i.current:
            c4 = dfi.eval(f"current_v {i.current}")
            dfi = dfi.loc[c4]

        if not dfi.empty:
            if i.cat2:
                dfi["cat2"] = i.cat2
                dfi["avl"] = i.avl
            tmp.append(dfi)
            df0 = df0.loc[~df0["index"].isin(dfi["index"])]

    dfx = pd.concat(tmp)
    dfx = dfx.merge(df2, on=["cat2", "vendor name"], how="left")
    dfx = dfx.reindex(columns=["index", "cat2", "avl", "package", "priority"])
    df_ypan = df_ypan.merge(dfx, on="index", how="left")
    df_ypan = df_ypan.drop(columns=["index"])
    df_ypan.to_excel("d:/result.xlsx", index=False)
