.ag-theme-alpine.headers1 {
  --ag-header-height: 30px;
  /* --ag-input-height: 1px !important; */
  height: auto !important;
  word-wrap: break-word;
  /* --ag-header-foreground-color: white; */
  /* --ag-header-background-color: black; */
  /* --ag-header-cell-hover-background-color: rgb(80, 40, 140); */
  /* --ag-header-cell-moving-background-color: rgb(80, 40, 140); */

}

/* .ag-theme-alpine.headers1 .ag-header.headers1 {
  font-family: cursive;
}

.ag-theme-alpine.headers1 .ag-header-group-cell.headers1 {
  font-weight: normal;
  font-size: 22px;
}

.ag-theme-alpine.headers1 .ag-header-cell.headers1 {
  font-size: 18px;
} */
.ag-center-cols-clipper {
  min-height: unset !important;
}

/* 表格头部纵向框线 */
.ag-header-cell {
  border-right: 1px solid #ccc;
}

/* 表格单元格纵向框线 */
.ag-cell {
  border-right: 1px solid #ccc !important;
}

/* 表头换行 */
.ag-header-cell-label {
  word-break: break-all;
}

.lock-visible-col {
  background: #66c2a5;
}

.grid-green-row {
  background-color: #66c2a5 !important;
}

.grid-blue-row {
  background-color: #119dff !important;
}

.row-dragging-grid-to-grid-container {
  display: flex;
  align-items: center;
  column-gap: 20px;
}

#div-row-dragging-grid2grid-complex-bin {
  transition: transform 500ms;
}
.ag-theme-alpine.compact {
  --ag-grid-size: 3px;
}
.ag-theme-quartz-dark.compact {
  --ag-grid-size: 3px;
}

#smd-outsourcing-date-picker{
  height:20px !important;
  min-height: 20px !important;
  max-height: 20px !important;
  /* display: none; */
}

#project-dmc-select{
  height:20px !important;
  min-height: 20px !important;
  max-height: 20px !important;
  /* display: none; */
}