from datetime import datetime

import dash_bootstrap_components as dbc
import dash_uploader as du
import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dash, dcc, html
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator

from common import read_sql
from config import UPLOAD_FOLDER_ROOT, pool
from tasks import bg_label_print, bg_mail

dash.register_page(__name__, title="资产")
ns = Namespace("myNamespace", "tabulator")

asset_menu = dbc.DropdownMenu(
    [
        dbc.DropdownMenuItem("历史记录", id="self-processing", n_clicks_timestamp=0),
        dbc.DropdownMenuItem("名下资产", id="self-asset", n_clicks_timestamp=0),
        dbc.DropdownMenuItem("部门资产", id="dept-asset", n_clicks_timestamp=0),
        dbc.DropdownMenuItem("名下采购", id="self-buy", n_clicks_timestamp=0),
        dbc.DropdownMenuItem("部门采购", id="dept-buy", n_clicks_timestamp=0),
    ],
    label="菜单",
)

modal = dbc.Modal(
    [
        dbc.ModalHeader(className="bg-info"),
        dbc.ModalBody(
            [
                dbc.Row(
                    dbc.Col(
                        DashTabulator(
                            id="modal-asset-table",
                            theme="tabulator_site",
                            options={
                                "placeholder": "请先选择资产",
                                "layout": "fitDataStretch",
                                "height": "150px",
                            },
                        ),
                    ),
                ),
                html.Br(),
                dbc.Row(
                    dbc.Col(
                        du.Upload(
                            text="点击上传报废设备照片",
                            filetypes=["jpg", "png"],
                            default_style={
                                "border-color": "#1ABC9C",
                                # 'min-height':'100%',
                                # 'line-height':'35px',
                                # 'height':'35px',
                            },
                            id="asset-scrap-uploader",
                        ),
                    ),
                    id="uploader-row",
                ),
            ]
        ),
        dbc.ModalFooter(
            [
                dbc.Alert(duration=5000, is_open=False, id="modal-asset-alert"),
                dbc.Button("提交", color="success", id="modal-asset-submit"),
                dbc.Button("关闭", color="danger", id="modal-asset-close"),
            ]
        ),
    ],
    id="modal-asset",
    centered=True,
    is_open=False,
    size="lg",
    scrollable=True,
    backdrop="static",
)

layout = dbc.Container(
    [
        dbc.Row(
            [
                dbc.Col(asset_menu, width=1),
                dbc.Col(
                    dbc.Button(
                        "维修",
                        color="success",
                        id="btn-asset-transfer",
                        n_clicks_timestamp=0,
                    ),
                    width=1,
                    id="col-1",
                ),
                dbc.Col(
                    dbc.Button(
                        "转移",
                        color="warning",
                        id="btn-asset-repair",
                        n_clicks_timestamp=0,
                    ),
                    width=1,
                    id="col-2",
                ),
                dbc.Col(
                    dbc.Button(
                        "报废", color="info", id="btn-asset-scrap", n_clicks_timestamp=0
                    ),
                    width=1,
                    id="col-3",
                ),
                dbc.Col(
                    dbc.Button(
                        "取消",
                        color="danger",
                        id="btn-asset-cancel",
                        n_clicks_timestamp=0,
                    ),
                    width=1,
                    id="col-4",
                ),
                dbc.Col(
                    dbc.Button(
                        "打印标签",
                        color="primary",
                        id="btn-asset-label",
                        n_clicks_timestamp=0,
                    ),
                    width=1,
                    id="col-5",
                ),
            ]
        ),
        html.Br(),
        dcc.Loading(
            DashTabulator(
                id="asset-table",
                theme="tabulator_site",
                downloadButtonType={
                    # "css": "btn btn-primary btn-sm position-absolute bottom-0 start-5 my-1",
                    "css": "btn btn-primary btn-sm end-0 my-1",
                    "text": "下载",
                    "type": "xlsx",
                },
                options={
                    "layout": "fitData",
                    "groupBy": "类型",
                    "height": "430px",
                    "clipboard": "copy",
                    "selectable": True,
                },
            )
        ),
        modal,
        dcc.Store(id="asset-store"),
        # dcc.Store(id='upload-store'),
    ],
    fluid=True,
)


# * ---------------回调函数-----------------
@callback(
    [
        Output("asset-table", "data"),
        Output("asset-table", "columns"),
        Output("col-1", "style"),
        Output("col-2", "style"),
        Output("col-3", "style"),
        Output("col-4", "style"),
        Output("col-5", "style"),
    ],
    [
        Input("self-processing", "n_clicks_timestamp"),
        Input("self-asset", "n_clicks_timestamp"),
        Input("dept-asset", "n_clicks_timestamp"),
        Input("self-buy", "n_clicks_timestamp"),
        Input("dept-buy", "n_clicks_timestamp"),
    ],
    [
        State("user", "data"),
    ],
    prevent_initial_call=False,
)
def menu_selected(n1, n2, n3, n4, n5, user):
    d = (n1, n2, n3, n4, n5)
    idx = d.index(max(d))
    hidden = {"display": "none"}
    show = {"display": "block"}

    dept_id = user.get("dept_id")  # ? EVCS部门禁用资产管理
    if dept_id in (6, 17):
        raise PreventUpdate

    if idx == 0:
        data, columns = asset_content("self-processing", user)
        return data, columns, hidden, hidden, hidden, show, hidden

    elif idx == 1:
        data, columns = asset_content("self-asset", user)
        return data, columns, show, show, show, hidden, show

    elif idx == 2:
        data, columns = asset_content("dept-asset", user)
        return data, columns, show, hidden, hidden, hidden, hidden

    elif idx == 3:
        data, columns = asset_content("self-buy", user)
        return data, columns, hidden, hidden, hidden, hidden, hidden

    elif idx == 4:
        data, columns = asset_content("dept-buy", user)
        return data, columns, hidden, hidden, hidden, hidden, hidden


def asset_content(t: str, user: dict):
    onno = user.get("onno")
    if t == "self-asset":
        sql1 = (
            "select 资产编号,资产名称,资产型号,资产机号,资产规格,配件,资产类别 as 类别,厂商,购入日期,\
            保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
            from ssp_asset.资产清单 where (工号=%s) order by id desc"
        )

        sql2 = (
            'select 资产编号,资产名称,资产型号,资产机号,资产规格,"" as 配件,使用类别 as 类别,厂商,购入日期,\
            保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
            from ssp_asset.内部清单 where (工号=%s) order by id desc'
        )
        params = [onno]

        df1 = read_sql(sql1, params=params)
        df1["来源"] = "资产清单"
        df2 = read_sql(sql2, params=params)
        df2["来源"] = "内部清单"
        df = pd.concat([df1, df2])

        df["购入日期"] = pd.to_datetime(
            df["购入日期"].fillna(pd.NaT), errors="coerce"
        ).dt.date

    elif t == "dept-asset":
        sql1 = (
            "select 资产编号,资产名称,资产型号,资产机号,资产规格,配件,资产类别 as 类别,厂商,购入日期,\
            保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
            from ssp_asset.资产清单 where 工号 in \
            (select onno from ssp.user where \
                dept_id=(select dept_id from ssp.user where onno=%s limit 1)) \
            order by id desc"
        )

        sql2 = (
            'select 资产编号,资产名称,资产型号,资产机号,资产规格,"" as 配件,使用类别 as 类别,厂商,购入日期,\
            保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
            from ssp_asset.内部清单 where 工号 in \
            (select onno from ssp.user where \
                dept_id=(select dept_id from ssp.user where onno=%s limit 1)) \
            order by id desc'
        )
        params = [onno]
        if onno in [59796417, 59797017, 59795330]:  # * roby,mike可以看到DES跨部门的资产
            sql1 = (
                "select 资产编号,资产名称,资产型号,资产机号,资产规格,配件,资产类别 as 类别,厂商,购入日期,\
                保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
                from ssp_asset.资产清单 where 工号 in \
                (select onno from ssp.user where dept_id in %s) order by id desc"
            )

            sql2 = "select 资产编号,资产名称,资产型号,资产机号,资产规格,\
                '' as 配件,使用类别 as 类别,厂商,购入日期,\
                保管人,部门,工号,部门代码,仪校编号,备注,海关监管,放置地点,使用状态,区域 \
                from ssp_asset.内部清单 where 工号 in \
                (select onno from ssp.user where dept_id in %s) order by id desc"
            if onno == 59797017:
                params = [[22, 28]]
            else:
                params = [[21, 22, 28]]

        df1 = read_sql(sql1, params=params)
        df1["来源"] = "资产清单"
        df2 = read_sql(sql2, params=params)
        df2["来源"] = "内部清单"
        df = pd.concat([df1, df2])
        df["购入日期"] = pd.to_datetime(df["购入日期"], errors="coerce").dt.date

    elif t == "self-buy":
        sql = (
            "select 状态,申请人,请购名称,请购型号,请购规格,请购数量,填单日期,预计交期,实际到货日,验收日期 \
            from ssp_asset.请购清单 where 申请人 like %s"
        )
        params = [f"{onno}%"]
        df = read_sql(sql, params=params)
        if not df.empty:
            df["填单日期"] = df["填单日期"].dt.date
            df["预计交期"] = df["预计交期"].dt.date
            df["实际到货日"] = df["实际到货日"].dt.date
            df["验收日期"] = df["验收日期"].dt.date

    elif t == "dept-buy":
        sql = (
            "select 状态,申请人,请购名称,请购型号,请购规格,请购数量,填单日期,预计交期,实际到货日,验收日期 \
            from ssp_asset.请购清单 where LEFT(申请人,8) in \
            (select onno from ssp.user where \
                dept_id=(select dept_id from ssp.user where onno=%s limit 1)) "
        )
        params = [onno]

        if onno in [59796417, 59797017, 59795330]:
            sql = (
                "select 状态,申请人,请购名称,请购型号,请购规格,请购数量,填单日期,预计交期,实际到货日,验收日期 \
                from ssp_asset.请购清单 where LEFT(申请人,8) in \
                (select onno from ssp.user where dept_id in %s) "
            )
            if onno == 59797017:
                params = [[22, 28]]
            else:
                params = [[21, 22, 28]]

        df = read_sql(sql, params=params)
        if not df.empty:
            df["填单日期"] = df["填单日期"].dt.date
            df["预计交期"] = df["预计交期"].dt.date
            df["实际到货日"] = df["实际到货日"].dt.date
            df["验收日期"] = df["验收日期"].dt.date

    elif t == "self-processing":
        sql = (
            'select id,状态,"维修" as 类型,资产编号,资产名称,资产型号,资产机号,问题描述 as 原因, \
            处理日期 as 条件 from ssp_asset.维修清单 where 报修人=\
            (select name from ssp.user where onno=%s limit 1)'
        )
        params = [onno]
        df1 = read_sql(sql, params=params)

        sql = (
            'select id,状态,"转移" as 类型,资产编号,资产名称,资产型号,"" as 资产机号,移转原因 as 原因, \
            PR单号 as 条件 from ssp_asset.转移清单 where 原保管人工号=\
            (select onno from ssp.user where onno=%s limit 1)'
        )
        params = [onno]
        df2 = read_sql(sql, params=params)

        sql = (
            'select id,状态,"报废" as 类型,资产编号,资产名称,资产型号,"" as 资产机号,报废原因 as 原因, \
            PR单号 as 条件 from ssp_asset.报废清单 where 工号=\
            (select onno from ssp.user where onno=%s limit 1)'
        )
        params = [onno]
        df3 = read_sql(sql, params=params)

        df = pd.concat([df1, df2, df3])
        df["状态"] = df["状态"].astype("category")
        df["状态"] = df["状态"].cat.set_categories(
            ["处理中", "在外维修", "已结束", "取消"]
        )
        df = df.sort_values(by=["状态"])

    columns = [
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "width": "1px",
            "hozAlign": "center",
        }
    ]
    col = [{"title": i, "field": i} for i in df.columns]
    for i in col:
        if i["field"] in ("区域", "来源", "id", "工号", "使用状态"):
            i.update({"visible": False})
        elif i["field"] in (
            "资产编号",
            "资产名称",
            "资产型号",
            "资产机号",
            "资产规格",
            "厂商",
            "请购名称",
            "请购型号",
            "请购规格",
        ):
            i.update({"headerFilter": "input", "width": 90})
        else:
            i.update({"headerFilter": "input"})

    columns.extend(col)
    data = df.to_dict(orient="records")
    return data, columns


@callback(
    Output("asset-store", "data"),
    Output("uploader-row", "style"),
    Output("modal-asset", "is_open"),
    Input("btn-asset-transfer", "n_clicks_timestamp"),
    Input("btn-asset-repair", "n_clicks_timestamp"),
    Input("btn-asset-scrap", "n_clicks_timestamp"),
    Input("btn-asset-cancel", "n_clicks_timestamp"),
    Input("btn-asset-label", "n_clicks_timestamp"),
    State("user", "data"),
    State("asset-table", "multiRowsClicked"),
)
def asset_action(n1, n2, n3, n4, n5, user, rows):
    d = (n1, n2, n3, n4, n5)
    if max(d) == 0:
        raise PreventUpdate
    if not rows:
        return None, {"display": "none"}, True

    idx = d.index(max(d))
    if idx == 0:
        df = pd.DataFrame(rows)
        columns = [
            "资产编号",
            "资产名称",
            "资产型号",
            "资产机号",
            "保管人",
            "部门",
            "报修人",
            "类别",
            "厂商",
            "资讯报修单号",
            "问题描述",
            "区域",
            "来源",
        ]
        df = df.reindex(columns=columns)
        df["报修人"] = user.get("name")
        columns = [{"title": i, "field": i} for i in columns]
        for i in columns:
            if i["field"] in ("问题描述", "资讯报修单号"):
                i.update({"editor": "input"})
            if i["field"] in (
                "保管人",
                "部门",
                "报修人",
                "区域",
                "来源",
                "厂商",
                "类别",
            ):
                i.update({"visible": False})
        data = df.to_dict(orient="records")
        return {"data": data, "columns": columns}, {"display": "none"}, True

    elif idx == 1:
        df = pd.DataFrame(rows)
        columns = [
            "资产编号",
            "资产名称",
            "资产型号",
            "保管人",
            "工号",
            "部门代码",
            "移转原因",
            "新保管人",
            "新部门代码",
            "新放置地点",
            "放置地点",
            "使用状态",
            "区域",
            "来源",
        ]
        df = df.reindex(columns=columns)
        columns = [{"title": i, "field": i} for i in columns]

        sql = "select 放置地点 from ssp_asset.放置地点"
        df1 = read_sql(sql)
        address = df1["放置地点"].tolist()

        sql = "select nt_name,name from ssp.user"
        df2 = read_sql(sql)
        users = df2["nt_name"].dropna().str.title().tolist()

        sql = "select distinct 部门代码 from ssp_asset.资产清单"
        df3 = read_sql(sql)
        dept_code = df3["部门代码"].dropna().tolist()
        # conn.close()

        for i in columns:
            if i["field"] == "新保管人":
                i.update(
                    {
                        "editor": "autocomplete",
                        "editorParams": {
                            "values": users,
                            "emptyPlaceholder": "输入NT账号",
                            "showListOnEmpty": True,
                        },
                    }
                )
            elif i["field"] in (
                "保管人",
                "工号",
                "部门代码",
                "使用状态",
                "放置地点",
                "区域",
                "来源",
            ):
                i.update({"visible": False})
            elif i["field"] == "移转原因":
                i.update(
                    {
                        "editor": "select",
                        "editorParams": [
                            "离职",
                            "保管人变更",
                            "部门代码变更",
                            "保管地址变更",
                        ],
                    }
                )
            elif i["field"] == "新放置地点":
                i.update({"editor": "select", "editorParams": address})
            elif i["field"] == "新部门代码":  # * autocomplete不能有空记录
                i.update(
                    {
                        "editor": "autocomplete",
                        "editorParams": {
                            "values": dept_code,
                            "emptyPlaceholder": "输入部门代码",
                            "showListOnEmpty": True,
                            "freetext": True,
                        },
                    }
                )
        data = df.to_dict(orient="records")
        return {"data": data, "columns": columns}, {"display": "none"}, True

    elif idx == 2:
        df = pd.DataFrame(rows)
        columns = [
            "资产编号",
            "资产名称",
            "资产型号",
            "保管人",
            "工号",
            "部门",
            "部门代码",
            "类别",
            "上级主管",
            "主管是否同意",
            "资讯报修单号",
            "报废原因",
            "区域",
            "来源",
        ]
        df = df.reindex(columns=columns)
        columns = [{"title": i, "field": i} for i in columns]

        sql = "select nt_name,name from ssp.user"
        df2 = read_sql(sql)
        users = df2["nt_name"].str.title().tolist()

        for i in columns:
            if i["field"] in ("报废原因", "资讯报修单号"):
                i.update({"editor": "input"})
            elif i["field"] == "上级主管":
                i.update(
                    {
                        "editor": "autocomplete",
                        "editorParams": {
                            "values": users,
                            "emptyPlaceholder": "输入NT账号",
                            "showListOnEmpty": True,
                        },
                    }
                )
            elif i["field"] == "主管是否同意":
                i.update({"editor": "select", "editorParams": ["是", "否"]})
            elif i["field"] in (
                "保管人",
                "工号",
                "部门代码",
                "部门",
                "区域",
                "来源",
                "类别",
            ):
                i.update({"visible": False})
        data = df.to_dict(orient="records")
        return {"data": data, "columns": columns}, {"display": "block"}, True

    elif idx == 3:
        df = pd.DataFrame(rows)
        columns = [{"title": i, "field": i} for i in df.columns]
        data = df.to_dict(orient="records")
        return {"data": data, "columns": columns}, {"display": "none"}, True

    elif idx == 4:
        df = pd.DataFrame(rows)
        df = df.reindex(columns=["保管人", "部门", "资产名称", "资产编号", "资产机号"])
        columns = [{"title": i, "field": i} for i in df.columns]
        data = df.to_dict(orient="records")
        return {"data": data, "columns": columns}, {"display": "none"}, True
    else:
        raise PreventUpdate


@callback(
    Output("modal-asset-table", "data"),
    Output("modal-asset-table", "columns"),
    Input("asset-store", "data"),
)
def modal_asset_body(data):
    if not data:
        return [], []
    return data["data"], data["columns"]


@callback(
    Output("modal-asset-table", "data"),
    Input("modal-asset-table", "cellEdited"),
    State("modal-asset-table", "data"),
)
def modal_asset_table_cellEdited(t, data):
    if t is None:
        raise PreventUpdate

    if t["column"] == "移转原因":
        df = pd.DataFrame(data)

        onno = t["row"]["工号"]
        sql = "select nt_name from ssp.user where onno=%s"
        user = read_sql(sql, params=[onno])

        nt_name = user["nt_name"].iloc[0].title()
        c1 = df["移转原因"] == "保管地址变更"
        df["新保管人"] = np.where(c1, nt_name, df["新保管人"])
        df["新部门代码"] = np.where(c1, df["部门代码"], df["新部门代码"])
        c2 = df["移转原因"] == "部门代码变更"
        df["新保管人"] = np.where(c2, nt_name, df["新保管人"])
        df["新放置地点"] = np.where(c2, df["放置地点"], df["新放置地点"])
        return df.to_dict(orient="records")
    else:
        raise PreventUpdate


@callback(
    Output("modal-asset-alert", "is_open"),
    Output("modal-asset-alert", "children"),
    Output("modal-asset-alert", "color"),
    Output("modal-asset-submit", "disabled"),
    Input("modal-asset-submit", "n_clicks"),
    State("modal-asset-table", "data"),
    State("asset-scrap-uploader", "upload_id"),
    State("user", "data"),
)
def modal_asset_submit(n, data, upload_id, user):
    if not n:
        raise PreventUpdate
    df = pd.DataFrame(data)
    col = df.columns
    nt_name = user.get("nt_name")
    area = user.get("area")
    dept_id = user.get("dept_id")

    mailto = f"{nt_name}@deltaww.com;<EMAIL>;<EMAIL>"
    if area == "HZ":
        mailto = f"{nt_name}@deltaww.com;<EMAIL>"

    elif area == "WH":
        mailto = f"{nt_name}@deltaww.com;<EMAIL>;<EMAIL>;<EMAIL>"

    elif area == "WH":
        df["区域"] = "WH"
        if dept_id == 1:
            mailto = f"{nt_name}@deltaww.com;<EMAIL>"
        elif dept_id == 21:
            mailto = f"{nt_name}@deltaww.com;<EMAIL>"

    if "报修人" in col:
        mailto = f"{nt_name}@deltaww.com;<EMAIL>;<EMAIL>"
        if df["问题描述"].fillna("").isin([""]).any():
            return True, "请完整填写问题描述", "warning", False

        if (
            ((df["类别"] == "计算机设备") & (df["资讯报修单号"].fillna("") == ""))
            .any()
            .any()
        ):
            return True, "计算机设备,资讯报修单号不能为空", "warning", False

        body = df.fillna("").to_html()
        df = df.drop(["类别", "资讯报修单号"], axis=1)
        df = df.rename(columns={"厂商": "维修厂商"})
        df["创建日期"] = datetime.now()
        df["状态"] = "处理中"

        with pool.connection() as conn:
            with conn.cursor() as cu:
                params = df.values.tolist()
                fields = ",".join(df.columns)
                ph = ",".join(["%s"] * df.columns.size)
                sql = f"insert into ssp_asset.维修清单({fields}) values({ph})"
                cu.executemany(sql, params)
                conn.commit()

        subject = f"【资产维修申请】:{nt_name}"
        bg_mail(mailto, subject, body)
        return True, "维修申请提交成功", "success", True

    elif "移转原因" in col:
        if (
            df[["移转原因", "新保管人", "新部门代码", "新放置地点"]]
            .fillna("")
            .isin([""])
            .any()
            .any()
        ):
            return (
                True,
                "请完整填写移转原因,新保管人,新部门代码,新放置地点",
                "warning",
                False,
            )

        params = df["新保管人"].unique().tolist()
        ph = ",".join(["%s"] * len(params))
        sql = (
            f"select nt_name,onno as 现保管人工号,dept as 现保管部门,name as 现保管人 \
            from user where nt_name in ({ph})"
        )
        user = read_sql(sql, params=params)
        df["新保管人"] = df["新保管人"].str.lower()
        user["nt_name"] = user["nt_name"].str.lower()
        df = df.merge(user, left_on="新保管人", right_on="nt_name", how="left")
        new_owner = ",".join((df["新保管人"] + "@deltaww.com").unique())
        mailto = mailto + ";" + new_owner

        columns = [
            "资产编号",
            "资产名称",
            "资产型号",
            "保管人",
            "工号",
            "部门代码",
            "现保管人",
            "现保管人工号",
            "现保管部门",
            "新部门代码",
            "移转原因",
            "新放置地点",
            "使用状态",
            "区域",
            "来源",
        ]
        df = df.reindex(columns=columns)

        df = df.rename(
            columns={
                "保管人": "原保管人",
                "工号": "原保管人工号",
                "部门代码": "原部门代码",
                "新部门代码": "现部门代码",
                "新放置地点": "放置地点",
            }
        )

        df["创建日期"] = datetime.now()
        df["状态"] = "处理中"

        with pool.connection() as conn:
            with conn.cursor() as cu:
                params = df.values.tolist()
                fields = ",".join(df.columns)
                ph = ",".join(["%s"] * df.columns.size)
                sql = f"insert into ssp_asset.转移清单({fields}) values({ph})"
                cu.executemany(sql, params)
                conn.commit()

        subject = f"【资产转移申请】:{nt_name}"
        body = df.fillna("").to_html()
        bg_mail(mailto, subject, body)
        return True, "转移申请提交成功", "success", True

    elif "报废原因" in col:
        mailto = f"{nt_name}@deltaww.com;<EMAIL>;<EMAIL>;<EMAIL>"
        if (
            df[["报废原因", "上级主管", "主管是否同意"]]
            .fillna("")
            .isin([""])
            .any()
            .any()
        ):
            return (
                True,
                "请完整填写报废原因,上级主管,主管是否同意",
                "warning",
                False,
            )

        if (
            ((df["类别"] == "计算机设备") & (df["资讯报修单号"].fillna("") == ""))
            .any()
            .any()
        ):
            return True, "计算机设备,资讯报修单号不能为空", "warning", False

        if not (UPLOAD_FOLDER_ROOT / upload_id).exists():
            return True, "报废申请,须上传报废设备的照片", "warning", False

        body = df.fillna("").to_html()
        df = df.drop(["上级主管", "主管是否同意", "类别", "资讯报修单号"], axis=1)
        df["创建日期"] = datetime.now()
        df["状态"] = "处理中"

        with pool.connection() as conn:
            with conn.cursor() as cu:
                params = df.values.tolist()
                fields = ",".join(df.columns)
                ph = ",".join(["%s"] * df.columns.size)
                sql = f"insert into ssp_asset.报废清单({fields}) values({ph})"
                cu.executemany(sql, params)
                conn.commit()

        subject = f"【资产报废申请】:{nt_name}"
        file_list = list((UPLOAD_FOLDER_ROOT / upload_id).glob("*.*"))
        bg_mail(mailto, subject, body, attach=file_list)
        return True, "报废申请提交成功", "success", True

    elif "条件" in col:
        if not (df["条件"].fillna("") == "").all():
            return True, "满足条件为空的记录才能取消", "warning", False

        with pool.connection() as conn:
            with conn.cursor() as cu:
                for i in df.itertuples():
                    sql = f"delete from ssp_asset.{i.类型}清单 where id=%s"
                    cu.execute(sql, [i.id])
                conn.commit()

        subject = f"【资产取消处理申请】:{nt_name}"
        body = df.fillna("").to_html()
        bg_mail(mailto, subject, body)
        return True, "取消申请提交成功", "success", True

    elif "保管人" in col:
        df = df.reindex(columns=["保管人", "部门", "资产名称", "资产编号", "资产机号"])
        df = df.rename(
            columns={
                "保管人": "owner",
                "部门": "dept",
                "资产名称": "asset_name",
                "资产编号": "asset_number",
                "资产机号": "serial_number",
            }
        )
        df["label_template"] = "asset"
        df["area"] = area
        bg_label_print(df.to_json(orient="records"))
        return True, "打印标签提交成功", "success", True
    else:
        raise PreventUpdate


@callback(
    Output("modal-asset", "is_open"),
    Input("modal-asset-close", "n_clicks"),
    # group='grp-asset-1'
)
def modal_asset_close(n):
    if n is None:
        raise PreventUpdate
    return False


@callback(
    Output("asset-scrap-uploader", "upload_id"),
    Input("btn-asset-scrap", "n_clicks"),
    State("user", "data"),
)
def create_upload_id(n, user):
    nt_name = user.get("nt_name")
    now = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"asset_scrap_{nt_name}_{now}"


@callback(
    Output("self-processing", "n_clicks_timestamp"),
    Input("modal-asset-close", "n_clicks_timestamp"),
)
def active_self_processing(n):
    return n


@callback(
    Output("modal-asset-submit", "disabled"),
    Input("modal-asset-close", "n_clicks"),
    # group='grp-asset-1'
)
def enable_asset_submit(n):
    return False
