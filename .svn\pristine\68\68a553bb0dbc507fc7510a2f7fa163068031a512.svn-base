# -*- coding: utf-8 -*-
import dash_ag_grid as dag
import feffery_antd_components.alias as fac
import pandas as pd
from dash import no_update, Patch, dcc
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback

from common import id_factory, read_sql
from config import UPLOAD_FOLDER_ROOT
from tasks import bg_label_print
from datetime import datetime
import dash_mantine_components as dmc
from utils import db

id = id_factory(__name__)


def stock_picking():
    table = dag.AgGrid(
        id=id("stock-picking-table"),
        className="ag-theme-quartz",
        columnSize="sizeToFit",
        columnDefs=[
            {
                "field": "picking_qty",
                "headerName": "领用数量",
                "editable": True,
                "cellEditor": {"function": "NumberInput"},
                "pinned": "left",
            },
            {"field": "qty", "headerName": "库存数量"},
            {"field": "checkcode", "headerName": "系列料号"},
            {"field": "deltapn", "headerName": "料号"},
            {"field": "stockno", "headerName": "库位号"},
            {"field": "des", "headerName": "描述"},
            {"field": "mfgname", "headerName": "厂商"},
            {"field": "mfgpn", "headerName": "厂商料号"},
            {"field": "area", "headerName": "区域"},
            {"field": "limituse", "headerName": "专用"},
        ],
        rowData=[],
        dashGridOptions={
            "rowSelection": "multiple",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
        },
        style={"height": "450px"},
    )
    div = fac.Flex(
        [
            fac.Flex(
                [
                    fac.RadioGroup(
                        options=[
                            {"label": "本地", "value": "local"},
                            {"label": "全域", "value": "all"},
                        ],
                        id=id("picking-area"),
                        value="local",
                    ),
                    fac.Input(
                        addonBefore="模糊搜索",
                        debounceWait=1000,
                        id=id("stock-picking-filter"),
                        style={"width": "300px"},
                    ),
                    fac.Popover(
                        fac.Upload(
                            apiUrl="/upload/",
                            buttonContent="上传批量领用",
                            # buttonProps={"danger": True},
                            id=id("picking-upload"),
                            showUploadList=False,
                            showSuccessMessage=False,
                            fileTypes=["xlsx"],
                        ),
                        title="excel文件格式",
                        content=fac.Image(src="/assets/img/picking.jpg"),
                    ),
                    fac.RadioGroup(
                        options=[
                            {"label": "给项目", "value": "prt"},
                            {"label": "给工程师", "value": "user"},
                        ],
                        id=id("picking-type"),
                        value="user",
                    ),
                    fac.Select(
                        # options=["全部", "专用"],
                        style={"width": "200px"},
                        id=id("picking-options"),
                        # mode="tags",
                    ),
                    fac.Button("提交", id=id("picking-submit"), type="primary"),
                ],
                gap="middle",
                align="center",
                justify="space-between",
                # noWrap=True,
            ),
            dcc.Loading(table),
            # fuc.ListenPaste(id=id("paste"), enableListenPaste=True),
        ],
        vertical=True,
        gap="small",
    )
    return dmc.Container(div, fluid=True)


def layout(user, **kwargs):
    return stock_picking()


@callback(
    Output(id("stock-picking-table"), "dashGridOptions"),
    Input(id("stock-picking-filter"), "value"),
)
def picking_filter(filter_value):
    patch_grid_options = Patch()
    patch_grid_options["quickFilterText"] = filter_value
    return patch_grid_options


@callback(
    Output(id("stock-picking-table"), "rowData"),
    Input(id("picking-area"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def picking_area(value, user):
    if value == "local":
        area = user.get("area")
        sql = "select id,deltapn,checkcode,stockno,des,mfgname,mfgpn,qty,area,limituse \
            from ssp.stock where area=%s"
        df = read_sql(sql, params=[area])
    else:
        area = user.get("area")
        sql = "select id,deltapn,checkcode,stockno,des,mfgname,mfgpn,qty,area,limituse \
            from ssp.stock"
        df = read_sql(sql)

    return df.to_dict("records")


@callback(
    Output(id("picking-options"), "options"),
    Output(id("picking-options"), "value"),
    Input(id("picking-type"), "value"),
    prevent_initial_call=False,
)
def picking_type(type):
    if type == "user":
        sql = "select distinct nt_name from ssp.user"
        res = db.execute(sql)
        options = [i["nt_name"].title() for i in res]
        return options, None
    else:
        sql = "select distinct prtno from ssp.prt"
        res = db.execute(sql)
        options = [i["prtno"] for i in res]
        return options, None


@callback(
    Output("msg", "children"),
    Output(id("stock-picking-table"), "rowData"),
    Input(id("picking-upload"), "lastUploadTaskRecord"),
    State("user", "data"),
)
def picking_upload(record, user):
    if not record:
        raise PreventUpdate

    file = UPLOAD_FOLDER_ROOT / record["taskId"] / record["fileName"]
    df = pd.read_excel(file, dtype=str, keep_default_na=False)
    df.columns = df.columns.str.lower()
    if not ({"checkcode", "qty"}.issubset(df.columns)):
        return fac.Message(
            content="文件必须栏位(checkcode,qty)", type="error"
        ), no_update
    df = df[["checkcode", "qty"]].rename(columns={"qty": "picking_qty"})
    area = user.get("area")
    sql = "select * from ssp.stock where area=%s and checkcode in %s"
    params = (area, df["checkcode"].to_list())
    stock = read_sql(sql, params=params)
    stock.columns = stock.columns.str.lower()
    df = df.merge(stock, on="checkcode", how="left")
    return fac.Message(content="上传成功", type="success"), df.to_dict("records")


@callback(
    Output("msg", "children"),
    Output(id("stock-picking-table"), "rowData"),
    Input(id("picking-submit"), "nClicks"),
    State(id("stock-picking-table"), "rowData"),
    State(id("picking-options"), "value"),
    State(id("picking-type"), "value"),
    State("user", "data"),
)
def picking_submit(nclicks, data, options, type, user):
    if not nclicks:
        raise PreventUpdate

    if not options:
        return fac.Message(content="请选择用途", type="error"), no_update

    df = pd.DataFrame(data)
    if "picking_qty" not in df.columns:
        return fac.Message(content="请输入领料数量", type="error"), no_update

    dfx = df.loc[df["picking_qty"].isna()]
    df = df.loc[df["picking_qty"].notna()]

    if df.empty:
        return fac.Message(content="请输入领料数量", type="error"), no_update
    df["picking_qty"] = df["picking_qty"].astype(int)
    df["qty"] = df["qty"].fillna(0).astype(int)
    if (df["picking_qty"] > df["qty"]).any():
        return fac.Message(
            content="领料数量不能大于库存,请修改", type="error"
        ), no_update

    if type == "user":
        res = db.find_one("ssp.user", {"nt_name": options})
    else:
        res = db.find_one("ssp.prt", {"prtno": options})

    if not res:
        raise PreventUpdate

    now = datetime.now()
    nt_name = user.get("nt_name")

    df["prtno"] = options
    df["qty"] = df["picking_qty"]
    df["stockoutdate"] = now
    df["owner1"] = nt_name
    df["type"] = "Debug"
    df["dept"] = res["dept"]
    df["owner2"] = nt_name
    df["lable"] = now
    df["source"] = "picking"
    df["stockoutdate2"] = now
    df["qty"] = df["picking_qty"]
    df["dept_id"] = res["dept_id"]

    sql = "update stock set qty=qty-%s where id=%s"
    params = df[["qty", "id"]].values.tolist()
    db.execute_many(sql, params)

    sql = "insert into stockout \
        (deltapn,checkcode,stock_id,qty,stockoutdate,prtno,type,dept,\
        stockno,area,owner1,owner2,lable,source,stockoutdate2,dept_id) \
        values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    params = df[
        [
            "deltapn",
            "checkcode",
            "id",
            "qty",
            "stockoutdate",
            "prtno",
            "type",
            "dept",
            "stockno",
            "area",
            "owner1",
            "owner2",
            "lable",
            "source",
            "stockoutdate2",
            "dept_id",
        ]
    ].values.tolist()
    db.execute_many(sql, params)

    df["prtno"] = df["prtno"] + f'|{res["dept"]}'
    df["label"] = (
        df["checkcode"].astype(str)
        + "{"
        + df["picking_qty"].astype(str)
        + "{"
        + df["owner1"].astype(str)
    )
    df["label_template"] = "picking"
    bg_label_print(df.to_json(orient="records"))

    return fac.Message(content="领料提交成功", type="success"), dfx.to_dict("records")
