import pandas as pd
from pocketflow import Flow, Node

from .utils import call_llm

prompt = """
你是一名SMD生产计划排程专家，负责为SMD产线安排生产计划。您的目标是充分利用工作时间和设备资源，生成一个最优化的生产计划方案。

项目清单如下:

注:贴片用时单位是小时

请严格遵循以下原则和约束进行排产：

计划开始时间：
    根据项目清单中的最早可开始日期，尽早开始排产

计划完成时间：
    计划开始时间确定后，计划完成时间=计划开始时间+贴片用时

设备资源：
    共有3台贴片机：MY300, BM221, W2。
    不同设备独立安排项目，合理分配项目，确保设备利用率最大化

设备特性：
    MY300： 适合生产点数少的项目
    BM221： 适合生产点数多的项目
    W2： 适合生产点数多的项目

工作时间：
    每日工作时间为早上9:00至下午16:30。
    午休时间（11:30至13:30） 不安排生产

换线时间(项目切换时间)：
    同一台设备上，不同项目之间必须预留40分钟的间隔时间

当所有项目的计划开始时间无法满足最晚需开始日期时，可以利用中午时间11:30-13:30排产，下午16:30-16:00也可以排产
如果仍然无法满足，告知哪些项目无法安排
        """


class PlanNode(Node):
    def prep(self, shared):
        return shared["prompt"]

    def exec(self, prompt):
        response = call_llm(prompt)
        return response

    def post(self, shared, prep_res, exec_res):
        shared["result"] = exec_res


class CheckNode(Node):
    def prep(self, shared):
        return shared["result"], shared["df"]

    def exec(self, prep_res):
        result, df = prep_res
        schedule = result.schedule
        df1 = pd.json_normalize([i.model_dump() for i in schedule])
        df = df.merge(df1, left_on="项目名称", right_on="project", how="left")
        set1 = set(df["项目名称"])
        set2 = set(df1["project"])
        return set1, set2

    def post(self, shared, prep_res, exec_res):
        set1, set2 = exec_res
        if set1 - set2:
            return "error"


def smd_plan(shared):
    plan_node = PlanNode()
    # check_node = CheckNode()
    # plan_node >> check_node
    # check_node - "error" >> plan_node
    flow = Flow(start=plan_node)
    flow.run(shared)
    return shared
