# -*- coding: utf-8 -*-
"""
SQLAlchemy 数据库工具模块
统一使用 SQLAlchemy 连接池进行数据库操作
"""
import logging
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from sqlalchemy import text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


class SQLAlchemyDB:
    """SQLAlchemy 数据库操作类"""
    
    def __init__(self, engine: Engine):
        self.engine = engine
    
    @contextmanager
    def get_connection(self, autocommit: bool = False):
        """
        获取数据库连接的上下文管理器
        
        Args:
            autocommit: 是否自动提交事务
        """
        conn = self.engine.connect()
        try:
            if not autocommit:
                trans = conn.begin()
                try:
                    yield conn
                    trans.commit()
                except Exception:
                    trans.rollback()
                    raise
            else:
                yield conn
        finally:
            conn.close()
    
    def execute_query(self, sql: str, params: Optional[Union[Dict, List, Tuple]] = None) -> List[Dict]:
        """
        执行查询语句并返回结果
        
        Args:
            sql: SQL 查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        try:
            with self.get_connection(autocommit=True) as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                
                # 将结果转换为字典列表，键名转为小写
                columns = result.keys()
                rows = result.fetchall()
                return [
                    {col.lower(): val for col, val in zip(columns, row)}
                    for row in rows
                ]
        except SQLAlchemyError as e:
            logger.error(f"SQL execute failed: {e}, SQL: {sql}, Params: {params}")
            raise e
    
    def execute_update(self, sql: str, params: Optional[Union[Dict, List, Tuple]] = None) -> int:
        """
        执行更新语句（INSERT, UPDATE, DELETE）
        
        Args:
            sql: SQL 语句
            params: 参数
            
        Returns:
            影响的行数
        """
        try:
            with self.get_connection() as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                return result.rowcount
        except SQLAlchemyError as e:
            logger.error(f"SQL execute failed: {e}, SQL: {sql}, Params: {params}")
            raise e
    
    def execute_insert(self, sql: str, params: Optional[Union[Dict, List, Tuple]] = None) -> int:
        """
        执行插入语句并返回插入的 ID
        
        Args:
            sql: INSERT SQL 语句
            params: 参数
            
        Returns:
            插入记录的 ID
        """
        try:
            with self.get_connection() as conn:
                if params:
                    result = conn.execute(text(sql), params)
                else:
                    result = conn.execute(text(sql))
                return result.lastrowid
        except SQLAlchemyError as e:
            logger.error(f"SQL execute failed: {e}, SQL: {sql}, Params: {params}")
            raise e
    
    def execute_many(self, sql: str, params_list: List[Union[Dict, List, Tuple]]) -> int:
        """
        批量执行 SQL 语句
        
        Args:
            sql: SQL 语句
            params_list: 参数列表
            
        Returns:
            影响的行数
        """
        try:
            with self.get_connection() as conn:
                result = conn.execute(text(sql), params_list)
                return result.rowcount
        except SQLAlchemyError as e:
            logger.error(f"SQL executemany failed: {e}, SQL: {sql}")
            raise e
    
    def read_sql(self, sql: str, params: Optional[Union[Dict, List, Tuple]] = None) -> pd.DataFrame:
        """
        执行查询并返回 pandas DataFrame
        
        Args:
            sql: SQL 查询语句
            params: 查询参数
            
        Returns:
            查询结果的 DataFrame
        """
        try:
            with self.get_connection(autocommit=True) as conn:
                if params and isinstance(params, list):
                    params = tuple(params)
                df = pd.read_sql(sql, conn, params=params)
                return df
        except SQLAlchemyError as e:
            logger.error(f"SQL read failed: {e}, SQL: {sql}, Params: {params}")
            raise e
    
    def insert_dict(self, table: str, data: Dict[str, Any]) -> int:
        """
        插入字典数据到表中
        
        Args:
            table: 表名
            data: 要插入的数据字典
            
        Returns:
            插入记录的 ID
        """
        data = data.copy()
        data.pop("id", None)  # 移除 id 字段
        
        fields = ",".join(data.keys())
        placeholders = ",".join([f":{key}" for key in data.keys()])
        sql = f"INSERT INTO {table}({fields}) VALUES({placeholders})"
        
        return self.execute_insert(sql, data)
    
    def update_dict(self, table: str, data: Dict[str, Any]) -> int:
        """
        根据 ID 更新表中的数据
        
        Args:
            table: 表名
            data: 包含 id 和要更新字段的数据字典
            
        Returns:
            影响的行数
        """
        data = data.copy()
        record_id = data.pop("id", None)
        if record_id is None:
            raise ValueError("Data must contain 'id' field for update")
        
        fields = ",".join(f"{key}=:{key}" for key in data.keys())
        sql = f"UPDATE {table} SET {fields} WHERE id=:id"
        data["id"] = record_id
        
        return self.execute_update(sql, data)
    
    def delete_by_filters(self, table: str, filters: Dict[str, Any]) -> int:
        """
        根据条件删除记录
        
        Args:
            table: 表名
            filters: 删除条件字典
            
        Returns:
            删除的行数
        """
        where_clause = " AND ".join(f"{key}=:{key}" for key in filters.keys())
        sql = f"DELETE FROM {table} WHERE {where_clause}"
        
        return self.execute_update(sql, filters)
    
    def get_table_columns(self, table: str) -> List[str]:
        """
        获取表的列名
        
        Args:
            table: 表名，可以是 schema.table 格式
            
        Returns:
            列名列表
        """
        if "." in table:
            schema, table_name = table.split(".")
        else:
            schema = "ssp"
            table_name = table
        
        sql = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE TABLE_SCHEMA=:schema AND TABLE_NAME=:table_name
        """
        
        result = self.execute_query(sql, {"schema": schema, "table_name": table_name})
        return [row["column_name"] for row in result]


# 创建全局数据库操作实例
def create_db_instance(engine: Engine) -> SQLAlchemyDB:
    """创建数据库操作实例"""
    return SQLAlchemyDB(engine)
