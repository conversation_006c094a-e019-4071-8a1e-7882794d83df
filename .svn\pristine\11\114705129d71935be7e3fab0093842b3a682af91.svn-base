# -*- coding: utf-8 -*-
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    no_update,
    dash,
)
from dash_extensions.javascript import Namespace
from . import layout
from db._ssp_ext import Dummyload, db, DummyloadHistory
import feffery_antd_components as fac
import pandas as pd
import numpy as np
from sqlalchemy import select
from tasks import bg_mail
from config import cfg

id = layout.id

layout = layout.layout
dash.register_page(__name__, path="/dummyload", title="DummyLoad")
ns = Namespace("myNamespace", "tabulator")

col0 = ["purpose", "project", "ee", "me", "pm", "application_date", "owner"]

cola = col0 + ["use_floor", "expected_end_date"]
colb = col0 + ["expected_return_date", "destination"]
colc = col0 + ["reason_giving"]
cold = col0 + ["apply_return_date", "reason_delay"]
cole = col0 + [
    "use_floor",
    "expected_end_date",
    "expected_return_date",
    "destination",
    "apply_return_date",
    "reason_delay",
]

cols = {
    "自用": cola,
    "外寄": colb,
    "赠予": colc,
    "延期": cold,
    "归还": cole,
}


def notice(message: str = "提交成功", type: str = "success"):
    notification = fac.AntdNotification(
        message=message,
        type=type,
        placement="bottomRight",
    )
    return notification


@callback(
    Output(id("tab1_table"), "data"),
    Output(id("tab2_table"), "data"),
    Input(id("tabs"), "activeKey"),
    prevent_initial_call=False,
)
def active_tabs(active_key):
    if active_key == "1":
        with db.Session() as ses:
            sql = Dummyload.select()
            data = [x.to_dict() for x in ses.scalars(sql)]
        return data, no_update
    elif active_key == "2":
        with db.Session() as ses:
            sql = Dummyload.select()
            data = [x.to_dict() for x in ses.scalars(sql)]
        return no_update, data
    else:
        raise PreventUpdate


@callback(
    Output(id("notice"), "children"),
    Input(id("tab1_submit_btn"), "n_clicks"),
    State(id("tab1_table"), "multiRowsClicked"),
    State(id("tab1_table"), "dataFiltered"),
    State(id("tab1_table"), "columns"),
    State("user", "data"),
)
def tab1_submit(n_clicks, data, data_filtered, columns, user):
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return True, notice("请勾选记录", "error")

    df = pd.DataFrame(data)
    if "action" not in df.columns:
        return True, notice("请选择ACTION", "error")

    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    df["purpose"] = df["action"]
    df = df.query("purpose.notna()")

    nt_name = user.get("nt_name").title()
    df["owner"] = nt_name

    def required_field(df: pd.DataFrame, cols: list):
        dfx = df.reindex(columns=cols).fillna("").replace("Invalid date", "")
        rf = (dfx == "").any()
        if rf.any():
            x = [i.get("title") for i in columns if i.get("field") in rf[rf].index]
            return x

    def update_db(df: pd.DataFrame, cols: list):
        with db.Session() as ses:
            for i in df.itertuples():
                dl = ses.get(Dummyload, i.id)
                for col in cols:
                    setattr(dl, col, getattr(i, col))
            ses.commit()

    dfa = df.query("action=='自用'")
    if not dfa.empty:
        rf = required_field(dfa, cola)
        if rf:
            return notice(f"请输入栏位信息{rf}", "error")
        else:
            if (
                dfa["expected_end_date"].apply(
                    lambda x: pd.date_range(pd.Timestamp.now(), x).size
                )
                > 90
            ).any():
                return notice("预计结束日期不能超过90天", "error")
            dfa["state"] = "已预约"
            update_db(dfa, cola)

    dfb = df.query("action=='外寄'")
    if not dfb.empty:
        rf = required_field(dfb, colb)
        if rf:
            return notice(f"请输入栏位信息{rf}", "error")
        else:
            if (
                dfa["expected_return_date"].apply(
                    lambda x: pd.date_range(pd.Timestamp.now(), x).size
                )
                > 90
            ).any():
                return notice("预计返回日期不能超过90天", "error")
            dfb["state"] = "已预约"
            update_db(dfb, colb)

    dfc = df.query("action=='赠予'")
    if not dfc.empty:
        rf = required_field(dfc, colc)
        if rf:
            return notice(f"请输入栏位信息{rf}", "error")
        else:
            dfc["state"] = "已赠予"
            update_db(dfc, colc)

    dfd = df.query("action=='延期'")
    if not dfd.empty:
        rf = required_field(dfd, cold)
        if rf:
            return notice(f"请输入栏位信息{rf}", "error")
        else:
            if (
                dfa["apply_return_date"].apply(
                    lambda x: pd.date_range(pd.Timestamp.now(), x).size
                )
                > 90
            ).any():
                return notice("申请延期归还时间不能超过90天", "error")
            dfd["state"] = "已预约"
            update_db(dfd, cold)

    dfe = df.query("action=='归还'")
    if not dfe.empty:
        update_db(dfe, cole)

    # =========添加历史记录========
    df["dummyload_id"] = df["id"]
    with db.Session() as ses:
        for item in df.itertuples():
            data = item._asdict()
            data.pop("id")
            data.pop("Index")
            history = DummyloadHistory(**data)
            ses.add(history)
        ses.commit()

    # =========通知管理员邮件========
    to = [nt_name] + cfg.dummy_admin
    to = ";".join(f"{i}@deltaww.com" for i in to)
    subject = "申请DummyLoad,请及时处理"
    body = "尊敬的SELENE.WANG 先生/小姐:<br>您好!<br>有申请DummyLoad的表单,请及时处理,谢谢！"
    bg_mail(to, subject, body)

    return notice()


@callback(
    Output(id("tabs"), "activeKey"),
    Input(id("notice"), "children"),
    State(id("tabs"), "activeKey"),
)
def refresh_tab1(notice, active_key):
    if not isinstance(notice, dict):
        raise PreventUpdate

    if notice.get("props").get("type") == "success":
        return active_key
    else:
        raise PreventUpdate


@callback(
    Output(id("notice"), "children"),
    Input(id("tab2_submit_btn"), "n_clicks"),
    State(id("tab2_table"), "multiRowsClicked"),
    State(id("tab2_table"), "dataFiltered"),
    State(id("tab2_table"), "columns"),
    State("user", "data"),
)
def tab2_submit(n_clicks, data, data_filtered, columns, user):
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请勾选记录", "error")

    df = pd.DataFrame(data)
    if "action" not in df.columns:
        return notice("请选择ACTION", "error")

    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    df = df.query("action.notna()")

    dfi = df.query("action=='同意'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                dl = ses.get(Dummyload, i.id)
                dl.purpose = None

                if i.purpose == "赠予":
                    dl.state = "已赠予"

                elif i.purpose == "外寄":
                    dl.state = "已预约"
                    to = f"{i.me}@deltaww.com"
                    subject = f"组装打包DummyLoad,序列号{i.equipment_pn}"
                    body = f"尊敬的{i.me} 先生/小姐:<br>您好!<br>DummyLoad-{i.equipment_pn},请及时组装打包,谢谢！"
                    bg_mail(to, subject, body)

                elif i.purpose == "归还":
                    for col in cole:
                        setattr(dl, col, None)
                    dl.state = "空闲"
                else:
                    dl.state = "已预约"
            ses.commit()

    dfi = df.query("action=='拒绝'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                dl = ses.get(Dummyload, i.id)
                if i.purpose in ("赠予", "延期", "归还"):
                    dl.purpose = None
                else:
                    colx = cols.get(i.purpose)
                    for col in colx:
                        setattr(dl, col, None)

                to = f"{i.owner}@deltaww.com"
                subject = f"你的DummyLoad申请已被拒绝"
                body = f"尊敬的{i.me} 先生/小姐:<br>您好!<br>你的DummyLoad申请已被拒绝,请主动联系管理员,谢谢！"
                bg_mail(to, subject, body)
            ses.commit()

    dfi = df.query("action=='更新'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                dl = ses.get(Dummyload, i.id)
                colx = dfi.columns.difference(["id", "action", "purpose"])
                for col in colx:
                    setattr(dl, col, getattr(i, col))
            ses.commit()

    dfi = df.query("action=='删除'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                dl = ses.get(Dummyload, i.id)
                ses.delete(dl)
            ses.commit()

    dfi = df.query("action=='添加'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                data = i._asdict()
                data.pop("Index")
                data.pop("id")
                data.pop("action")
                dummyload = Dummyload(**data)
                ses.add(dummyload)
            ses.commit()

    dfi = df.query("action=='转移'")
    if not dfi.empty:
        with db.Session() as ses:
            for i in dfi.itertuples():
                dl = ses.get(Dummyload, i.id)
                dl.owner = i.owner

                to = f"{i.owner}@deltaww.com"
                subject = f"DummyLoad转移通知"
                body = f"尊敬的{i.owner} 先生/小姐:<br>您好!<br>DummyLoad-{i.equipment_pn},已转移到您名下,请注意查看,谢谢！"
                bg_mail(to, subject, body)
            ses.commit()

    # =========添加历史记录========
    df["dummyload_id"] = df["id"]
    with db.Session() as ses:
        for item in df.itertuples():
            data = item._asdict()
            data.pop("id")
            data.pop("Index")
            history = DummyloadHistory(**data)
            ses.add(history)
        ses.commit()

    return notice()


@callback(
    Output(id("tab2_table"), "data"),
    Input(id("add_row"), "n_clicks"),
    State(id("tab2_table"), "data"),
)
def tab2_table_add_row(n_clicks, data):
    """插入行"""
    if n_clicks:
        if not data:
            row = {"id": 1, "action": "添加"}
        else:
            row = {"id": max(i["id"] for i in data) + 1, "action": "添加"}

        data.insert(0, row)
        return data
    else:
        raise PreventUpdate


@callback(
    Output(id("search_dropdown"), "options"),
    Input(id("search_dropdown"), "id"),
    prevent_initial_call=False,
)
def search_dropdown(id):
    with db.Session() as ses:
        sql = select(Dummyload.id, Dummyload.equipment_pn).distinct()
        options = [{"label": i.equipment_pn, "value": i.id} for i in ses.execute(sql)]
        return options


@callback(
    Output(id("tab3_table"), "data"),
    Input(id("search_dropdown"), "value"),
)
def dropdown_value(value):
    if not value:
        return [{}]
    with db.Session() as ses:
        sql = select(DummyloadHistory).where(DummyloadHistory.dummyload_id.in_(value))
        df = pd.DataFrame(i.to_dict() for i in ses.scalars(sql))
        df["action"] = np.where(df["action"] == df["purpose"], None, df["action"])
        c1 = df["action"] != df["purpose"]
        c2 = df["action"].notna()
        df["purpose"] = np.where(c1 & c2, None, df["purpose"])
        return df.to_dict(orient="records")
