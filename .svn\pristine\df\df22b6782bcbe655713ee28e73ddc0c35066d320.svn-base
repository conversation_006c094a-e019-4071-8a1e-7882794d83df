# -*- coding: utf-8 -*-
from datetime import datetime, timedelta

import dash_ag_grid as dag
import dash_mantine_components as dmc
import feffery_antd_charts as fact
import pandas as pd
from dash import Patch, dcc, html
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    no_update,
)

from common import get_nt_name, id_factory, read_sql
from components import create_sidebar, notice
from config import cfg, pool
from utils import db

id = id_factory(__name__)

bom_type_dict = {"EE": "b_ee", "ME": "b_me", "MAG": "b_mag"}
menu_items = [
    {
        "key": "0",
        "title": "BOM",
        "label": "BOM",
        "icon": "material-symbols:home",
        "href": "/bom?page=home",
        "page": "home",
        "font-weight": "bolder",
        "color": "rgb(0, 159, 232)",
    },
    {
        "key": "1",
        "title": "未处理",
        "label": "未处理",
        "icon": "mdi:account-details",
        "href": "/bom?page=open&status=open",
        "page": "open",
        "status": "open",
        "count": 0,
    },
    {
        "key": "2",
        "title": "在途",
        "label": "在途",
        "icon": "mdi:account-edit",
        "href": "/bom?page=ongoing&status=ongoing",
        "page": "ongoing",
        "status": "ongoing",
        "count": 0,
    },
    {
        "key": "3",
        "title": "已处理",
        "label": "已处理",
        "icon": "mdi:account-eye",
        "href": "/bom?page=close&status=close",
        "page": "close",
        "status": "close",
        "count": 0,
    },
    {
        "key": "4",
        "title": "职责表",
        "label": "职责表",
        "icon": "mdi:account-eye",
        "href": "/bom?page=duty",
        "page": "duty",
    },
]


def page_task_open():
    table = dag.AgGrid(
        id=id("open-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "id", "headerName": "id", "width": 80, "hide": True},
            {
                "field": "prtno",
                "headerName": "项目号",
                "width": 135,
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "pinned": "left",
            },
            {"field": "dept", "headerName": "部门", "width": 100},
            {"field": "pm", "headerName": "PM", "width": 100},
            {"field": "ee", "headerName": "EE", "width": 100},
            {"field": "me", "headerName": "ME", "width": 100},
            {"field": "mag", "headerName": "MAG", "width": 100},
            {"field": "proj", "headerName": "机种名", "width": 100},
            {"field": "qty", "headerName": "数量", "width": 70},
            {
                "field": "pcbstatus",
                "headerName": "PCB",
                "width": 100,
                "cellStyle": {
                    "styleConditions": [
                        {
                            "condition": "!['需采购','库存板',null].includes(params.data.pcbstatus)",
                            "style": {"color": "red"},
                        }
                    ]
                },
            },
            {
                "field": "b_ee",
                "headerName": "EE",
                "editable": True,
                "width": 70,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["Y", "NA"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
            {
                "field": "b_me",
                "headerName": "ME",
                "editable": True,
                "width": 90,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["Y", "NA"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
            {
                "field": "b_mag",
                "headerName": "MAG",
                "editable": True,
                "width": 90,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["Y", "NA"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
            {"field": "change_count", "headerName": "变更数", "width": 100},
            {
                "field": "bom_owner",
                "headerName": "处理人",
                "editable": True,
                "width": 120,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.bom_owner,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
        ],
        rowData=[],
        # columnSize="sizeToFit",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        getRowStyle={
            "styleConditions": [
                {
                    "condition": "bomOpenHightLight(params)",
                    # "condition": "params.data.b_ee=='Y'",
                    "style": {"backgroundColor": "#D8E6FD"},
                }
            ]
        },
        style={"height": "80vh"},
    )
    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Indicator(
                        dmc.Tab("BOM", value="bom", color="red"),
                        label="10",
                        size=15,
                        position="top-end",
                        offset=5,
                        processing=True,
                        id=id("open1-indicator"),
                        color="red",
                        disabled=True,
                    ),
                    dmc.Indicator(
                        dmc.Tab("ChangeList", value="change", color="blue"),
                        label="10",
                        size=15,
                        position="top-end",
                        offset=5,
                        processing=True,
                        id=id("open2-indicator"),
                        color="red",
                        disabled=True,
                    ),
                    dmc.SegmentedControl(
                        id=id("open-owner"),
                        value="own",
                        data=[
                            {"value": "own", "label": "自己的"},
                            {"value": "all", "label": "所有的"},
                        ],
                        color="blue",
                        size="xs",
                        ml="auto",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dmc.LoadingOverlay(table),
        ],
        color="red",
        value="bom",
        id=id("open-tabs"),
    )

    return layout


def page_task_ongoing():
    table = dag.AgGrid(
        id=id("ongoing-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "prtno",
                "headerName": "项目号",
                "width": 135,
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "pinned": "left",
            },
            {"field": "dept", "headerName": "部门", "width": 80},
            {"field": "pm", "headerName": "PM", "width": 80},
            {"field": "ee", "headerName": "EE", "width": 80},
            {"field": "me", "headerName": "ME", "width": 80},
            {"field": "mag", "headerName": "MAG", "width": 80},
            {"field": "proj", "headerName": "机种名", "width": 100},
            {"field": "qty", "headerName": "数量", "width": 70},
            {"field": "bomtype", "headerName": "类型", "width": 80},
            {"field": "designno_qty", "headerName": "位置数", "width": 80},
            {"field": "problem_qty", "headerName": "问题数", "width": 80},
            {"field": "previous_date", "headerName": "上阶段完成日期", "width": 170},
            {
                "field": "owner1",
                "headerName": "处理人",
                "width": 100,
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.bom_owner,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "editable": True,
            },
            {
                "field": "action",
                "headerName": "料表重置",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["料表重置"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 80,
                "editable": True,
            },
        ],
        rowData=[{}],
        # columnSize="autoSize",
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
        style={"height": "80vh"},
    )
    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Indicator(
                        dmc.Tab("料表检查", value="1", color="red"),
                        label="10",
                        size=15,
                        position="top-end",
                        offset=5,
                        processing=True,
                        id=id("ongoing1-indicator"),
                        color="red",
                        disabled=True,
                    ),
                    dmc.Indicator(
                        dmc.Tab("缺料确认", value="3", color="blue"),
                        label="10",
                        size=15,
                        position="top-end",
                        offset=5,
                        processing=True,
                        id=id("ongoing3-indicator"),
                        color="red",
                        disabled=True,
                    ),
                    dmc.Indicator(
                        dmc.Tab("料表扣库", value="4", color="green"),
                        label="10",
                        size=15,
                        position="top-end",
                        offset=5,
                        processing=True,
                        id=id("ongoing4-indicator"),
                        color="red",
                        disabled=True,
                    ),
                    dmc.SegmentedControl(
                        id=id("ongoing-owner"),
                        value="own",
                        data=[
                            {"value": "own", "label": "自己的"},
                            {"value": "all", "label": "所有的"},
                        ],
                        color="blue",
                        size="xs",
                        ml="auto",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dmc.LoadingOverlay(table),
        ],
        color="red",
        value="1",
        id=id("ongoing-tabs"),
    )

    return layout


def page_task_close():
    table = dag.AgGrid(
        id=id("close-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {
                "field": "prtno",
                "headerName": "项目号",
                "width": 125,
                "cellRenderer": "markdown",
                "linkTarget": "_blank",
                "pinned": "left",
            },
            {"field": "smstatus", "headerName": "状态", "width": 90},
            {"field": "bom_owner", "headerName": "BOM处理人", "width": 110},
            {"field": "dept", "headerName": "部门", "width": 100},
            {"field": "pm", "headerName": "PM", "width": 100},
            {"field": "ee", "headerName": "EE", "width": 100},
            {"field": "me", "headerName": "ME", "width": 100},
            {"field": "mag", "headerName": "MAG", "width": 100},
            {"field": "proj", "headerName": "机种名", "width": 100},
            {"field": "qty", "headerName": "数量", "width": 70},
            {"field": "designno_qty", "headerName": "位置数", "width": 70},
            {"field": "bom_qty", "headerName": "BOM数", "width": 90},
            {"field": "change_qty", "headerName": "Change数", "width": 90},
            # {"field": "receive_date", "headerName": "接收日期", "width": 90},
            # {"field": "finish_date", "headerName": "完成日期", "width": 90},
        ],
        # rowData=[{}],
        # columnSize="autoSize",
        # enableEnterpriseModules=True,
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            # "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
            "rowHeight": 35,
            "enableCellTextSelection": True,
            "ensureDomOrder": True,
        },
    )

    layout = dmc.Tabs(
        [
            dmc.TabsList(
                [
                    dmc.Tab(
                        "已处理",
                        rightSection=dmc.Badge(
                            p=0,
                            variant="filled",
                            sx={"width": 30, "height": 16, "pointerEvents": "none"},
                            id=id("close-badge"),
                        ),
                        value="1",
                    ),
                ]
            ),
            dmc.Space(h=10),
            dmc.DateRangePicker(
                style={"width": 200},
                id=id("close-date-range"),
                maxDate=datetime.now(),
                inputFormat="YYYY-MM-DD",
                placeholder="日期区间",
                # dropdownPosition="flip",
                value=[
                    datetime.now().date() - timedelta(days=180),
                    datetime.now().date() + timedelta(days=1),
                ],
                clearable=False,
                # ml="auto",
            ),
            table,
            dmc.Button("Download", id=id("download-btn"), size="xs"),
            dcc.Download(id=id("download-data")),
        ],
        color="red",
        value="1",
        id="tabs",
    )

    return layout


def bom_duty():
    df = read_sql("select * from bom_duty")
    # df1 = read_sql("select concat_ws('_',dept_group,dept_name) as depts from ssp.dept")
    # depts = df1["depts"].tolist()

    # df2 = read_sql(
    #     "select nt_name from ssp.user where role_group='pur' and termdate is null"
    # )
    # pur = df2["nt_name"].str.title().tolist()

    table = dag.AgGrid(
        id=id("duty-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"headerName": "ID", "field": "id", "hide": True},
            {
                "field": "action",
                "headerName": "ACTION",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": ["update", "delete", "add"],
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
                "singleClickEdit": True,
                "width": 130,
            },
            {
                "field": "dept",
                "headerName": "部门",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.depts,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
            {
                "field": "bom_owner",
                "headerName": "负责人",
                "cellEditor": {"function": "DMC_Select"},
                "cellEditorParams": {
                    "options": cfg.bom_owner,
                    "clearable": True,
                    "shadow": "xl",
                },
                "cellEditorPopup": True,
            },
        ],
        columnSize="sizeToFit",
        rowData=df.to_dict(orient="records"),
        defaultColDef={
            "editable": True,
            "resizable": True,
            "sortable": True,
            "filter": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
    )
    layout = dmc.Stack(
        [
            table,
            dmc.Button("提交", id=id("duty-submit")),
        ]
    )
    return layout


def query_stock():
    df = read_sql("select * from stock")
    df.columns = df.columns.str.lower()
    table = dag.AgGrid(
        id=id("stock-table"),
        className="ag-theme-quartz",
        columnDefs=[
            {"field": "deltapn", "headerName": "DeltaPN", "width": 80},
            {"field": "checkcode", "headerName": "CheckCode", "width": 80},
            {"field": "stockno", "headerName": "StockNo", "width": 80},
            {"field": "area", "headerName": "区域", "width": 80},
            {"field": "qty", "headerName": "数量", "width": 80},
        ],
        columnSize="sizeToFit",
        rowData=df.to_dict(orient="records"),
        defaultColDef={
            "resizable": True,
            "sortable": True,
            "filter": True,
            "editable": True,
        },
        dashGridOptions={
            "rowSelection": "single",
            "stopEditingWhenCellsLoseFocus": True,
            "singleClickEdit": True,
        },
    )
    layout = dmc.Stack(
        [
            dmc.TextInput(id=id("filter"), label="搜索"),
            table,
            # dmc.Button("提交", id=id("project-modify-submit")),
        ]
    )
    return layout


def query_smbom():
    layout = dmc.Stack(
        [
            dmc.Group(
                [
                    dmc.TextInput(
                        label="输入项目号",
                        id=id("prtno"),
                        description="仅用于扣库报错时,删除SMBOM,然后后重新扣库",
                    ),
                    dmc.Button("查询", id=id("smbom-btn-query")),
                    dmc.Button("删除", id=id("smbom-btn-delete"), color="red"),
                ],
                align="flex-end",
            ),
            html.Div(id=id("smbom-content")),
        ]
    )
    return layout


# @cache.cached(timeout=600)
def homepage():
    now = pd.Timestamp.now()
    year = now.year
    week = now.week
    sql = "select date(finish_date) as date,owner1,dept from ssp.bom_record a \
        left join ssp.prt b on a.prt_id=b.id \
        where year(a.finish_date)>=%s and a.source='bom'"
    df = read_sql(sql, params=[year - 1])
    if df.empty:
        return
    df["owner1"] = df["owner1"].str.title()
    df["date"] = pd.to_datetime(df["date"])
    df["week"] = df["date"].dt.isocalendar().week
    df["year"] = df["date"].dt.year
    df["date"] = df["date"].dt.strftime("%m/%d")
    df["qty"] = 1

    df1 = df.groupby(["year", "week"], as_index=False).count()
    df1["week"] = df1["week"].astype(str)
    df1["year"] = df1["year"].astype(str)
    c1 = df["week"] == week
    c2 = df["year"] == year
    dfw = df[c1 & c2]
    df2 = (
        dfw.groupby("owner1", as_index=False)
        .count()
        .sort_values("qty", ascending=False)
    )
    df3 = (
        dfw.groupby("dept", as_index=False).count().sort_values("qty", ascending=False)
    )

    graph1 = fact.AntdLine(
        data=df1.to_dict(orient="records"),
        xField="week",
        yField="qty",
        seriesField="year",
        # isStack=True,
        # smooth=True,
        color=["#F58E8E", "#22C55E"],
        label={
            "position": "center",
            # "formatter": {"func": "({ loading }) => `${(loading * 100).toFixed(0)}%`"},
        },
        height=200,
        # animation=True,
        # seriesField="owner1",
        # isStack=True,
    )
    graph2 = fact.AntdBar(
        data=df2.to_dict(orient="records"),
        xField="qty",
        yField="owner1",
        # color="#f0884d",
        # yAxis={"grid": None},
        # xAxis={"grid": None},
        # style=style(backgroundColor="white", height="100%", width="100%", padding=10),
        label={"position": "left"},
        height=250,
        # groupField="spec",
        # isPercent=True,
        # padding=10,
        # meta={"日期": {"type": "cat"}},
        # label={"position": "top"},
        # yAxis={"max": 100000},
    )
    graph3 = fact.AntdPie(
        data=df3.to_dict(orient="records"),
        colorField="dept",
        angleField="qty",
        height=250,
        label={"type": "inner", "content": "{percentage}"},
        innerRadius=0.5,
    )
    graph = dmc.Stack(
        [
            dmc.Text("周BOM数量走势"),
            graph1,
            dmc.Group(
                [
                    dmc.Stack([dmc.Text(f"{year}年-第{week}周-处理人分布"), graph2]),
                    dmc.Stack([dmc.Text(f"{year}年-第{week}周-部门分布"), graph3]),
                ],
                grow=True,
            ),
        ],
        spacing=10,
    )
    return graph


def layout(user, page=None, **kwargs):
    if page is None:
        content = homepage()
    elif page == "open":
        content = page_task_open()
    elif page == "ongoing":
        content = page_task_ongoing()
    elif page == "close":
        content = page_task_close()
    elif page == "duty":
        content = bom_duty()
    elif page == "stock":
        content = query_stock()
    elif page == "smbom":
        content = query_smbom()
    else:
        content = homepage()

    nt_name = user.get("nt_name")
    sql = "select count(*) as count from prt \
        where bom_owner=%s \
        and (b_ee=%s or b_me=%s or b_mag=%s) \
        and prtbomreceive in %s and smstatus!=%s"
    params = [nt_name, "Y", "Y", "Y", ["Y", "X"], "cancel"]
    data = db.execute_fetchone(sql, params)
    menu_items[1]["count"] = data.get("count")

    sql = "select count(*) as count from ssp.bom_record where status=%s and owner1=%s"
    params = ("processing", nt_name)
    data = db.execute_fetchone(sql, params)
    menu_items[2]["count"] = data.get("count")

    sidebar = create_sidebar(page, menu_items)
    appshell = dmc.AppShell(
        content,
        navbar=sidebar,
        style={"position": "fixed"},
    )
    return appshell


@callback(
    Output("global-notice", "children"),
    Output(id("open-tabs"), "value"),
    Input(id("open-table"), "cellValueChanged"),
    State(id("open-tabs"), "value"),
)
def update_open_owner(cell_changed, tab_value):
    """未处理转单"""
    if not cell_changed:
        raise PreventUpdate

    cell_changed = cell_changed[0]
    field = cell_changed.get("colId")

    if field not in ("bom_owner", "b_ee", "b_me", "b_mag"):
        raise PreventUpdate

    id = cell_changed.get("data").get("id")
    value = cell_changed.get("value")
    if not value:
        raise PreventUpdate

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if field == "bom_owner":
                sql = "update ssp.prt set bom_owner=%s where id=%s"
                cu.execute(sql, [value, id])
                sql = "update ssp.bom_record set owner1=%s where prt_id=%s"
                cu.execute(sql, [value, id])
                conn.commit()
                return notice(f"转单{value}成功"), tab_value
            else:
                old_value = cell_changed.get("oldValue")
                if (old_value == "Y" and value == "NA") or (
                    old_value == "NA" and value == "Y"
                ):
                    sql = f"update ssp.prt set {field}=%s where id=%s"
                    cu.execute(sql, [value, id])
                    conn.commit()
                    return notice(f"修改BOM状态{value}成功"), tab_value
                else:
                    return notice("当前状态禁止修改", "error"), tab_value


@callback(
    Output("global-notice", "children"),
    Output(id("ongoing-table"), "rowData"),
    Input(id("ongoing-table"), "cellValueChanged"),
)
def update_ongoing_owner(cell_changed):
    """在途转单"""
    if not cell_changed:
        raise PreventUpdate

    cell_changed = cell_changed[0]
    field = cell_changed.get("colId")

    if field not in ("owner1", "action"):
        raise PreventUpdate

    id = cell_changed.get("data").get("id")
    value = cell_changed.get("value")

    if not value:
        raise PreventUpdate

    table_data = Patch()
    row_idx = cell_changed.get("rowIndex")
    del table_data[row_idx]

    with pool.connection() as conn:
        with conn.cursor() as cu:
            if field == "owner1":
                sql = f"update ssp.bom_record set {field}=%s where id=%s"
                cu.execute(sql, [value, id])

            elif field == "action":
                owner1 = cell_changed.get("data").get("owner1")
                nt_name = get_nt_name()
                if nt_name.lower() != owner1.lower():
                    return notice("非本人名下不可重置", "error"), no_update

                bom_type = cell_changed.get("data").get("bomtype", "").split(",")
                bom_type = {
                    bom_type_dict.get(i): "Y" for i in bom_type if bom_type_dict.get(i)
                }
                prt_id = cell_changed.get("data").get("prt_id")
                if bom_type:
                    field = ",".join(f"{i}='{j}'" for i, j in bom_type.items())
                    sql = f"update ssp.prt set {field} where id=%s"
                    cu.execute(sql, [prt_id])

                cu.execute("delete from ssp.bom_record where id=%s", [id])
                cu.execute("delete from ssp.bom_initial where bom_id=%s", [id])
                cu.execute("delete from ssp.bom_shortage where bom_id=%s", [id])
                sql = "update ssp.stock a JOIN \
                    (SELECT stock_id,qty FROM stockout WHERE bom_id=%s)b \
                    ON a.id=b.stock_id SET a.qty=a.qty+b.qty"
                cu.execute(sql, [id])
                cu.execute("delete from ssp.stockout where bom_id=%s", [id])
                cu.execute("delete from ssp.pur where bom_id=%s", [id])
                cu.execute("delete from ssp.smbom where bom_id=%s", [id])
            conn.commit()

    return notice(f"{value}成功"), table_data


@callback(
    Output(id("duty-table"), "rowData"),
    Input(id("duty-table"), "cellValueChanged"),
    State(id("duty-table"), "rowData"),
)
def duty_table_add_row(changed, data):
    if not changed:
        raise PreventUpdate

    changed = changed[0]
    if (changed.get("colId") == "action") and (changed.get("value") == "add"):
        idx = changed.get("rowIndex")
        data[idx].update({"action": ""})
        data.insert(0, {"action": "add"})
        return data
    else:
        raise PreventUpdate


@callback(
    Output("global-notice", "children"),
    Output(id("duty-table"), "rowData"),
    Input(id("duty-submit"), "n_clicks"),
    State(id("duty-table"), "rowData"),
    State(id("duty-table"), "columnState"),
)
def duty_submit(n_clicks, data, columns):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    columns = [column.get("colId") for column in columns]
    df = df.reindex(columns=columns)

    add = df.query("action=='add'").drop(["id", "action"], axis=1)
    if not add.empty:
        data = add.to_dict(orient="records")
        for item in data:
            db.insert("ssp.bom_duty", item)

    update = df.query("action=='update'").drop("action", axis=1)
    if not update.empty:
        data = update.to_dict(orient="records")
        for item in data:
            db.update("ssp.bom_duty", item)

    delete = df.query("action=='delete'").drop("action", axis=1)
    if not delete.empty:
        data = delete[["id"]].to_dict(orient="records")
        for item in data:
            db.delete("ssp.bom_duty", item)
    dfx = read_sql("select * from bom_duty")
    return notice(), dfx.to_dict(orient="records")


@callback(
    Output(id("open-table"), "rowData"),
    Output(id("open-table"), "columnDefs"),
    Input(id("open-tabs"), "value"),
    Input(id("open-owner"), "value"),
    prevent_initial_call=False,
)
def open_tabs_switch(value, owner):
    """未处理"""
    column_defs = Patch()
    if value == "bom":
        column_defs[14]["cellEditorParams"].update({"disabled": False})
        if owner == "own":
            sql = "select id,prtno,dept,pm,ee,me,mag,proj,qty,b_ee,b_me,b_mag,\
                bom_owner,pcbstatus from prt where \
                    bom_owner=%s \
                    and (b_ee=%s or b_me=%s or b_mag=%s) \
                    and prtbomreceive in %s and smstatus!=%s"
            params = [get_nt_name(), "Y", "Y", "Y", ["Y", "X"], "cancel"]
        else:
            sql = "select id,prtno,dept,pm,ee,me,mag,proj,qty,b_ee,b_me,b_mag,\
                bom_owner,pcbstatus from prt where \
                    bom_owner is not null \
                    and (b_ee=%s or b_me=%s or b_mag=%s) \
                    and prtbomreceive in %s and smstatus!=%s"
            params = ["Y", "Y", "Y", ["Y", "X"], "cancel"]
    else:
        column_defs[14]["cellEditorParams"].update({"disabled": True})
        if owner == "own":
            sql = "select id,prtno,dept,pm,ee,me,mag,proj,qty,b_ee,b_me,b_mag,\
                bom_owner, pcbstatus from ssp.prt \
                where bom_owner=%s \
                and (b_ee =%s or b_me =%s or b_mag =%s) \
                and (b_ee !=%s and b_me !=%s and b_mag !=%s) \
                and prtbomreceive in %s and smstatus not in %s"
            params = [
                get_nt_name(),
                "X",
                "X",
                "X",
                "O",
                "O",
                "O",
                ["Y", "X"],
                ["cancel", "close"],
            ]
        else:
            sql = "select id,prtno,dept,pm,ee,me,mag,proj,qty,b_ee,b_me,b_mag,\
                bom_owner, pcbstatus from ssp.prt \
                where bom_owner is not null \
                and (b_ee =%s or b_me =%s or b_mag =%s) \
                and (b_ee !=%s and b_me !=%s and b_mag !=%s) \
                and prtbomreceive in %s and smstatus not in %s"
            params = ["X", "X", "X", "O", "O", "O", ["Y", "X"], ["cancel", "close"]]

    df = read_sql(sql, params=params)

    if df.empty:
        return [], column_defs

    if value == "change":  # * 不显示有change在途的项目
        sql = "select prt_id from ssp.bom_record \
            where prt_id in %s and source=%s and status=%s"
        params = [df["id"].tolist(), "change", "processing"]
        df1 = read_sql(sql, params=params)
        if not df1.empty:
            df = df.loc[~df["id"].isin(df1["prt_id"])]

    prt_id = df["id"].tolist()
    if prt_id:
        sql = "SELECT prt_id as id,COUNT(*) AS change_count \
            FROM ssp.bom_record  \
                WHERE prt_id IN %s AND SOURCE=%s GROUP BY prt_id"
        df1 = read_sql(sql, params=[prt_id, "change"])
        df = df.merge(df1, on="id", how="left")

    df.columns = df.columns.str.lower()
    df = df.loc[:, ~df.columns.duplicated()]
    df["prtno"] = df.apply(
        lambda x: f"**[{x['prtno']}](/bom/open?prt_id={x['id']}&prtno={x['prtno']}&type={value})**",
        axis=1,
    )

    return df.to_dict(orient="records"), column_defs


@callback(
    Output(id("open1-indicator"), "label"),
    Output(id("open2-indicator"), "label"),
    Output(id("open1-indicator"), "disabled"),
    Output(id("open2-indicator"), "disabled"),
    Input(id("open-table"), "rowData"),
    State(id("open-tabs"), "value"),
)
def open_indicator(data, tabs):
    """显示未处理数量"""
    l1 = 0
    l2 = 0
    if tabs == "bom":
        l1 = len(data)
    else:
        l2 = len(data)
    d1 = l1 == 0
    d2 = l2 == 0
    return l1, l2, d1, d2


@callback(
    Output(id("ongoing-table"), "rowData"),
    Input(id("ongoing-tabs"), "value"),
    Input(id("ongoing-owner"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def ongoing_tabs_switch(value, owner, user):
    """在途"""
    if owner == "own":
        sql = "select * from bom_record a \
            left join (select id as prtid,dept,pm,ee,me,mag,proj,qty from prt)b \
                on a.prt_id=b.prtid \
                where owner1=%s and status=%s and processingmode=%s"
        params = (user.get("nt_name"), "processing", value)
    else:
        sql = "select * from bom_record a \
            left join (select id as prtid,dept,pm,ee,me,mag,proj,qty from prt)b \
                on a.prt_id=b.prtid \
                where status=%s and processingmode=%s"
        params = ("processing", value)
    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()

    if value == "1":
        df["previous_date"] = df["update_date"]
    elif value == "2":
        df["previous_date"] = df["check_date"]
    elif value == "3":
        df["previous_date"] = df["gmt_update"]
    elif value == "4":
        df["previous_date"] = df["confirmed_date"]

    if df.empty:
        return []

    df.columns = df.columns.str.lower()
    df["prtno"] = df.apply(
        lambda x: f"**[{x['prtno']}](/bom/ongoing?bom_id={x['id']}&step={x['processingmode']}&prtno={x['prtno']}&prt_id={x['prt_id']}&source={x['source']})**",
        axis=1,
    )

    return df.to_dict(orient="records")


@callback(
    Output(id("ongoing1-indicator"), "label"),
    Output(id("ongoing3-indicator"), "label"),
    Output(id("ongoing4-indicator"), "label"),
    Output(id("ongoing1-indicator"), "disabled"),
    Output(id("ongoing3-indicator"), "disabled"),
    Output(id("ongoing4-indicator"), "disabled"),
    Input(id("ongoing-owner"), "value"),
    State("user", "data"),
    prevent_initial_call=False,
)
def ongoing_indicator(owner, user):
    """显示在途数量"""
    if owner == "own":
        sql = "select processingmode from bom_record where owner1=%s and status=%s"
        params = [user.get("nt_name"), "processing"]
    else:
        sql = "select processingmode from bom_record where status=%s"
        params = ["processing"]

    data = db.execute(sql, params)
    l1 = len([i for i in data if i.get("processingmode") == "1"])
    l3 = len([i for i in data if i.get("processingmode") == "3"])
    l4 = len([i for i in data if i.get("processingmode") == "4"])
    d1 = l1 == 0
    d3 = l3 == 0
    d4 = l4 == 0
    return l1, l3, l4, d1, d3, d4


@callback(
    Output(id("ongoing-table"), "rowData"),
    Input(id("ongoing-table"), "cellClicked"),
)
def ongoing_table_cell_clicked(cell_clicked):
    if cell_clicked.get("colId") != "prtno":
        raise PreventUpdate
    table_data = Patch()
    row_idx = cell_clicked.get("rowIndex")
    del table_data[row_idx]
    return table_data


@callback(
    Output(id("close-table"), "rowData"),
    Output(id("close-badge"), "children"),
    Input(id("close-date-range"), "value"),
    prevent_initial_call=False,
)
def task_close_data(date_range):
    """已处理数据"""
    sql = "select * from ssp.prt a left join \
        (SELECT prt_id,sum(SOURCE='bom') as bom_qty, \
        sum(SOURCE='change') as change_qty \
            FROM bom_record GROUP BY prt_id)b \
        on a.id=b.prt_id \
        where (b_ee in %s and b_me in %s and b_mag in %s) \
        and appdate between %s and %s and bom_owner is not null"
    date_range = [f"{date_range[0]} 00:00:00", f"{date_range[-1]} 23:59:59"]
    params = [["X", "NA"], ["X", "NA"], ["X", "NA"]] + date_range
    df = read_sql(sql, params=params)
    df.columns = df.columns.str.lower()
    df["designno_qty"] = df["smd_sum"] + df["dip_sum"]
    df["prtno"] = df.apply(
        lambda x: f"**[{x['prtno']}](/bom/close?prt_id={x['id']}&prtno={x['prtno']})**",
        axis=1,
    )
    return df.to_dict(orient="records"), df.shape[0]


@callback(
    Output(id("stock-table"), "dashGridOptions"),
    Input(id("filter"), "value"),
)
def update_filter(filter_value):
    if not filter_value:
        raise PreventUpdate
    return {"quickFilterText": filter_value}


@callback(
    Output(id("download-data"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State(id("close-table"), "rowData"),
    State("user", "data"),
    State(id("close-table"), "columnState"),
)
def download_close_data(n_clicks, data, user, columns):
    if not n_clicks:
        raise PreventUpdate
    df = pd.DataFrame(data)
    columns = [i.get("colId") for i in columns]
    df = df.reindex(columns=columns)
    nt_name = user.get("nt_name")
    df["prtno"] = df["prtno"].str.slice(3, 14)
    return dcc.send_data_frame(df.to_excel, f"已处理_{nt_name}.xlsx", index=False)
