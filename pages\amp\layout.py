# -*- coding: utf-8 -*-
import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash_extensions.enrich import dcc, html
from dash_extensions.javascript import Namespace
from dash_tabulator import DashTabulator

from common import id_factory

id = id_factory(__name__)

ns = Namespace("myNamespace", "tabulator")


submit_btn = dbc.<PERSON><PERSON>(
    "新增",
    color="danger",
    size="sm",
    style={"width": "70px"},
    id=id("submit_btn"),
    disabled=True,
)
download_btn = dbc.<PERSON><PERSON>(
    "下载", color="success", size="sm", style={"width": "70px"}, id=id("download_btn")
)
update_btn = dbc.<PERSON><PERSON>(
    "更新", color="warning", size="sm", style={"width": "70px"}, id=id("update_btn")
)
delete_btn = dbc.<PERSON><PERSON>(
    "删除", color="secondary", size="sm", style={"width": "70px"}, id=id("delete_btn")
)


add_row = dbc.<PERSON><PERSON>(
    className="fa fa-plus",
    size="sm",
    color="light",
    id=id("add_row"),
)
table = DashTabulator(
    id=id("table"),
    theme="tabulator_site",
    options={
        "layout": "fitDataStretch",
        "height": "500px",
        "selectable": True,
    },
    columns=[
        {
            "formatter": "rowSelection",
            "titleFormatter": "rowSelection",
            "hozAlign": "center",
            "headerSort": False,
        },
        {
            "title": "ID",
            "field": "id",
            "headerFilter": "input",
            "visible": False,
        },
        {
            "title": "序号",
            "field": "seq",
            "headerFilter": "input",
            "editor": "input",
        },
        {
            "title": "Customer",
            "field": "customerer",
            "headerFilter": "input",
            "editor": "input",
        },
        {
            "title": "Model Name",
            "field": "model_name",
            "headerFilter": "input",
            "editor": "textarea",
            "width": 200,
            "formatter": "textarea",
        },
        {
            "title": "Project Name",
            "field": "project_name",
            "headerFilter": "input",
            "editor": "textarea",
            "width": 250,
            "formatter": "textarea",
        },
        {
            "title": "Product Categroy",
            "field": "product_categroy",
            "headerFilter": "input",
            "editor": "input",
        },
        {
            "title": "Milage",
            "field": "milage",
            "headerFilter": "input",
            "editor": "input",
        },
        {
            "title": "Failure Q'ty",
            "field": "failure_qty",
            "headerFilter": "input",
            "editor": "input",
        },
        {
            "title": "Failure Date",
            "field": "failure_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "MFG Date",
            "field": "mfg_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "Failure Description",
            "field": "failure_description",
            "headerFilter": "input",
            "editor": "textarea",
            "width": 300,
            "formatter": "textarea",
        },
        {
            "title": "RC Category",
            "field": "rc_category",
            "editor": "select",
            "editorParams": ["Design", "Material", "Process", "NTF", "CID"],
            "headerFilter": "input",
            "width": 130,
        },
        {
            "title": "RC",
            "field": "rc",
            "editor": "textarea",
            "headerFilter": "input",
            "width": 400,
            "formatter": "textarea",
        },
        {
            "title": "Corrective action",
            "field": "corrective_action",
            "editor": "textarea",
            "headerFilter": "input",
            "width": 300,
            "formatter": "textarea",
        },
        {
            "title": "Implement Date",
            "field": "implement_date",
            "headerFilter": "input",
            "editor": ns("dateEditor"),
            "formatter": "datetime",
            "formatterParams": {
                "inputFormat": "YYYY-MM-DD",
                "outputFormat": "YYYY-MM-DD",
                "invalidPlaceholder": "",
            },
        },
        {
            "title": "Standardization",
            "field": "standardization",
            "headerFilter": "input",
            "editor": "textarea",
            "width": 300,
            "formatter": "textarea",
        },
        {
            "title": "Owner",
            "field": "owner",
            "editor": "input",
            "headerFilter": "input",
        },
    ],
    data=[],
)


amp_download = dcc.Download(id=id("amp_download"))


def layout(**kwargs):
    layout = dbc.Container(
        [
            dmc.Grid(
                [
                    dmc.Col(update_btn, span=1),
                    dmc.Col(submit_btn, span=1),
                    dmc.Col(download_btn, span=1),
                    dmc.Col(delete_btn, span=1),
                ]
            ),
            html.Div(
                [
                    dmc.LoadingOverlay(table),
                    add_row,
                    amp_download,
                ]
            ),
        ],
        fluid=True,
    )
    return layout
