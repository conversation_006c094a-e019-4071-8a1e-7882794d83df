import base64
import io
import dash_mantine_components as dmc
import dash_keyed_file_browser as kfb
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import Input, Output, State, callback, dcc, html
from common import id_factory
from pathlib import Path


def file_browser(path: Path, page_name: str = __name__):
    id = id_factory(page_name)

    if not path.exists():
        try:
            path.mkdir()
        except Exception as e:
            pass

    alert = dmc.Alert(id=id("alert"), hide=True, color="orange")

    upload = dcc.Upload(
        dmc.Button("上传文档", variant="outline"),
        id=id("upload"),
        style={"display": "none"},
    )
    delete = dmc.Button(
        "删除文档",
        variant="outline",
        id=id("delete"),
        color="red",
        style={"display": "none"},
    )

    layout = html.Div(
        [
            kfb.KeyedFileBrowser(
                files=[{"key": "", "size": 0}],
                id=id("kfb"),
                showActionBar=False,
                canFilter=False,
            ),
            dcc.Download(id=id("download")),
            dmc.Group([upload, delete, alert]),
        ],
    )

    @callback(
        Output(id("kfb"), "files"),
        Input(id("alert"), "color"),
        prevent_initial_call=False,
    )
    def create_kfb(color):
        file0 = [
            i
            for i in path.rglob("*")
            if not i.stem.startswith("~") and i.name != "Thumbs.db"
        ]

        file0 = [
            {
                "key": i.relative_to(path).as_posix(),
                "size": i.stat().st_size,
                "modified": i.stat().st_mtime * 1000,
            }
            for i in file0
        ]
        return file0

    @callback(
        Output(id("download"), "data"),
        Input(id("kfb"), "openFile"),
        State("user", "data"),
    )
    def download_file(value, user):
        if value is None:
            raise PreventUpdate

        fn = value.get("key")
        if not fn:
            raise PreventUpdate
        return dcc.send_file(path / fn)

    @callback(
        Output(id("alert"), "hide"),
        Output(id("alert"), "children"),
        Output(id("alert"), "color"),
        Input(id("upload"), "filename"),
        State(id("upload"), "contents"),
    )
    def upload_file(fn, contents):
        if not contents:
            raise PreventUpdate

        if not path.exists():
            path.mkdir()

        content_type, content_string = contents.split(",")
        decoded = base64.b64decode(content_string)
        fio = io.BytesIO(decoded)
        file_path = path / fn
        with open(file_path, "wb") as f:
            f.write(fio.getvalue())
        return False, "上传成功", "green"

    @callback(
        Output(id("alert"), "hide"),
        Output(id("alert"), "children"),
        Output(id("alert"), "color"),
        Input(id("delete"), "n_clicks"),
        State(id("kfb"), "selectedFile"),
    )
    def delete_file(n_clicks, selected_file):
        if not n_clicks:
            raise PreventUpdate

        if not selected_file:
            raise PreventUpdate
        fn = selected_file.get("key")
        (path / fn).unlink()
        return False, "删除成功", "green"

    @callback(
        Output(id("upload"), "style"),
        Output(id("delete"), "style"),
        Input("user", "data"),
    )
    def btn_hidden(user):
        nt_name = user.get("nt_name")
        if nt_name.lower() in ("weiming.li", "jialian.jl.yao"):
            return {"display": "block"}, {"display": "block"}
        else:
            raise PreventUpdate

    return layout
