from datetime import datetime

import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    dash,
    dcc,
    no_update,
)
from dash_extensions.javascript import Namespace

from common import read_sql
from components.notice import notice
from config import cfg, pool

from . import layout

id = layout.id


layout = layout.layout
dash.register_page(__name__, path="/amp/lesson_learn", title="amp_lesson_learn")
ns = Namespace("myNamespace", "tabulator")


@callback(
    Output(id("table"), "data"),
    Output(id("submit_btn"), "disabled"),
    Input(id("add_row"), "n_clicks"),
    State(id("table"), "data"),
    State("user", "data"),
)
def table_add_row(n_clicks, data, user):
    """插入行"""
    nt_name = user.get("nt_name").lower()

    if nt_name not in cfg.amp_lesson_admin:  # 不是管理员
        raise PreventUpdate
    if not n_clicks:
        raise PreventUpdate
    if n_clicks == 1:
        return [{}], False
    data.insert(0, {})

    return data, False


@callback(
    Output(id("table"), "data"),
    Input("user", "data"),
    prevent_initial_call=False,
)
def table_data(user):
    """初始加载数据"""
    sql = "SELECT * FROM ssp_ext.amp_lesson_learn"
    df = read_sql(sql)

    df = df.reindex(
        columns=[
            "id",
            "seq",
            "customerer",
            "model_name",
            "project_name",
            "product_categroy",
            "milage",
            "failure_qty",
            "failure_date",
            "mfg_date",
            "failure_description",
            "rc_category",
            "rc",
            "corrective_action",
            "implement_date",
            "standardization",
            "owner",
        ]
    )

    return df.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("table"), "data"),  # 提交成功 or 失败 页面数据清空与否
    Input(id("submit_btn"), "n_clicks"),
    State(id("table"), "columns"),
    State(id("table"), "data"),
    State("user", "data"),
)
def submit(n_clicks, columns, data, user):
    """插入数据到数据库"""
    nt_name = user.get("nt_name").lower()

    if nt_name not in cfg.amp_lesson_admin:  # 不是管理员
        raise PreventUpdate
    if not n_clicks:
        raise PreventUpdate

    df = pd.DataFrame(data)
    if df.empty:
        return notice("表格内容不能空", "error")

    df = df.reindex(columns=[i.get("field") for i in columns if i.get("field")])
    df = df.dropna(how="all")

    conn = pool.connection()
    cu = conn.cursor()

    sql2 = (
        "insert into ssp_ext.amp_lesson_learn(customerer,model_name,project_name,product_categroy,milage,failure_qty,\
        failure_date,mfg_date,failure_description,rc_category,rc,corrective_action,implement_date,\
        standardization,owner,seq) \
        value(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)"
    )

    df = df.reindex(
        columns=[
            "customerer",
            "model_name",
            "project_name",
            "product_categroy",
            "milage",
            "failure_qty",
            "failure_date",
            "mfg_date",
            "failure_description",
            "rc_category",
            "rc",
            "corrective_action",
            "implement_date",
            "standardization",
            "owner",
            "seq",
        ]
    )
    df = df.replace({"": None, np.nan: None})

    params = df.values.tolist()
    cu.executemany(sql2, params)
    conn.commit()
    cu.close()
    conn.close()
    return notice("提交成功", "success"), [{}]


@callback(
    Output("global-notice", "children"),
    Input(id("update_btn"), "n_clicks"),
    State(id("table"), "multiRowsClicked"),
    State(id("table"), "dataFiltered"),
    State("user", "data"),
)
def tab3_submit(n_clicks, data, data_filtered, user):
    nt_name = user.get("nt_name").lower()

    if nt_name not in cfg.amp_lesson_admin:  # 不是管理员
        raise PreventUpdate

    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需更新的记录", "error")

    df = pd.DataFrame(data)
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    # df = df.fillna("")# int类型无法填入
    df = df.replace({"": None})
    df = df.replace({np.nan: None})

    with pool.connection() as conn:
        with conn.cursor() as cu:
            """'更新数据库的内容"""
            sql = "update ssp_ext.amp_lesson_learn set\
              customerer=%s,model_name=%s,project_name=%s,product_categroy=%s,milage=%s,\
              failure_qty=%s,failure_date=%s,mfg_date=%s,failure_description=%s,rc_category=%s,\
              rc=%s,corrective_action=%s,implement_date=%s,standardization=%s,owner=%s,seq=%s\
              where id=%s"
            params = df[
                [
                    "customerer",
                    "model_name",
                    "project_name",
                    "product_categroy",
                    "milage",
                    "failure_qty",
                    "failure_date",
                    "mfg_date",
                    "failure_description",
                    "rc_category",
                    "rc",
                    "corrective_action",
                    "implement_date",
                    "standardization",
                    "owner",
                    "seq",
                    "id",
                ]
            ].values.tolist()
            cu.executemany(sql, params)
            conn.commit()

    return notice("更新成功", "success")


@callback(
    Output("global-notice", "children"),
    Output(id("table"), "data"),
    Input(id("delete_btn"), "n_clicks"),
    State(id("table"), "multiRowsClicked"),
    State(id("table"), "dataFiltered"),
    State(id("table"), "data"),
    State("user", "data"),
)
def tab3_submit(n_clicks, data, data_filtered, all_data, user):
    nt_name = user.get("nt_name").lower()
    if nt_name not in cfg.amp_lesson_admin:  # 不是管理员
        raise PreventUpdate
    if not n_clicks:
        raise PreventUpdate
    if not data:
        return notice("请先选择需删除的记录", "error")

    df = pd.DataFrame(data)  # 已经选中的df
    dff = pd.DataFrame(i for i in data_filtered["rows"] if i)
    df = df.loc[df["id"].isin(dff["id"])]
    df = df.replace({"": None})
    dfa = pd.DataFrame(all_data)  # 页面原先所有行
    dfa = dfa.loc[~dfa["id"].isin(df["id"])]  # 所有数据去除勾选的行

    with pool.connection() as conn:
        with conn.cursor() as cu:
            """'删除数据库的内容"""
            sql = "delete FROM ssp_ext.amp_lesson_learn where id=%s"
            params = df[["id"]].values.tolist()
            cu.executemany(sql, params)
            conn.commit()

    return notice("删除成功", "success"), dfa.to_dict(orient="records")


@callback(
    Output("global-notice", "children"),
    Output(id("amp_download"), "data"),
    Input(id("download_btn"), "n_clicks"),
    State(id("table"), "multiRowsClicked"),
    State(id("table"), "data"),
    State("user", "data"),
)
def dowload_data(n_clicks, selected_row, data, user):
    """下载"""
    nt_name = user.get("nt_name").lower()

    if nt_name not in cfg.amp_lesson_admin:  # 不是管理员
        raise PreventUpdate

    if not n_clicks:
        raise PreventUpdate

    if not selected_row:
        return notice("请先选择需要下载的数据", "error"), no_update

    df = pd.DataFrame(data)
    df = pd.DataFrame(selected_row)
    xlsx = dcc.send_data_frame(
        df.to_excel,
        f"amp_{datetime.now():%y%m%d%H%M%S}.xlsx",
        index=False,
    )
    return notice("下载成功", "success"), xlsx
