# -*- coding: utf-8 -*-
from dataclasses import dataclass
from datetime import datetime
from io import BytesIO

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import feffery_antd_components as fac
import feffery_utils_components as fuc
import numpy as np
import orjson
import pandas as pd
from dash import ctx, dcc, html, no_update
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import ALL, Input, Output, State, callback

from common import get_nt_name, id_factory, parse_search, read_sql
from components import notice, CeRejectAIO, CeCancelAIO
from config import UPLOAD_FOLDER_ROOT, cfg, IMG_TYPE

# from dbtool import db
from dash_iconify import DashIconify
import dash_ag_grid as dag
from tasks import bg_mail
from utils import db

id = id_factory(__name__)
js_close_window = """
    if (confirm("提交成功，关闭当前窗口?")) {close();}
    """


def task_information(task):
    return fac.AntdDescriptions(
        [
            fac.AntdDescriptionItem(task.get("dept"), label="部门"),
            fac.AntdDescriptionItem(task.get("applicant"), label="申请人"),
            fac.AntdDescriptionItem(task.get("status"), label="状态"),
        ],
        labelStyle={"fontWeight": "bold"},
        size="small",
    )


def form_part_1(task, fa):
    cc = task.get("cc").split(",")
    form_part_1 = [
        dmc.Center(dmc.Text("元件失效分析申请单", weight=700)),
        dmc.Divider(),
        dmc.Stack(
            [
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="主题(Purpose)",
                            withAsterisk=True,
                            size="xs",
                            id={"type": id("form1"), "index": "purpose"},
                            value=fa.get("purpose"),
                            # disabled=True,
                        ),
                        dmc.MultiSelect(
                            label="抄送(Cc)",
                            size="xs",
                            data=cc,
                            id=id("cc"),
                            value=cc,
                        ),
                    ],
                    grow=True,
                ),
                dmc.Group(
                    [
                        dmc.Select(
                            label="发生地点(Place of Occurrence)",
                            placeholder="Select one",
                            id={"type": id("form1"), "index": "place"},
                            data=[
                                {"value": "debug", "label": "Debug"},
                                {"value": "qe_test", "label": "QE_Test"},
                                {"value": "customer", "label": "Customer"},
                                {"value": "plant", "label": "Plant"},
                                {"value": "others", "label": "Others"},
                            ],
                            size="xs",
                            withAsterisk=True,
                            value=fa.get("place"),
                            # disabled=True,
                        ),
                        dmc.Select(
                            label="地区(Region)",
                            placeholder="Select one",
                            id={"type": id("form1"), "index": "region"},
                            data=[
                                {"value": "thailand", "label": "Thailand"},
                                {"value": "wuhu", "label": "Wuhu"},
                                {"value": "wujiang", "label": "WuJiang"},
                                {"value": "dongguan", "label": "Dongguan"},
                            ],
                            size="xs",
                            value=fa.get("region"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="客户(Customer)",
                            id={"type": id("form1"), "index": "customer"},
                            size="xs",
                            withAsterisk=True,
                            value=fa.get("customer"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                ),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="使用机种(Model)",
                            id={"type": id("form1"), "index": "model"},
                            size="xs",
                            withAsterisk=True,
                            value=fa.get("model"),
                            # disabled=True,
                        ),
                        dmc.Select(
                            label="机种阶段(Model Stage)",
                            placeholder="Select one",
                            id={"type": id("form1"), "index": "stage"},
                            data=[
                                {"value": "evt", "label": "EVT"},
                                {"value": "dvt", "label": "DVT"},
                                {"value": "pvt", "label": "PVT"},
                                {"value": "mp", "label": "MP"},
                            ],
                            size="xs",
                            value=fa.get("stage"),
                            # disabled=True,
                        ),
                        dmc.NumberInput(
                            label="不良数(Defectives)",
                            id={"type": id("form1"), "index": "defectives"},
                            size="xs",
                            withAsterisk=True,
                            min=1,
                            value=fa.get("defectives"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="不良率(Failure Rates)",
                            id={"type": id("form1"), "index": "failure_rates"},
                            size="xs",
                            withAsterisk=True,
                            value=fa.get("failure_rates"),
                            # disabled=True,
                        ),
                        dmc.Select(
                            label="是否有其他材料失效?(other failure?)",
                            id={"type": id("form1"), "index": "other_failure"},
                            size="xs",
                            value=fa.get("other_failure"),
                            data=["Yes", "No"],
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                ),
            ]
        ),
        dmc.TextInput(
            label="其他失效材料描述【内容需要描述：DeltaPN/厂商/厂商料号，失效现象简单描述】",
            id={"type": id("form1"), "index": "other_failure_description"},
            size="xs",
            value=fa.get("other_failure_description"),
            withAsterisk=True,
        ),
        dmc.Textarea(
            label="问题描述(Failure Description)",
            id={"type": id("form1"), "index": "failure_description"},
            autosize=True,
            size="xs",
            withAsterisk=True,
            value=fa.get("failure_description"),
            # disabled=True,
        ),
        dmc.Group(
            [
                dmc.TextInput(
                    label="位置号",
                    size="xs",
                    withAsterisk=True,
                    id={"type": id("form1"), "index": "designno"},
                    debounce=1500,
                    value=fa.get("designno"),
                    # disabled=True,
                ),
                dmc.TextInput(
                    label="料号",
                    size="xs",
                    withAsterisk=True,
                    id={"type": id("form1"), "index": "deltapn"},
                    debounce=1500,
                    value=fa.get("deltapn"),
                    # disabled=True,
                ),
                dmc.TextInput(
                    label="描述",
                    size="xs",
                    id={"type": id("form1"), "index": "des"},
                    value=fa.get("des"),
                    # disabled=True,
                ),
                dmc.TextInput(
                    label="厂商",
                    size="xs",
                    id={"type": id("form1"), "index": "mfgname"},
                    value=fa.get("mfgname"),
                    # disabled=True,
                ),
                dmc.TextInput(
                    label="厂商料号",
                    size="xs",
                    id={"type": id("form1"), "index": "mfgpn"},
                    debounce=1500,
                    value=fa.get("mfgpn"),
                    # disabled=True,
                ),
                dmc.Select(
                    label="材料类别1",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id={"type": id("form1"), "index": "cat1"},
                    data=[fa.get("cat1")],
                    value=fa.get("cat1"),
                    # disabled=True,
                ),
                dmc.Select(
                    label="材料类别2",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id={"type": id("form1"), "index": "cat2"},
                    value=fa.get("cat2"),
                    data=[fa.get("cat2")],
                    # disabled=True,
                ),
                dmc.Select(
                    label="材料类别3",
                    placeholder="Select one",
                    size="xs",
                    withAsterisk=True,
                    id={"type": id("form1"), "index": "cat3"},
                    value=fa.get("cat3"),
                    data=[fa.get("cat3")],
                    # disabled=True,
                ),
            ],
            spacing=0,
            align="end",
            grow=True,
        ),
    ]
    return dmc.Stack(form_part_1)


def form_part_2(task):
    form_part_2 = [
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("输入输出信息", size="xs", weight=700),
                        dmc.Text("output/input information", color="dimmed", size="xs"),
                    ],
                    # style={"background-color": "#f1f5f8"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="輸入電壓(input voltage AC/DC)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form2"), "index": "input_voltage"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("input_voltage"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="輸出電壓(output voltage)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form2"), "index": "output_voltage"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("output_voltage"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="輸出電流/功率(Output current/power)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form2"), "index": "output_power"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("output_power"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="工作頻率(operating frequency)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form2"), "index": "operating_frequency"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("operating_frequency"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
            # style={"width": "800px"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("失效材料所在功能", size="xs", weight=700),
                        dmc.Text(
                            "Function of the failed material", color="dimmed", size="xs"
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.Select(
                            label="位置固定/隨機(Fixed/random position)",
                            placeholder="Select one",
                            data=[
                                {
                                    "value": "fixed",
                                    "label": "Fixed",
                                },
                                {
                                    "value": "random",
                                    "label": "Random",
                                },
                            ],
                            # style={"width": 200},
                            size="xs",
                            withAsterisk=True,
                            id={"type": id("form2"), "index": "position"},
                            value=task.get("position"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="元件應用功能(Component application function)",
                            # style={"width": 225},
                            size="xs",
                            id={"type": id("form2"), "index": "component_function"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("component_function"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "失效條件(元件電壓/電流/Ramp time/测试时间\
                                 /溫度/濕度/震動/上電失效or測試多久失效等)",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Failure condition (component voltage/current /Ramp time\
                                /Test time/ temperature/humidity/vibration/power-on \
                                failure or test duration failure, etc.)",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Textarea(
                    label="失效前測試項目及條件(Failure test items and conditions)",
                    # style={"width": 225},
                    size="xs",
                    id={"type": id("form2"), "index": "failure_condition"},
                    # debounce=1500,
                    withAsterisk=True,
                    autosize=True,
                    value=task.get("failure_condition"),
                    # disabled=True,
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "外觀無burn mark或無法工作時,各pin阻抗對比",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Comparison of pin impedance when there is no burn \
                                mark in appearance or cannot work",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="失效樣品(Failure sample)",
                            size="xs",
                            id={"type": id("form2"), "index": "failure_sample"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("failure_sample"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="正常樣品(Normal sample)",
                            size="xs",
                            id={"type": id("form2"), "index": "normal_sample"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("normal_sample"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text("失效相關波形", size="xs", weight=700),
                        dmc.Text(
                            "Failure dependent waveform", color="dimmed", size="xs"
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="失效波形(Failure waveform)",
                            size="xs",
                            id={"type": id("form2"), "index": "failure_waveform"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("failure_waveform"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="正常波形(Normal waveform)",
                            size="xs",
                            id={"type": id("form2"), "index": "normal_waveform"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("normal_waveform"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
        dmc.Paper(
            [
                html.Div(
                    [
                        dmc.Text(
                            "交叉驗證結果(失效產品上失效材料A,正常產品上正常材料B)",
                            size="xs",
                            weight=700,
                        ),
                        dmc.Text(
                            "Cross validation results (Failed material A on failed \
                                product, normal material B on normal product)",
                            color="dimmed",
                            size="xs",
                        ),
                    ],
                    # style={"background-color": "azure"},
                ),
                dmc.Divider(),
                dmc.Group(
                    [
                        dmc.TextInput(
                            label="A->B測試結果(A->B test result)",
                            size="xs",
                            id={"type": id("form2"), "index": "a_b_test"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("a_b_test"),
                            # disabled=True,
                        ),
                        dmc.TextInput(
                            label="B->A測試結果(B->A test result)",
                            size="xs",
                            id={"type": id("form2"), "index": "b_a_test"},
                            debounce=1500,
                            withAsterisk=True,
                            value=task.get("b_a_test"),
                            # disabled=True,
                        ),
                    ],
                    grow=True,
                    # style={"backgroundColor": "white"},
                ),
                dmc.Space(h=10),
            ],
            withBorder=True,
            shadow="xs",
            p="xs",
            style={"background-color": "#f1f5f8"},
        ),
    ]
    return dmc.Stack(form_part_2)


def form_part_3(task):
    img_group = []
    dict1 = {
        "marking_picture": "材料Marking照片",
        "peripheral_circuit": "周边电路",
        "failure_waveform_pic": "失效波形",
        "normal_waveform_pic": "正常波形",
    }
    attach_group = []

    for i, j in dict1.items():
        if folder := task.get(i):
            src = [
                f"/upload/{folder}/{i.name}"
                for i in (UPLOAD_FOLDER_ROOT / folder).glob("*.*")
            ]

            div = fac.AntdRibbon(fac.AntdImage(src=src, height=100, width=100), text=j)
            img_group.append(div)

    if folder := task.get("attachment"):
        div = [
            dmc.Anchor(
                i.name,
                href=f"/upload/{folder}/{i.name}",
                target="_blank",
                variant="link",
            )
            for i in (UPLOAD_FOLDER_ROOT / folder).glob("*.*")
        ]
        attach_group.extend(div)

    # if folder := task.get("ce_attachment"):
    #     div = [
    #         dmc.Anchor(
    #             i.name,
    #             href=f"/upload/{folder}/{i.name}",
    #             target="_blank",
    #             variant="link",
    #         )
    #         for i in (UPLOAD_FOLDER_ROOT / folder).glob("*.*")
    #     ]
    #     attach_group.extend(div)

    output = dmc.Stack(
        [
            dmc.Paper(
                [
                    html.Div(
                        [
                            dmc.Text("照片", size="xs"),
                            dmc.Text("picture", color="dimmed", size="xs"),
                        ],
                    ),
                    dmc.Divider(),
                    dmc.Group(img_group),
                ],
                withBorder=True,
                style={"background-color": "#f1f5f8"},
            ),
            dmc.Paper(
                [
                    html.Div(
                        [
                            dmc.Text("附件", size="xs"),
                            dmc.Text("attachment", color="dimmed", size="xs"),
                        ],
                    ),
                    dmc.Divider(),
                    dmc.Group(attach_group),
                ],
                withBorder=True,
                style={"background-color": "#f1f5f8"},
            ),
        ]
    )
    return output


mail_modal = dmc.Modal(title="通知厂商做失效分析", centered=True, id=id("mail-modal"))


@dataclass
class Btndisplay:
    update: str = "none"
    close: str = "none"
    reopen: str = "none"
    approve: str = "none"
    reject: str = "none"
    cancel: str = "block"


def ce_form(user, task_id, task, fa):
    nt_name = user.get("nt_name").lower()
    role_group = user.get("role_group")

    status = task.get("status")
    btn_display = Btndisplay()

    action_display = "none"
    disabled_upload = True
    hide_8d = True

    if role_group == "CE":
        action_display = "flex"
        disabled_upload = False
        hide_8d = False

    sample_disabled = fa.get("receive_sample_time")
    report_disabled = fa.get("provide_report_time")
    start_date = task.get("start_date")

    ce_remark = fa.get("ce_remark")
    if ce_remark:
        ce_remark = orjson.loads(ce_remark)
        ce_remark.insert(
            0, {"time": f"{start_date:%Y-%m-%d %H:%M:%S}", "comment": "申请日期"}
        )

        time_line_chidren = []
        for item in ce_remark:
            time_line_item_children = []
            if comment := item.get("comment"):
                time_line_item_children.append(
                    dmc.Group(
                        [
                            dmc.Text(
                                "备注:",
                                color="orange",
                                size="xs",
                                underline=True,
                                weight=700,
                            ),
                            dmc.Text(comment, size="xs"),
                        ]
                    )
                )
            if attachment := item.get("attachment"):
                list1 = [
                    dmc.Text(
                        "附件:", color="green", size="xs", underline=True, weight=700
                    )
                ]
                list1.extend(
                    [
                        dmc.Anchor(
                            i.name,
                            href=f"/upload/{attachment}/{i.name}",
                            target="_blank",
                            variant="link",
                            size="xs",
                        )
                        for i in (UPLOAD_FOLDER_ROOT / attachment).glob("*.*")
                    ]
                )
                time_line_item_children.append(dmc.Group(list1))

            if item.get("sample"):
                time_line_item_children.append(
                    dmc.Text(
                        "厂商已经接到样品",
                        color="blue",
                        size="xs",
                        underline=True,
                        weight=700,
                    )
                )

            if item.get("report"):
                time_line_item_children.append(
                    dmc.Text(
                        "首次报告提供",
                        color="blue",
                        size="xs",
                        underline=True,
                        weight=700,
                    )
                )

            if time_line_item_children:
                time_line_chidren.append(
                    dmc.TimelineItem(
                        time_line_item_children,
                        title=dmc.Group(
                            [
                                dmc.Text(item.get("time"), color="dimmed", size="xs"),
                                dmc.Text(item.get("user"), color="dimmed", size="xs"),
                            ],
                            # color="dimmed",
                            # size="sm",
                        ),
                        # active=True,
                    )
                )

        ce_remark_timeline = dmc.Timeline(
            time_line_chidren,
            active=len(time_line_chidren),
            bulletSize=10,
            lineWidth=2,
        )
    else:
        ce_remark_timeline = dmc.Timeline()

    if status == "close":
        btn_display.reopen = "block"

    elif status == "approve":
        if nt_name in cfg.ce_admin:
            btn_display.approve = "block"
            btn_display.reject = "block"
    else:
        btn_display.update = "block"
        btn_display.close = "block"

    if fa.get("cat1") in ("Active", "Passive"):
        other_info_open = True
    else:
        other_info_open = False

    conclusion = fa.get("conclusion", "")
    sql = "select a.*,b.dept,b.start_date from ce.fa a \
        left join (select id,dept,start_date from ce.task)b \
            on a.task_id=b.id where a.deltapn=%s"
    df = read_sql(sql, params=[task.get("deltapn")])
    df = df.loc[df["task_id"] != int(task_id)]
    df["task_id"] = df["task_id"].apply(lambda x: f"**[点击查看](/ce/fa/ce?task={x})**")

    fl1 = [
        {
            "name": j.name,
            "url": f"/upload/burn_mark_{task_id}/{j.name}",
            "status": "done",
        }
        for j in (UPLOAD_FOLDER_ROOT / f"burn_mark_{task_id}").glob("*")
    ]

    fl2 = [
        {
            "name": j.name,
            "url": f"/upload/final_report_{task_id}/{j.name}",
            "status": "done",
        }
        for j in (UPLOAD_FOLDER_ROOT / f"final_report_{task_id}").glob("*")
    ]

    fl3 = [
        {
            "name": j.name,
            "url": f"/upload/fa_ce_attachment_{task_id}/{j.name}",
            "status": "done",
        }
        for j in (UPLOAD_FOLDER_ROOT / f"fa_ce_attachment_{task_id}").glob("*")
    ]

    issue = (
        df["conclusion"]
        .value_counts()
        .reindex(["Quality Issue", "Design Issue", "EOS", "Other"])
        .fillna(0)
        .reset_index()
    )
    issue["color"] = ["red", "blue", "green", "orange"]

    ce_form = dmc.Stack(
        [
            dmc.Group(
                [
                    task_information(task),
                    dmc.Button(
                        "下载",
                        variant="subtle",
                        color="orange",
                        size="xs",
                        id=id("download-btn"),
                    ),
                    dcc.Download(id=id("download")),
                    dmc.Button(
                        "通知厂商", variant="subtle", size="xs", id=id("mail-btn")
                    ),
                ],
                position="right",
                noWrap=True,
            ),
            form_part_1(task, fa),
            dbc.Collapse(form_part_2(fa), id=id("other-info"), is_open=other_info_open),
            form_part_3(fa),
            ce_remark_timeline,
            dmc.RadioGroup(
                [
                    dmc.Radio(label="一般", value="一般", color="blue"),
                    dmc.Radio(label="紧急", value="紧急", color="red"),
                ],
                value=task.get("urgent"),
                label="问题等级(Severity Level)",
                size="sm",
                id=id("severity-level"),
            ),
            dmc.Textarea(
                label="CE备注",
                autosize=True,
                withAsterisk=True,
                id=id("ce-comment"),
            ),
            dmc.Group(
                [
                    dmc.CheckboxGroup(
                        [
                            dmc.Checkbox(
                                label="厂商已经接到样品",
                                value="sample",
                                disabled=sample_disabled,
                            ),
                            dmc.Checkbox(
                                label="首次报告提供",
                                value="report",
                                disabled=report_disabled,
                            ),
                        ],
                        id=id("checkbox"),
                        value=[],
                        orientation="horizontal",
                        label="其他",
                    ),
                ],
                spacing=100,
            ),
            dmc.RadioGroup(
                [
                    dmc.Radio(label="Quality Issue", value="Quality Issue"),
                    dmc.Radio(label="Design Issue", value="Design Issue"),
                    dmc.Radio(label="EOS", value="EOS"),
                    dmc.Radio(label="Other", value="Other"),
                ],
                id=id("conclusion"),
                orientation="horizontal",
                label="结论",
                value=conclusion,
            ),
            html.Div(
                [
                    dmc.Stack(
                        [
                            dmc.TextInput(
                                label="Process",
                                size="xs",
                                id=id("process"),
                                required=True,
                                value=fa.get("process"),
                            ),
                            dmc.TextInput(
                                label="物性表现/器件电性表现",
                                size="xs",
                                id=id("physical-property"),
                                required=True,
                                value=fa.get("physical_property"),
                            ),
                            dmc.TextInput(
                                label="module表现",
                                size="xs",
                                id=id("module-property"),
                                value=fa.get("module_property"),
                            ),
                        ],
                        id=id("quality-issue"),
                        display="none",
                    ),
                    dmc.TextInput(
                        label="简单概括design issue",
                        size="xs",
                        id=id("design-issue"),
                        required=True,
                        display="none",
                        value=fa.get("design_issue"),
                    ),
                    dmc.RadioGroup(
                        [
                            dmc.Radio(label="OC", value="OC"),
                            dmc.Radio(label="OV", value="OV"),
                            dmc.Radio(label="ESD", value="ESD"),
                            dmc.Radio(label="Other", value="Other"),
                        ],
                        id=id("eos"),
                        orientation="horizontal",
                        display="none",
                        value=fa.get("eos") or "",
                    ),
                    dmc.TextInput(
                        label="Other",
                        size="xs",
                        id=id("other"),
                        required=True,
                        display="none",
                        value=fa.get("other"),
                    ),
                ],
            ),
            fac.AntdPictureUpload(
                apiUrl="/upload/",
                buttonContent="上传BurnMark照片",
                id=id("burn-mark"),
                uploadId=f"burn_mark_{task_id}",
                defaultFileList=fl1,
                disabled=disabled_upload,
                fileTypes=IMG_TYPE,
            ),
            fac.AntdDraggerUpload(
                apiUrl="/upload/",
                text="上传附件",
                id=id("ce-attachment"),
                lastUploadTaskRecord={},
                uploadId=f"fa_ce_attachment_{task_id}",
                defaultFileList=fl3,
                disabled=disabled_upload,
            ),
            fac.AntdDraggerUpload(
                apiUrl="/upload/",
                text="上传8D Report",
                id=id("final-report"),
                lastUploadTaskRecord={},
                uploadId=f"final_report_{task_id}",
                defaultFileList=fl2,
                disabled=disabled_upload,
            ),
            dmc.Text("历史问题", size="xs"),
            dmc.Group(
                [
                    fac.AntdBadge(
                        i.index,
                        count=i.conclusion,
                        color=i.color,
                        offset=[15, 0],
                        # showZero=True,
                    )
                    for i in issue.itertuples()
                ],
                position="center",
                spacing=80,
            ),
            fuc.FefferyResizable(
                dag.AgGrid(
                    className="ag-theme-quartz",
                    columnDefs=[
                        {"field": "start_date", "headerName": "申请日期"},
                        {"field": "dept", "headerName": "申请部门"},
                        {"field": "conclusion", "headerName": "结论"},
                        {
                            "field": "task_id",
                            "headerName": "8D报告",
                            "cellRenderer": "markdown",
                            "linkTarget": "_blank",
                            "hide": hide_8d,
                        },
                    ],
                    rowData=df.to_dict(orient="records"),
                    # columnSize="autoSize",
                    defaultColDef={
                        "resizable": True,
                        "sortable": True,
                        "filter": True,
                        "wrapHeaderText": True,
                        "autoHeaderHeight": True,
                    },
                    dashGridOptions={
                        "rowSelection": "single",
                        "stopEditingWhenCellsLoseFocus": True,
                        "singleClickEdit": True,
                        "rowHeight": 35,
                        "enableCellTextSelection": True,
                        # "ensureDomOrder": True,
                    },
                    style={"height": "300px"},
                )
            ),
            dmc.Divider(),
            dmc.Group(
                [
                    dmc.Button(
                        "更新",
                        color="blue",
                        id=id("ce-update-submit"),
                        style={"display": btn_display.update},
                    ),
                    dmc.Button(
                        "结案",
                        color="green",
                        id=id("ce-close-submit"),
                        style={"display": btn_display.close},
                    ),
                    dmc.Button(
                        "再开案",
                        color="orange",
                        id=id("ce-reopen-submit"),
                        style={"display": btn_display.reopen},
                    ),
                    dmc.Button(
                        "核准",
                        color="green",
                        id=id("ce-approve-submit"),
                        style={"display": btn_display.approve},
                    ),
                    dmc.Button(
                        "主管驳回",
                        color="pink",
                        id=id("ce-reject-submit"),
                        style={"display": btn_display.reject},
                    ),
                    CeRejectAIO(__name__),
                    CeCancelAIO(__name__),
                    dmc.Button(
                        "添加提醒",
                        # color="yellow",
                        leftIcon=DashIconify(icon="mdi:add-alert", width=15),
                        variant="subtle",
                        id=id("clock"),
                    ),
                ],
                # grow=True,
                display=action_display,
                position="apart",
                spacing=20,
            ),
            dmc.Space(h=10),
            mail_modal,
            dmc.Modal(
                dmc.Stack(
                    [
                        dmc.Text("新增提醒", size="sm", weight=700),
                        dmc.Group(
                            [
                                fac.AntdInput(
                                    addonBefore="提醒内容",
                                    id=id("clock-content"),
                                    autoSize=True,
                                    style={"width": 300},
                                ),
                                fac.AntdDatePicker(id("clock-date")),
                                fac.AntdButton(
                                    id=id("add-clock"),
                                    type="dashed",
                                    icon=fac.AntdIcon(
                                        icon="fc-plus", style={"fontSize": "28px"}
                                    ),
                                ),
                            ],
                            # align="end",
                            spacing=0,
                        ),
                        dmc.Divider(),
                        dmc.Text("历史提醒", size="sm", weight=700),
                        dcc.Store(id=id("history"), storage_type="local", data=[]),
                        fac.AntdTimeline(
                            items=[
                                {
                                    "content": fac.AntdCountdown(
                                        title="提醒1",
                                        value="2024-05-19 00:00:00",
                                        format="还剩Y年M月D天H小时m分s秒",
                                        valueStyle={"fontSize": "12px"},
                                    )
                                },
                                {
                                    "content": fac.AntdCountdown(
                                        title="提醒2",
                                        value="2024-06-11 00:00:00",
                                        format="还剩Y年M月D天H小时m分s秒",
                                        valueStyle={"fontSize": "12px"},
                                    )
                                },
                            ],
                            id=id("clock-list"),
                        ),
                    ]
                ),
                title="失效分析提醒",
                id=id("clock-modal"),
                centered=True,
                size="lg",
            ),
            fuc.FefferyExecuteJs(id=id("js")),
        ]
    )
    return ce_form


def layout(user, **query):
    task_id = query.get("task")
    if not task_id:
        return dmc.Container()

    task = db.find_one("ce.task", {"id": task_id})
    fa = db.find_one("ce.fa", {"task_id": task_id})
    output = dmc.Container(ce_form(user, task_id, task, fa))
    return output


@callback(
    Output(id("quality-issue"), "display"),
    Output(id("design-issue"), "display"),
    Output(id("eos"), "display"),
    Output(id("other"), "display"),
    Input(id("conclusion"), "value"),
    prevent_initial_call=False,
)
def conclusion_content_display(value):
    if value == "Quality Issue":
        return "block", "none", "none", "none"
    elif value == "Design Issue":
        return "none", "block", "none", "none"
    elif value == "EOS":
        return "none", "none", "block", "none"
    elif value == "Other":
        return "none", "none", "none", "block"
    else:
        raise PreventUpdate


@callback(
    Output(id("clock-modal"), "opened"),
    Input(id("clock"), "n_clicks"),
)
def open_clock_modal(n_clicks):
    if not n_clicks:
        raise PreventUpdate
    return True


@callback(
    Output("global-notice", "children"),
    Output(id("history"), "data"),
    Input(id("add-clock"), "nClicks"),
    State(id("history"), "data"),
    State(id("clock-date"), "value"),
    State(id("clock-content"), "value"),
    State("user", "data"),
)
def add_to_history_store(n_clicks, data: list, date, content, user):
    if not n_clicks:
        raise PreventUpdate
    if not date:
        return notice("填写提醒日期", "error"), no_update
    if not content:
        return notice("填写提醒内容", "error"), no_update
    date = pd.Timestamp(date) + pd.Timedelta(hours=9)
    data.insert(
        0,
        {
            "content": fac.AntdCountdown(
                title=content,
                value=f"{date}",
                format="还剩Y年M月D天H小时m分s秒",
                valueStyle={"fontSize": "12px"},
            )
        },
    )
    nt_name = user.get("nt_name")
    to = f"{nt_name}@deltaww.com"

    bg_mail.schedule(args=(to, f"【FA提醒】{content}"), eta=date)

    return notice("添加提醒成功"), data


@callback(
    Output(id("clock-list"), "items"),
    Input(id("history"), "data"),
)
def store_to_clock_list_items(data):
    if not data:
        raise PreventUpdate
    return data


@callback(
    Output("global-notice", "children"),
    Output(id("ce-update-submit"), "disabled"),
    Output(id("ce-close-submit"), "disabled"),
    Output(id("ce-reopen-submit"), "disabled"),
    Output(id("ce-approve-submit"), "disabled"),
    Output(id("ce-reject-submit"), "disabled"),
    Output(id("js"), "jsString"),
    Input(id("ce-update-submit"), "n_clicks"),
    Input(id("ce-close-submit"), "n_clicks"),
    Input(id("ce-reopen-submit"), "n_clicks"),
    Input(id("ce-approve-submit"), "n_clicks"),
    Input(id("ce-reject-submit"), "n_clicks"),
    State(id("severity-level"), "value"),
    State(id("ce-comment"), "value"),
    State(id("checkbox"), "value"),
    State(id("conclusion"), "value"),
    State(id("process"), "value"),
    State(id("physical-property"), "value"),
    State(id("module-property"), "value"),
    State(id("design-issue"), "value"),
    State(id("eos"), "value"),
    State(id("other"), "value"),
    State("url", "search"),
    State(id("ce-attachment"), "lastUploadTaskRecord"),
    State({"type": id("form1"), "index": ALL}, "value"),
    State({"type": id("form2"), "index": ALL}, "value"),
)
def ce_update_submit(
    n1,
    n2,
    n3,
    n4,
    n5,
    urgent,
    comment,
    checkbox,
    conclusion,
    process,
    physical_property,
    module_property,
    design_issue,
    eos,
    other,
    search,
    attachment,
    form1,
    form2,
):
    if not any([n1, n2, n3, n4, n5]):
        raise PreventUpdate

    url = parse_search(search)
    task_id = url.get("task")

    fa = db.find_one("ce.fa", {"task_id": task_id})
    ce_remark = orjson.loads(fa.get("ce_remark") or "[]")

    now = f"{datetime.now():%Y-%m-%d %H:%M:%S}"
    dd = {"time": now, "user": get_nt_name()}

    if comment:
        dd.update({"comment": comment})
    if attachment:
        dd.update({"attachment": attachment.get("taskId")})

    form1 = {i.get("id").get("index"): i.get("value") for i in ctx.states_list[-2]}
    form2 = {i.get("id").get("index"): i.get("value") for i in ctx.states_list[-1]}
    data = {"id": fa.get("id")} | form1 | form2

    if "sample" in checkbox:
        dd.update({"sample": now})
        if not fa.get("receive_sample_time"):
            data.update({"receive_sample_time": now})

    if "report" in checkbox:
        dd.update({"report": now})
        if not fa.get("provide_report_time"):
            data.update({"provide_report_time": now})

    ce_remark.append(dd)
    data.update({"ce_remark": orjson.dumps(ce_remark)})

    if conclusion:
        data.update({"conclusion": conclusion})
        if conclusion == "Quality Issue":
            if not process or not physical_property:
                return (notice("Process和物性表现必填", "error"), *[no_update] * 6)
            data.update(
                {
                    "process": process,
                    "physical_property": physical_property,
                    "module_property": module_property,
                }
            )
        elif conclusion == "Design Issue":
            data.update({"design_issue": design_issue})
        elif conclusion == "EOS":
            data.update({"eos": eos})
        elif conclusion == "Other":
            data.update({"other": other})

    if ctx.triggered_id == id("ce-update-submit"):
        db.update("ce.fa", data)
        db.update("ce.task", {"id": task_id, "status": "ongoing", "urgent": urgent})

    elif ctx.triggered_id == id("ce-close-submit"):
        fp = UPLOAD_FOLDER_ROOT / f"burn_mark_{task_id}"
        if not fp.exists():
            return (notice("请上传Burn Mark", "error"), *[no_update] * 6)

        fp = UPLOAD_FOLDER_ROOT / f"final_report_{task_id}"
        if not fp.exists():
            return (notice("请上传8D Report", "error"), *[no_update] * 6)

        if not conclusion:
            return (notice("请选择结论", "error"), *[no_update] * 6)

        task_data = {"id": task_id, "urgent": urgent}
        if urgent == "紧急":
            status = "approve"
            task_data.update({"status": status})
        else:
            status = "close"
            task_data.update({"status": status, "end_date": now})

        db.update("ce.fa", data)
        db.update("ce.task", task_data)

    elif ctx.triggered_id == id("ce-reopen-submit"):
        data.update({"conclusion": None})
        db.update("ce.fa", data)
        db.update("ce.task", {"id": task_id, "status": "open", "urgent": urgent})

    elif ctx.triggered_id == id("ce-approve-submit"):
        db.update("ce.fa", data)
        db.update("ce.task", {"id": task_id, "status": "close", "urgent": urgent})

    elif ctx.triggered_id == id("ce-reject-submit"):
        if not comment:
            return (
                notice("请填写退件原因", "error"),
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
            )
        db.update("ce.fa", data)
        db.update(
            "ce.task", {"id": task_id, "status": "ongoing", "ce_comment": comment}
        )
    return notice(), True, True, True, True, True, js_close_window


@callback(
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State("url", "search"),
)
def download_fa_data(n_clicks, search):
    if not n_clicks:
        raise PreventUpdate

    url = parse_search(search)
    task_id = url.get("task")

    task = db.execute("select * from ce.task where id = %s", (task_id,))
    df0 = pd.DataFrame(task)
    df0 = df0.reindex(columns=["type", "dept", "applicant", "status"])

    fa = db.execute("select * from ce.fa where task_id = %s", (task_id,))
    df = pd.DataFrame(fa)
    df = pd.concat([df0, df], axis=1)

    df = df.fillna("")
    for col in [
        "attachment",
        "marking_picture",
        "peripheral_circuit",
        "failure_waveform_pic",
        "normal_waveform_pic",
    ]:
        df[col] = np.where(
            df[col] == "",
            df[col],
            UPLOAD_FOLDER_ROOT.as_uri() + "/" + df[col],
        )

    df = df.reindex(
        columns=df.columns.difference(
            ["id", "task_id", "create_time", "update_time"], sort=False
        )
    )
    col1 = db.execute("SHOW FULL COLUMNS FROM ce.task")
    col1 = {i.get("field"): i.get("comment") for i in col1}
    col2 = db.execute("SHOW FULL COLUMNS FROM ce.fa")
    col2 = {i.get("field"): i.get("comment") for i in col2}
    col1.update(col2)
    df = df.rename(columns=col1)

    bio = BytesIO()
    with pd.ExcelWriter(bio, engine="xlsxwriter") as writer:
        dft = df.T

        ce_remark = dft.loc["CE备注", 0]
        if ce_remark:
            dfc = pd.read_json(ce_remark)
            dfc["attachment"] = np.where(
                dfc["attachment"].isna(),
                dfc["attachment"],
                UPLOAD_FOLDER_ROOT.as_uri() + "/" + dfc["attachment"],
            )
            dft = dft.drop("CE备注", axis=0)
            dfc = dfc.T.unstack().droplevel(0).reset_index()
            dft = pd.concat([dft, dfc], axis=0, ignore_index=True)

        dft = dft.reset_index()
        dft.to_excel(writer, sheet_name="Sheet1", header=False, index=False)
        workbook = writer.book
        worksheet = writer.sheets["Sheet1"]
        format = workbook.add_format({"font_name": "Arial"})
        format.set_align("left")

        worksheet.set_column("A:A", 50, format)
        worksheet.set_column("B:B", 100, format)
        writer.save()
        bio.seek(0)
        workbook = bio.read()

    return dcc.send_bytes(workbook, f"FA_{task_id}.xlsx")


@callback(
    Input(id("burn-mark"), "lastUploadTaskRecord"),
)
def upload_burn_mark(upload):
    if not upload:
        raise PreventUpdate
    task_id = upload.get("taskId")
    file_name = upload.get("fileName")
    original_file = UPLOAD_FOLDER_ROOT / task_id / file_name
    new_file = original_file.with_stem("burn_mark")
    if new_file.exists():
        new_file.unlink()
    original_file.rename(new_file)

    fa = db.find_one("ce.fa", {"task_id": task_id.rsplit("_")[-1]})
    db.update("ce.fa", {"id": fa.get("id"), "burn_mark": f"{task_id}/{new_file.name}"})


@callback(
    Input(id("final-report"), "lastUploadTaskRecord"),
)
def upload_final_report(upload):
    if not upload:
        raise PreventUpdate
    task_id = upload.get("taskId")
    file_name = upload.get("fileName")
    original_file = UPLOAD_FOLDER_ROOT / task_id / file_name
    new_file = original_file.with_stem("final_report")
    if new_file.exists():
        new_file.unlink()
    original_file.rename(new_file)

    fa = db.find_one("ce.fa", {"task_id": task_id.rsplit("_")[-1]})
    db.update(
        "ce.fa", {"id": fa.get("id"), "final_report": f"{task_id}/{new_file.name}"}
    )
