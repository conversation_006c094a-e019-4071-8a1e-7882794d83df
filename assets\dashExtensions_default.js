window.dashExtensions = Object.assign({}, window.dashExtensions, {
    default: {
        function0: function(e, cell) {
            window.alert("Printing row data for: ")
        },
        function1: function(cell) {
            var data = cell.getRow().getData();
            return data.status == undefined || data.status === "open";
        },
        function2: function(term, values) {
                var matches = [];
                window.alert(123);
                values.forEach(function(item) {
                    if (item.value === term) {
                        matches.push(item);
                    }
                });

                return matches;
            }

            ,
        function3: function(cell) {
                cell.getElement().style.backgroundColor = "#84e6ac";
            }

            ,
        function4: function(cell, formatterParams, onRendered) {
            let v = cell.getValue();
            v = v * 100;
            return v.toFixed(0) + '%'
        }

    }
});