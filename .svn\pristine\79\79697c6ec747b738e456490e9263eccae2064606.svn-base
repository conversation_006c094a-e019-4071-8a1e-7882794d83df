# -*- coding: utf-8 -*-
import pandas as pd
from dash.exceptions import PreventUpdate
from dash_extensions.enrich import (
    Input,
    Output,
    State,
    callback,
    no_update,
    dcc,
    clientside_callback,
)
from common import read_sql, parse_search
from components.notice import notice
from config import pool
import numpy as np
import dash
from . import layout
from dash import Patch
from urllib.parse import parse_qsl, urlsplit

id = layout.id
layout = layout.layout
dash.register_page(__name__, path="/ce/survey/ce", title="材料调查-CE")

survey_db = {
    "life_time": ["life", "tct", "y_n"],
    "operation_temp": ["ta_min", "ta_max"],
    "flammability": ["flammability"],
    "auto_motive": [
        "device_sop_date",
        "device_total_volume",
        "process_sop_date",
        "process_total_volume",
    ],
}


@callback(
    Output("global-notice", "children"),
    Output(id("submit"), "disabled"),
    Input(id("submit"), "n_clicks"),
    State(id("table"), "rowData"),
    State(id("table"), "columnState"),
    State(id("survey-items"), "value"),
    State("url", "search"),
    State(id("update_rows"), "data"),
)
def update(n_clicks, data, columns, items, search, rows):
    if not n_clicks:
        raise PreventUpdate
    url = parse_search(search)
    task_id = url.get("task")

    df = pd.DataFrame(data)
    columns = [i["colId"] for i in columns]
    df = df.reindex(columns=columns)
    df = df.loc[df.index.isin(rows)]
    if df.empty:
        raise PreventUpdate

    col1 = ["deltapn", "des", "mfgname", "mfgpn"]
    if (df[df.columns.difference(col1)].fillna("") == "").any().any():
        status = "ongoing"
    else:
        status = "close"

    conn = pool.connection()
    cu = conn.cursor()
    sql = "update ce.task set status=%s where id=%s and sub_type!=%s"
    cu.execute(sql, [status, task_id, "汇总"])

    for item in items:
        col2 = survey_db.get(item)
        # breakpoint()
        dfi = df.loc[df[col2].notna().all(axis=1)]
        if not dfi.empty:
            dfi = dfi.reindex(columns=["deltapn", "des", "mfgname", "mfgpn"] + col2)

            fields = ",".join(dfi.columns)
            ph = ",".join(["%s"] * dfi.columns.size)
            ph2 = ",".join(f"{i}=%s" for i in col2)
            # sql = f"replace into ce.{item}({fields}) values({ph})"
            # cu.executemany(sql, params)
            sql = f"insert into ce.{item}({fields}) values({ph}) ON DUPLICATE KEY UPDATE {ph2}"
            dfx = pd.concat([dfi, dfi[col2]], axis=1)
            dfx = dfx.replace({np.nan: None})
            params = dfx.values.tolist()
            for item in params:
                cu.execute(sql, item)
    conn.commit()
    cu.close()
    conn.close()
    return notice(), True


@callback(
    Output(id("download"), "data"),
    Input(id("download-btn"), "n_clicks"),
    State(id("model_id"), "data"),
    State(id("survey-items"), "value"),
    State(id("model"), "value"),
)
def export_data_as_excel(n_clicks, model_id, items, model):
    if not n_clicks:
        raise PreventUpdate

    sql = f"select * from \
        (select deltapn, des, mfgname, mfgpn,source,designno,remark \
            from ce.survey where model_id=%s) as a"
    for item in items:
        fileds = [f"deltapn as deltapn_{item}"] + survey_db.get(item)
        fileds = ",".join(fileds)
        sql += f" left join (select {fileds} from ce.{item}) as {item} on a.deltapn={item}.deltapn_{item} "
    df = read_sql(sql, params=[model_id])
    df.drop([f"deltapn_{i}" for i in items], axis=1, inplace=True)
    return dcc.send_data_frame(
        df.to_excel, f"{model}_survey.xlsx", sheet_name=f"{model}", index=False
    )


@callback(
    Output("global-notice", "children"),
    Output(id("close"), "disabled"),
    Input(id("close"), "n_clicks"),
    State(id("unfinished"), "children"),
    State("url", "search"),
)
def close(n_clicks, unfinished, search):
    if not n_clicks:
        raise PreventUpdate

    unfinished = int(unfinished.split(":")[-1])
    if unfinished > 0:
        return notice("还有未完成的调查,无法结案", "error"), no_update

    url = dict(parse_qsl(urlsplit(search).query))
    task_id = url.get("task")
    sql = "update ce.task set status=%s where id=%s"
    params = ("close", task_id)
    conn = pool.connection()
    cu = conn.cursor()
    cu.execute(sql, params)
    conn.commit()
    cu.close()
    conn.close()

    return notice("结案成功"), True


@callback(
    Output(id("update_rows"), "data"),
    Input(id("table"), "cellValueChanged"),
)
def cell_changed_mark_selected(changed):
    if not changed:
        raise PreventUpdate
    data = Patch()
    # print(changed)
    data.append(int(changed[0].get("rowId")))
    return data


clientside_callback(
    """function () {
        if (confirm("提交成功，关闭当前窗口?")) {close();}
    }""",
    Input(id("close"), "disabled"),
    Input(id("submit"), "disabled"),
)
